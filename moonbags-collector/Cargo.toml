[package]
name = "moonbags-collector"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = { version = "1.0" }
clap = { version = "4.5", features = ["derive", "env"] }
dotenv = { version = "0.15" }
tokio = { version = "1.43", features = ["full"] }
tracing = { version = "0.1" }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
serde = { version = "1.0", features = ["derive"] }
strum_macros = { version = "0.27" }
strum = { version = "0.27" }
serde_json = { version = "1.0" }
mongodb = { version = "2.8", features = ["tracing-unstable"] }
bson = { version = "2", features = ["chrono-0_4"] }
chrono = { version = "0.4" }
futures = { version = "0.3" }
async-trait = { version = "0.1" }
sui_sdk = { git = "https://github.com/mystenlabs/sui", package = "sui-sdk", tag = "mainnet-v1.42.2" }
sui-keys = { git = "https://github.com/mystenlabs/sui", package = "sui-keys", tag = "mainnet-v1.42.2" }
shared-crypto = { git = "https://github.com/mystenlabs/sui", package = "shared-crypto", tag = "mainnet-v1.42.2" }
move-core-types = { git = "https://github.com/mystenlabs/sui", package = "move-core-types", tag = "mainnet-v1.42.2" }
reqwest = { version = "0.12.4", features = ["json"] }
# redis as pool
bb8 = "0.9"
bb8-redis = "0.18"
redis = "0.27"
cached = { version = "0.54", features = ["async"] }
socketio-rust-emitter = { git = "https://github.com/epli2/socketio-rust-emitter.git" }
rust_decimal = { version = "1.36", features = ["maths"] }
rust_decimal_macros = "1.36"
serde_with = "3.12.0"
rdkafka = { version = "0.36.2", features = ["ssl", "sasl"] }
sha2 = "0.10.9"
nanoid = "0.4"
slugify = "0.1.0"
