use bson::Decimal128;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct StakingUsers {
    pub staking_pool_address: String,
    pub staking_coin_address: String,
    pub user_address: String,
    pub staked_amount: Decimal128,
    pub account_object_id: String,
    pub reward_claimed: Option<Decimal128>,
    pub last_stake: u64,
}
