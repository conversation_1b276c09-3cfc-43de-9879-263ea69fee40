use bson::Decimal128;
use serde::{Deserialize, Serialize};
use std::fmt::Debug;

#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Candle {
    pub low: Decimal128,
    pub high: Decimal128,
    pub open: Decimal128,
    pub close: Decimal128,
}

#[derive(Serialize, Debug, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Candles {
    pub token_address: String,
    pub timestamp: u64,
    pub resolution: u32,
    pub price: Candle,
    pub price_usd: Candle,
    pub market_cap: Candle,
    pub market_cap_usd: Candle,
    pub volume_base: String,
    pub volume_quote: String,
    pub volume_usd: String,
}
