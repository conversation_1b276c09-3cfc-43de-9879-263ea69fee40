use bson::doc;
use mongodb::options::ClientOptions;
use mongodb::{Client, Database};
use serde::{Deserialize, Serialize};
use tokio::sync::OnceCell;

pub static MONGODB: OnceCell<Database> = OnceCell::const_new();

pub async fn setup_mongodb(uri: &str) -> &'static Database {
    MONGODB
        .get_or_init(|| async {
            let client_options = ClientOptions::parse(uri).await.expect("Invalid uri");
            let database_name = client_options
                .default_database
                .as_ref()
                .expect("Database name must be included in MongoDB URI")
                .to_string();
            let client = Client::with_options(client_options).expect("Cannot init client");
            let database_instance = client.database(&database_name);
            database_instance
                .run_command(doc! { "ping": 1 }, None)
                .await
                .expect("Cannot db ping");
            database_instance
        })
        .await
}

pub fn get_mongodb_instance() -> &'static Database {
    MONGODB.get().expect("MongoDB is not initialized")
}

#[derive(Debug, Serialize, Deserialize, strum_macros::Display)]
#[serde(rename_all = "snake_case")]
#[strum(serialize_all = "snake_case")]
pub enum EDatabase {
    Trades,
    Candles,
    Holders,
    Coins,
    TokenPrices,
    LatestSignatures,
    StakingEvents,
    StakingUsers,
    CoinLock,
}
