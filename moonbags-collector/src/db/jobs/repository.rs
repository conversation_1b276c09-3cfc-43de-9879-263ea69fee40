use super::{Job, JobStatus, JobType};
use crate::db::utils::traits::RepositorySharedMethod;
use bson::{doc, DateTime};
use chrono::Utc;
use mongodb::{Collection, Database};

#[derive(Debug, Clone)]
pub struct JobsRepository {
    collection: Collection<Job>,
}

impl RepositorySharedMethod for JobsRepository {
    type Schema = Job;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection("jobs"),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl JobsRepository {
    pub async fn create_deploy_pool_job(
        &self,
        token_address: String,
        sui_amount: u64,
        token_amount: u64,
    ) -> anyhow::Result<bson::oid::ObjectId> {
        let now = DateTime::from_millis(Utc::now().timestamp_millis());
        let job = Job {
            id: None,
            job_type: JobType::DeployPoolAndAddLiquidity,
            status: JobStatus::Pending,
            token_address,
            sui_amount,
            token_amount,
            pool_id: None,
            nft_object_id: None,
            is_sui_coin_first: None,
            tx_hash: None,
            error_message: None,
            created_at: now,
            updated_at: now,
        };

        let result = self.insert_one(&job, None).await?;
        Ok(result.inserted_id.as_object_id().unwrap())
    }

    pub async fn create_add_job(
        &self,
        token_address: String,
        pool_id: String,
        sui_amount: u64,
        token_amount: u64,
        is_sui_coin_first: bool,
    ) -> anyhow::Result<bson::oid::ObjectId> {
        let now = DateTime::from_millis(Utc::now().timestamp_millis());
        let job = Job {
            id: None,
            job_type: JobType::AddLiquidity,
            status: JobStatus::Pending,
            token_address,
            sui_amount,
            token_amount,
            pool_id: Some(pool_id),
            nft_object_id: None,
            is_sui_coin_first: Some(is_sui_coin_first),
            tx_hash: None,
            error_message: None,
            created_at: now,
            updated_at: now,
        };

        let result = self.insert_one(&job, None).await?;
        Ok(result.inserted_id.as_object_id().unwrap())
    }

    pub async fn create_burn_position_job(
        &self,
        token_address: String,
        pool_id: String,
        nft_object_id: String,
        is_sui_coin_first: bool,
    ) -> anyhow::Result<bson::oid::ObjectId> {
        let now = DateTime::from_millis(Utc::now().timestamp_millis());
        let job = Job {
            id: None,
            job_type: JobType::BurnPosition,
            status: JobStatus::Pending,
            token_address,
            sui_amount: 0,
            token_amount: 0,
            pool_id: Some(pool_id),
            nft_object_id: Some(nft_object_id),
            is_sui_coin_first: Some(is_sui_coin_first),
            tx_hash: None,
            error_message: None,
            created_at: now,
            updated_at: now,
        };

        let result = self.insert_one(&job, None).await?;
        Ok(result.inserted_id.as_object_id().unwrap())
    }

    pub async fn update_job_status(
        &self,
        job_id: bson::oid::ObjectId,
        status: JobStatus,
        tx_hash: Option<String>,
        error_message: Option<String>,
    ) -> anyhow::Result<()> {
        let now = DateTime::from_millis(Utc::now().timestamp_millis());

        let mut update = doc! {
            "$set": {
                "status": bson::to_bson(&status).unwrap(),
                "updated_at": now,
            }
        };

        if let Some(tx) = tx_hash.clone() {
            update = doc! {
                "$set": {
                    "status": bson::to_bson(&status).unwrap(),
                    "tx_hash": tx,
                    "updated_at": now,
                }
            };
        }

        if let Some(err) = error_message.clone() {
            update = doc! {
                "$set": {
                    "status": bson::to_bson(&status).unwrap(),
                    "error_message": err,
                    "updated_at": now,
                }
            };
        }

        if tx_hash.is_some() && error_message.is_some() {
            update = doc! {
                "$set": {
                    "status": bson::to_bson(&status).unwrap(),
                    "tx_hash": tx_hash.unwrap(),
                    "error_message": error_message.unwrap(),
                    "updated_at": now,
                }
            };
        }

        self.update_one(doc! { "_id": job_id }, update, None)
            .await?;

        Ok(())
    }

    pub async fn update_nft_object_id(
        &self,
        job_id: bson::oid::ObjectId,
        nft_object_id: String,
    ) -> anyhow::Result<()> {
        let now = DateTime::from_millis(Utc::now().timestamp_millis());

        self.update_one(
            doc! { "_id": job_id },
            doc! {
                "$set": {
                    "nft_object_id": nft_object_id,
                    "updated_at": now,
                }
            },
            None,
        )
        .await?;

        Ok(())
    }

    pub async fn update_pool_id(
        &self,
        job_id: bson::oid::ObjectId,
        pool_id: String,
    ) -> anyhow::Result<()> {
        let now = DateTime::from_millis(Utc::now().timestamp_millis());

        self.update_one(
            doc! { "_id": job_id },
            doc! {
                "$set": {
                    "pool_id": pool_id,
                    "updated_at": now,
                }
            },
            None,
        )
        .await?;

        Ok(())
    }

    pub async fn get_pending_jobs(&self, limit: i64) -> anyhow::Result<Vec<Job>> {
        let jobs = self
            .find(
                doc! { "status": bson::to_bson(&JobStatus::Pending).unwrap() },
                mongodb::options::FindOptions::builder()
                    .sort(doc! { "created_at": 1 })
                    .limit(limit)
                    .build(),
            )
            .await?;

        Ok(jobs)
    }
}
