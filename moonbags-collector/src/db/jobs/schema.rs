use bson::DateTime;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub enum JobType {
    DeployPoolAndAddLiquidity,
    AddLiquidity,
    BurnPosition,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum JobStatus {
    Pending,
    Processing,
    Completed,
    Failed,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct Job {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<bson::oid::ObjectId>,
    pub job_type: JobType,
    pub status: JobStatus,
    pub token_address: String,
    pub sui_amount: u64,
    pub token_amount: u64,
    pub pool_id: Option<String>,
    pub nft_object_id: Option<String>,
    pub is_sui_coin_first: Option<bool>,
    pub tx_hash: Option<String>,
    pub error_message: Option<String>,
    pub created_at: DateTime,
    pub updated_at: DateTime,
}
