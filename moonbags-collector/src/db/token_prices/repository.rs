use super::TokenPrices;
use crate::{
    db::{common::EDatabase, utils::traits::RepositorySharedMethod},
    services::redis_service::RedisService,
    utils::common::ToF64,
};
use bson::{doc, Decimal128};
use chrono::Utc;
use mongodb::{options::FindOneAndUpdateOptions, Collection, Database};
use std::str::FromStr;

#[derive(Debug, Clone)]
pub struct TokenPricesRepository {
    collection: Collection<TokenPrices>,
}

impl RepositorySharedMethod for TokenPricesRepository {
    type Schema = TokenPrices;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::TokenPrices.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl TokenPricesRepository {
    pub async fn upsert_token_price(
        &self,
        token_address: &str,
        price_usd: f64,
    ) -> anyhow::Result<(), anyhow::Error> {
        let result = self
            .get_collection()
            .find_one_and_update(
                doc! { "address": token_address },
                doc! {
                    "$set": {
                        "priceUsd": Decimal128::from_str(&price_usd.to_string()).unwrap(),
                        "updatedAt": bson::DateTime::from(Utc::now()),
                    },
                    "$setOnInsert": {
                        "address": token_address,
                        "createdAt": bson::DateTime::from(Utc::now()),
                    }
                },
                FindOneAndUpdateOptions::builder().upsert(true).build(),
            )
            .await;

        match result {
            Ok(_) => {
                tracing::info!(
                    "Token price upserted for: token={:?}, price_usd={:?}",
                    token_address,
                    price_usd
                );
            }
            Err(e) => {
                tracing::error!(
                    "Failed to upsert token price for token={:?}, price_usd={:?}: {}",
                    token_address,
                    price_usd,
                    e
                );
            }
        }

        Ok(())
    }

    pub async fn get_cached_token_price(
        &self,
        token_address: &str,
    ) -> anyhow::Result<f64, anyhow::Error> {
        let redis_service = RedisService::new().await;
        let sui_price_usd = redis_service.get_sui_price_usd().await?;

        match sui_price_usd {
            Some(sui_price_usd) => Ok(sui_price_usd.parse::<f64>().unwrap()),
            None => {
                let filter = doc! { "address": token_address };
                let token_price = self.find_one(filter, None).await?;

                match token_price {
                    Some(token_price) => Ok(token_price.price_usd.to_f64()),
                    None => Err(anyhow::anyhow!(format!(
                        "Failed to get token price for token_address={:?}: {}",
                        token_address, "Token price not found"
                    ))),
                }
            }
        }
    }
}
