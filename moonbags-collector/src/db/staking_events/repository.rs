use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::StakingEvents;

#[derive(Debug, Clone)]
pub struct StakingEventsRepository {
    collection: Collection<StakingEvents>,
}

impl RepositorySharedMethod for StakingEventsRepository {
    type Schema = StakingEvents;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::StakingEvents.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}
