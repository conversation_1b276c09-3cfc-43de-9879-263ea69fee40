use bson::Document;
use serde::{Deserialize, Serialize};
use strum_macros::{EnumString, VariantNames};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct StakingEvents {
    pub tx_digest: String,
    pub event_seq: u64,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub timestamp: Option<u64>,
    pub event: EStakingEventType,
    pub data: Document,
}

#[derive(
    Debug,
    Serialize,
    Deserialize,
    Clone,
    Copy,
    EnumString,
    VariantNames,
    strum_macros::Display,
    PartialEq,
    Eq,
    Hash,
)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum EStakingEventType {
    ClaimCreatorPoolEvent,
    ClaimStakingPoolEvent,
    DepositPoolCreatorEvent,
    InitializeCreatorPoolEvent,
    InitializeStakingPoolEvent,
    StakeEvent,
    UnstakeEvent,
    UpdateRewardIndexEvent,
}
