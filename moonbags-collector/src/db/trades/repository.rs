use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::Trades;

#[derive(Debug, Clone)]
pub struct TradesRepository {
    collection: Collection<Trades>,
}

impl RepositorySharedMethod for TradesRepository {
    type Schema = Trades;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::Trades.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}
