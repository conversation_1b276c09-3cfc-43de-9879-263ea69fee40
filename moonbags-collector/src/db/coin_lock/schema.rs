use bson::{DateTime, Decimal128};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct CoinLock {
    pub contract_id: String,
    pub locker: String,
    pub recipient: String,
    pub token_info: TokenInfo,
    pub amount: Decimal128,
    pub fee: Decimal128,
    pub start_time: u64,
    pub end_time: u64,
    pub closed: bool,
    pub created_at: DateTime,
    pub updated_at: DateTime,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct TokenInfo {
    pub token_address: String,
    pub symbol: String,
    pub logo_uri: String,
}
