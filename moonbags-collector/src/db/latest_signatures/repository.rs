use bson::doc;
use chrono::Utc;
use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::{EServiceName, LatestSignatures};

#[derive(<PERSON>bu<PERSON>, Clone)]
pub struct LatestSignaturesRepository {
    collection: Collection<LatestSignatures>,
}

impl RepositorySharedMethod for LatestSignaturesRepository {
    type Schema = LatestSignatures;

    fn new(database: &Database) -> Self {
        Self {
            collection: database
                .collection::<Self::Schema>(&EDatabase::LatestSignatures.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl LatestSignaturesRepository {
    pub async fn find_one_by_service_name(
        &self,
        service_name: &EServiceName,
    ) -> anyhow::Result<Option<LatestSignatures>> {
        let record = self
            .collection
            .find_one(doc! { "serviceName": bson::to_bson(service_name)? }, None)
            .await?;
        Ok(record)
    }

    pub async fn upsert_latest_signature(
        &self,
        service_name: &EServiceName,
        signature: &str,
    ) -> anyhow::Result<()> {
        match self.find_one_by_service_name(service_name).await? {
            Some(_) => {
                let filter = doc! {
                    "serviceName": bson::to_bson(service_name)?
                };
                let update = doc! {
                    "$set": {
                        "signature": signature.to_owned(),
                        "updatedAt": Utc::now(),
                    }
                };
                self.update_one(filter, update, None).await?;
            }
            None => {
                self.insert_one_or_ignore(
                    &LatestSignatures {
                        id: None,
                        service_name: service_name.clone(),
                        signature: signature.to_owned(),
                        updated_at: Utc::now().into(),
                    },
                    None,
                )
                .await?;
            }
        };
        Ok(())
    }
}
