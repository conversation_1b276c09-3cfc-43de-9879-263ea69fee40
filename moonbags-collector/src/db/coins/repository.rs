use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::Coins;

#[derive(Debug, Clone)]
pub struct CoinsRepository {
    collection: Collection<Coins>,
}

impl RepositorySharedMethod for CoinsRepository {
    type Schema = Coins;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::Coins.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}
