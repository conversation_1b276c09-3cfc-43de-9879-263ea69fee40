use std::sync::LazyLock;

use clap::Parse<PERSON>;
use sui_sdk::types::{base_types::ObjectID, Identifier};

pub static APP_CONFIG: LazyLock<Config> = LazyLock::new(Config::parse);

#[derive(Debug, Parser)]
pub struct Config {
    #[clap(long, env, default_value = "debug")]
    pub log_level: String,

    #[clap(long, env, default_value = "https://fullnode.mainnet.sui.io:443")]
    pub fullnode_url: String,

    /// the address part of the events
    #[clap(long, env)]
    pub staking_event_package: ObjectID,

    /// the address part of the events
    #[clap(long, env)]
    pub bonding_event_package: ObjectID,

    #[clap(long, env)]
    pub moonbags_package_id: ObjectID,

    /// the module part of the events
    #[clap(long, env)]
    pub event_module: Identifier,

    /// the module part of the stake events
    #[clap(long, env, default_value = "moonbags_stake")]
    pub stake_event_module: Identifier,

    /// the module part of the token lock events
    #[clap(long, env, default_value = "moonbags_token_lock")]
    pub token_lock_event_module: Identifier,

    #[clap(long, env)]
    pub mongodb_uri: String,

    #[clap(long, env)]
    pub redis_host: String,

    #[clap(long, env)]
    pub redis_port: String,

    #[clap(long, env)]
    pub redis_password: String,

    #[clap(long, env)]
    pub redis_db: String,

    #[clap(long, env, value_delimiter = ',')]
    pub kafka_brokers: Vec<String>,

    #[clap(long, env)]
    pub kafka_group_id: String,

    #[clap(long, env, default_value = "false")]
    pub kafka_ssl_enabled: bool,

    #[clap(long, env)]
    pub kafka_sasl_username: String,

    #[clap(long, env)]
    pub kafka_sasl_password: String,

    #[clap(long, env)]
    pub cetus_package: String,

    #[clap(long, env)]
    pub shro_token_address: String,

    #[clap(long, env)]
    pub admin_wallet_private_key: String,

    #[clap(long, env)]
    pub moonbags_configuration_object: String,

    // Turbos Finance configuration
    #[clap(long, env)]
    pub turbos_finance_clmm_package_id: String,

    #[clap(long, env)]
    pub turbos_position_nft_type: String,

    #[clap(long, env)]
    pub turbos_finance_address: String,

    #[clap(long, env)]
    pub turbos_dynamic_table_address: String,

    #[clap(long, env)]
    pub turbos_pool_config_object_id: String,

    #[clap(long, env)]
    pub turbos_pool_config_initial_shared_version: u64,

    #[clap(long, env)]
    pub turbos_fee_type_object_id: String,

    #[clap(long, env)]
    pub turbos_fee_type_sequence_number: u64,

    #[clap(long, env)]
    pub turbos_fee_type_object_digest: String,

    #[clap(long, env)]
    pub turbos_positions_object_id: String,

    #[clap(long, env)]
    pub turbos_positions_initial_shared_version: u64,

    #[clap(long, env)]
    pub turbos_versioned_object_id: String,

    #[clap(long, env)]
    pub turbos_versioned_initial_shared_version: u64,

    #[clap(long, env)]
    pub sui_type: String,

    #[clap(long, env)]
    pub turbos_fee_type: String,

    #[clap(long, env)]
    pub turbos_swap_event: String,

    #[clap(long, env)]
    pub telegram_bot_token: Option<String>,

    #[clap(long, env)]
    pub telegram_chat_id: Option<String>,

    #[clap(long, env, default_value = "false")]
    pub telegram_notifications_enabled: bool,
}
