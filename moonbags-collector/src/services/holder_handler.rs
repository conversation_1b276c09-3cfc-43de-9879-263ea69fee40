use std::str::FromStr;

use bson::doc;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

use crate::{
    common::HOLDERS_REPOSITORY,
    db::{holders::Holders, utils::traits::RepositorySharedMethod},
    utils::common::{un_decimals_from_str, ToF64, TOKEN_DECIMALS},
};

use super::{
    collect_curve_events::TradedEvent,
    kafka::{enums::KafkaTopic, kafka_service::KafkaService},
    redis_service::RedisEmitter,
};

pub async fn handle_traded_event_for_holder(
    event: &TradedEvent,
    tx_digest: String,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let is_buy = event.is_buy;
    let token_address = event.token_address.to_string();
    let user_address = event.user.to_string();
    let un_decimals_amount = un_decimals_from_str(&event.token_amount.to_string(), TOKEN_DECIMALS);
    let change_amount = if is_buy {
        un_decimals_amount
    } else {
        -un_decimals_amount
    };

    upsert_holder(
        token_address.clone(),
        user_address.clone(),
        change_amount,
        tx_digest.clone(),
        event.ts,
    )
    .await?;

    // Send update holder event
    let pub_holder = PubHolders {
        token_address: token_address.clone(),
        user_address: user_address.clone(),
        change_amount,
        last_hash: tx_digest.clone(),
        last_synced_at: event.ts,
    };

    if let Err(e) = emit_update_holder_event(&pub_holder, &token_address, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::UpdatedHolder, &vec![pub_holder]).await
    {
        tracing::error!("Failed to publish to kafka: {}", e);
    }

    Ok(())
}

async fn upsert_holder(
    token_address: String,
    user_address: String,
    change_amount: f64,
    last_hash: String,
    last_synced_at: u64,
) -> anyhow::Result<()> {
    let result = HOLDERS_REPOSITORY
        .get_collection()
        .find_one(
            doc! {
                "tokenAddress": &token_address,
                "userAddress": &user_address,
            },
            None,
        )
        .await;

    let Ok(opt_holder) = result else {
        tracing::error!(
            "Failed to find holder info for holder={}, token={}: {:?}",
            &token_address,
            &user_address,
            result.err()
        );
        return Ok(());
    };

    let Some(holder) = opt_holder else {
        let new_holder = Holders {
            token_address: token_address.clone(),
            user_address: user_address.clone(),
            amount: change_amount.to_string(),
            last_hash: last_hash.clone(),
            last_synced_at,
        };

        match HOLDERS_REPOSITORY
            .get_collection()
            .insert_one(new_holder, None)
            .await
        {
            Ok(_) => {
                tracing::info!(
                    "Holder info inserted for: holder={}, token={}, amount={}",
                    &token_address,
                    &user_address,
                    change_amount
                );
            }
            Err(e) => {
                tracing::error!(
                    "Failed to insert holder info for holder={}, token={}, amount={}: {}",
                    &token_address,
                    &user_address,
                    change_amount,
                    e
                );
            }
        }
        return Ok(());
    };

    let change_bg_amount = Decimal::from_str(&change_amount.to_string()).unwrap();
    let old_bg_amount = Decimal::from_str(&holder.amount.to_string()).unwrap();
    let new_amount = (old_bg_amount + change_bg_amount).to_f64();

    let result = HOLDERS_REPOSITORY
        .get_collection()
        .update_one(
            doc! {
                "tokenAddress": &token_address,
                "userAddress": &user_address,
            },
            doc! {
                "$set": {
                    "amount": &new_amount.to_string(),
                    "lastHash": &last_hash,
                    "lastSyncedAt": bson::to_bson(&last_synced_at)?
                }
            },
            None,
        )
        .await;

    match result {
        Ok(_) => {
            tracing::info!(
                "Holder info updated for: holder={}, token={}, amount={}",
                &user_address,
                &token_address,
                new_amount
            );
        }
        Err(e) => {
            tracing::error!(
                "Failed to update holder info for holder={}, token={}, amount={}: {}",
                &user_address,
                &token_address,
                new_amount,
                e
            );
        }
    }

    Ok(())
}

fn emit_update_holder_event(
    holder: &PubHolders,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(holder)?;
    let room = format!("SUBSCRIBE_COIN::{}", token_address);
    let event = "UpdatedHolder";

    redis_emitter.emit_room(&room, event, &pub_data);

    Ok(())
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubHolders {
    pub token_address: String,
    pub user_address: String,
    pub change_amount: f64,
    pub last_hash: String,
    pub last_synced_at: u64,
}
