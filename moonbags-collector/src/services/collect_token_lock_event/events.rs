use serde::{Deserialize, Serialize};
use serde_with::serde_as;
use serde_with::DisplayFromStr;

use crate::utils::common::{deserialize_sui_address, deserialize_token_type};

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct LockCreatedEvent {
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub contract_id: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub locker: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub recipient: String,
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde_as(as = "DisplayFromStr")]
    pub amount: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub fee: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub start_time: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub end_time: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct TokensWithdrawnEvent {
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub contract_id: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub sender: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub recipient: String,
    #[serde_as(as = "DisplayFromStr")]
    pub amount: u64,
}
