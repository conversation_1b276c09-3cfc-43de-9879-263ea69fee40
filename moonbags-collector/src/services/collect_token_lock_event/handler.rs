use bson::{doc, Decimal128};
use chrono::Utc;
use mongodb::options::{FindOneAndUpdateOptions};
use std::str::FromStr;

use crate::{
    common::{COINS_REPOSITORY, COIN_LOCK_REPOSITORY},
    db::{
        coin_lock::{CoinLock, TokenInfo},
        utils::traits::RepositorySharedMethod,
    },
    utils::common::{un_decimals_from_str, TOKEN_DECIMALS},
};

use super::events::{LockCreatedEvent, TokensWithdrawnEvent};

pub async fn handle_lock_created_event(event: &LockCreatedEvent) -> anyhow::Result<()> {
    tracing::info!("Handling lock created event: {:?}", event);

    let coin_info_result = COINS_REPOSITORY
        .get_collection()
        .find_one(doc! { "tokenAddress": &event.token_address }, None)
        .await?;

    let (symbol, logo_uri) = match coin_info_result {
        Some(coin_info) => (
            coin_info.symbol.unwrap_or_default(),
            coin_info.logo_uri.unwrap_or_default(),
        ),
        None => {
            tracing::warn!(
                "Token not found in coins repository: {}",
                event.token_address
            );
            ("".to_string(), "".to_string())
        }
    };

    let coin_lock = CoinLock {
        contract_id: event.contract_id.clone(),
        locker: event.locker.clone(),
        recipient: event.recipient.clone(),
        token_info: TokenInfo {
            token_address: event.token_address.clone(),
            symbol,
            logo_uri,
        },
        amount: Decimal128::from_str(
            &un_decimals_from_str(&event.amount.to_string(), TOKEN_DECIMALS).to_string(),
        )
        .unwrap(),
        fee: Decimal128::from_str(
            &un_decimals_from_str(&event.fee.to_string(), TOKEN_DECIMALS).to_string(),
        )
        .unwrap(),
        start_time: event.start_time,
        end_time: event.end_time,
        closed: false,
        created_at: Utc::now().into(),
        updated_at: Utc::now().into(),
    };

    let filter = doc! {
        "contractId": &event.contract_id,
    };
    
    let token_info_bson = bson::to_bson(&coin_lock.token_info)
        .map_err(|e| anyhow::anyhow!("Failed to serialize token info: {}", e))?;
    
    let update = doc! {
        "$set": {
            "locker": &event.locker,
            "recipient": &event.recipient,
            "amount": &coin_lock.amount,
            "tokenInfo": token_info_bson,
            "fee": &coin_lock.fee,
            "startTime": coin_lock.start_time as i64,
            "endTime": coin_lock.end_time as i64,
            "closed": false,
            "updatedAt": Utc::now(),
        }
    };
    let options = FindOneAndUpdateOptions::builder().upsert(true).build();

    match COIN_LOCK_REPOSITORY.find_one_and_update(filter, update, options).await {
        Ok(_) => {
            tracing::info!(
                "Successfully saved lock created event: contract={}, locker={}, recipient={}, amount={}, start_time={}, end_time={}",
                event.contract_id,
                event.locker,
                event.recipient,
                event.amount,
                coin_lock.start_time,
                coin_lock.end_time,
            );
            Ok(())
        }
        Err(e) => {
            tracing::error!("Failed to save lock created event: {}", e);
            Err(anyhow::anyhow!("Failed to save lock created event: {}", e))
        }
    }
}

pub async fn handle_tokens_withdrawn_event(event: &TokensWithdrawnEvent) -> anyhow::Result<()> {
    tracing::info!("Handling tokens withdrawn event: {:?}", event);

    let result = COIN_LOCK_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "contractId": &event.contract_id,
            },
            doc! { "$set": { "closed": true } },
            None,
        )
        .await;

    match result {
        Ok(Some(_)) => {
            tracing::info!("Lock fully withdrawn: contract={}", event.contract_id);

            Ok(())
        }
        Ok(None) => {
            tracing::warn!(
                "No matching lock record found for withdrawal: contract={}. Cannot process withdrawal.",
                event.contract_id
            );
            Ok(())
        }
        Err(e) => {
            tracing::error!("Failed to update lock record: {}", e);
            Err(anyhow::anyhow!("Failed to update lock record: {}", e))
        }
    }
}
