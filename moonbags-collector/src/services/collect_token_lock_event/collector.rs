use std::time::Duration;
use sui_sdk::{
    rpc_types::{EventFilter, SuiEvent},
    types::event::EventID,
};
use tokio::time::sleep;

use crate::{
    common::{get_sui_client_instance, LATEST_SIGNATURES_REPOSITORY},
    config::APP_CONFIG,
    db::latest_signatures::EServiceName,
};

use super::{
    events::{LockCreatedEvent, TokensWithdrawnEvent},
    handler::{handle_lock_created_event, handle_tokens_withdrawn_event},
};

pub async fn collect_token_lock_events() -> anyhow::Result<()> {
    let sui_client = get_sui_client_instance();

    let limit = None;
    let next_cursor_record = LATEST_SIGNATURES_REPOSITORY
        .find_one_by_service_name(&EServiceName::CollectTokenLockEvent)
        .await?;
    let mut next_cursor = match next_cursor_record {
        Some(x) => Some(serde_json::from_str::<EventID>(&x.signature)?),
        None => None,
    };

    let filter = EventFilter::MoveEventModule {
        package: APP_CONFIG.staking_event_package,
        module: APP_CONFIG.token_lock_event_module.clone(),
    };

    loop {
        let result = sui_client
            .event_api()
            .query_events(filter.clone(), next_cursor, limit, false)
            .await;

        let Ok(events) = result else {
            tracing::error!("Failed to query events: {:?}", result.err());
            sleep(Duration::from_millis(800)).await;
            continue;
        };

        if events.data.is_empty() {
            tracing::info!("No token lock events found, sleeping for 800ms");
            sleep(Duration::from_millis(800)).await;
            continue;
        }
        if events.next_cursor.is_none() {
            tracing::info!("No more events, lazy exit now");
            break;
        }

        process_events(&events.data).await?;
        next_cursor = events.next_cursor;
        sleep(Duration::from_millis(400)).await;
    }

    Ok(())
}

async fn process_events(events: &[SuiEvent]) -> anyhow::Result<()> {
    for event in events {
        match event.type_.name.as_str() {
            "LockCreatedEvent" => {
                let lock_event =
                    serde_json::from_value::<LockCreatedEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed lock created event: {:?}", lock_event);

                if let Err(e) = handle_lock_created_event(&lock_event).await {
                    tracing::error!("Error handling lock created event: {}", e);
                    continue;
                }
            }
            "TokensWithdrawnEvent" => {
                let withdraw_event =
                    serde_json::from_value::<TokensWithdrawnEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed tokens withdrawn event: {:?}", withdraw_event);

                if let Err(e) = handle_tokens_withdrawn_event(&withdraw_event).await {
                    tracing::error!("Error handling tokens withdrawn event: {}", e);
                    continue;
                }
            }
            _ => {
                tracing::warn!("Unknown event type: {:?}", event.type_.name);
                continue;
            }
        }

        if let Err(e) = LATEST_SIGNATURES_REPOSITORY
            .upsert_latest_signature(
                &EServiceName::CollectTokenLockEvent,
                &serde_json::to_string(&event.id)?,
            )
            .await
        {
            tracing::error!("Failed to update latest signature: {}", e);
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;
    use sui_sdk::{
        rpc_types::SuiTransactionBlockResponseOptions, types::digests::TransactionDigest,
    };

    use super::*;
    use crate::{
        common::initialize_sui_client, db::common::setup_mongodb,
        utils::tracing::init_standard_tracing,
    };

    #[tokio::test]
    async fn test_process_events() -> anyhow::Result<()> {
        dotenv::dotenv().ok();
        init_standard_tracing(env!("CARGO_CRATE_NAME"));
        setup_mongodb(&APP_CONFIG.mongodb_uri).await;
        initialize_sui_client().await;

        let sui_client = get_sui_client_instance();

        let tx = sui_client
            .read_api()
            .get_transaction_with_options(
                TransactionDigest::from_str("5zWFPbEqcfqWRhrDMxHrvykCRFZsUkkcFaqTHVR3MALa")
                    .unwrap(),
                SuiTransactionBlockResponseOptions {
                    show_input: false,
                    show_raw_input: false,
                    show_effects: false,
                    show_events: true,
                    show_object_changes: true,
                    show_balance_changes: false,
                    show_raw_effects: false,
                },
            )
            .await?;
        let events_data = tx.events.unwrap().data;

        process_events(&events_data).await?;

        Ok(())
    }
}
