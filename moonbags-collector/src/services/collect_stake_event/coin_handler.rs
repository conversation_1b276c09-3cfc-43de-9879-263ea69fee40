use std::str::FromStr;

use bson::{doc, Decimal128};
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sui_sdk::types::event::EventID;

use crate::{
    common::COINS_REPOSITORY,
    db::utils::traits::RepositorySharedMethod,
    services::redis_service::RedisEmitter,
    utils::common::{get_real_token_decimals, un_decimals_from_str, SUI_DECIMALS},
};

use super::events::{
    ClaimCreatorPoolEvent, ClaimStakingPoolEvent, DepositPoolCreatorEvent,
    InitializeCreatorPoolEvent, InitializeStakingPoolEvent, StakeEvent, UnstakeEvent,
    UpdateRewardIndexEvent,
};

pub async fn handle_initialize_staking_pool_event(event: &InitializeStakingPoolEvent) {
    let token_address = &event.token_address;
    let staking_pool_address = &event.staking_pool;

    match COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
            },
            doc! {
                "$set": {
                    "rewardTokenPool.address": &staking_pool_address
                },
                "$setOnInsert": {
                    "tokenAddress": &token_address,
                },
            },
            FindOneAndUpdateOptions::builder().upsert(true).build(),
        )
        .await
    {
        Ok(_) => {
            tracing::info!(
                "Updated staking pool for {}: {}",
                token_address,
                staking_pool_address
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update staking pool for {}: {:?}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_stake_event(
    event: &StakeEvent,
    redis_emitter: &RedisEmitter,
    event_id: &EventID,
) {
    let token_address = &event.token_address;
    let staking_amount = un_decimals_from_str(
        &event.amount.to_string(),
        get_real_token_decimals(token_address),
    );

    let event_uuid = generate_event_uuid(event.timestamp, event_id);
    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address, 
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$inc": {
                    "rewardTokenPool.stakedAmount": Decimal128::from_str(&staking_amount.to_string()).unwrap()
                },
                "$set": {
                    "rewardTokenPool.event_uuid": event_uuid,
                },
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update token staking amount for {}: {:?}",
            token_address,
            result.err()
        );
        return;
    };
    let Some(updated_coin) = opt_coin else {
        tracing::error!(
            "Failed to update token staking amount for {}: {}",
            token_address,
            "Token not found"
        );
        return;
    };

    let new_staked_amount = updated_coin
        .reward_token_pool
        .and_then(|pool| pool.staked_amount)
        .map(|amount| amount.to_string())
        .unwrap();

    tracing::info!(
        "Updated token staking amount for {}: amount={}, new_staked_amount={}",
        token_address,
        staking_amount,
        new_staked_amount
    );

    let pub_event = PubStakingEvent {
        token_address: token_address.clone(),
        amount: staking_amount.to_string(),
        user_address: event.staker.clone(),
        timestamp: event.timestamp,
    };

    if let Err(e) = emit_staking_event_to_coin_room(&pub_event, token_address, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) = emit_staking_event_to_staking_room(&pub_event, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }
}

pub async fn handle_unstake_event(
    event: &UnstakeEvent,
    redis_emitter: &RedisEmitter,
    event_id: &EventID,
) {
    let token_address = event.token_address.clone();
    let unstaking_amount = un_decimals_from_str(
        &event.amount.to_string(),
        get_real_token_decimals(&token_address),
    );
    let negative_amount = -Decimal::from_str(&unstaking_amount.to_string()).unwrap();

    let event_uuid = generate_event_uuid(event.timestamp, event_id);
    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address, 
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$inc": {
                    "rewardTokenPool.stakedAmount": Decimal128::from_str(&negative_amount.to_string()).unwrap()
                },
                "$set": {
                    "rewardTokenPool.event_uuid": event_uuid,
                },
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update token staking amount for {}: {:?}",
            token_address,
            result.err()
        );
        return;
    };

    let Some(updated_coin) = opt_coin else {
        tracing::error!(
            "Failed to update token staking amount for {}: {}",
            token_address,
            "Token not found"
        );
        return;
    };

    let new_staked_amount = updated_coin
        .reward_token_pool
        .and_then(|pool| pool.staked_amount)
        .map(|amount| amount.to_string())
        .unwrap();

    tracing::info!(
        "Updated token staking amount for {}: amount=-{}, new_staked_amount={}",
        token_address,
        unstaking_amount,
        new_staked_amount
    );

    let pub_event = PubStakingEvent {
        token_address: token_address.clone(),
        amount: negative_amount.to_string(),
        user_address: event.unstaker.clone(),
        timestamp: event.timestamp,
    };

    if let Err(e) = emit_staking_event_to_coin_room(&pub_event, &token_address, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) = emit_staking_event_to_staking_room(&pub_event, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }
}

/*
pub async fn handle_update_reward_index_event(event: &UpdateRewardIndexEvent) {
    let token_address = event.token_address.clone();
    let reward_amount = un_decimals_from_str(&event.reward.to_string(), SUI_DECIMALS);

    let result = COINS_REPOSITORY
        .find_one(doc! {"tokenAddress": &token_address}, None)
        .await;

    let token = match result {
        Ok(Some(token)) => token,
        Ok(None) => {
            tracing::error!(
                "Failed to update sui staking amount for {}: {}",
                token_address,
                "Token not found"
            );
            return;
        }
        Err(err) => {
            tracing::error!(
                "Failed to update sui staking amount for {}: {}",
                token_address,
                err
            );
            return;
        }
    };

    let reward_token_pool = token.reward_token_pool.as_ref().unwrap();
    let new_total_reward_amount = Decimal::from_str(&reward_amount.to_string()).unwrap()
        + reward_token_pool
            .total_reward
            .as_ref()
            .map(|x| Decimal::from_str(x).unwrap())
            .unwrap_or(Decimal::ZERO);
    let new_reward_claimable_amount = Decimal::from_str(&reward_amount.to_string()).unwrap()
        + reward_token_pool
            .reward_claimable
            .as_ref()
            .map(|x| Decimal::from_str(x).unwrap())
            .unwrap_or(Decimal::ZERO);

    match COINS_REPOSITORY
        .update_one(
            doc! {
                "tokenAddress": &token_address,
            },
            doc! {
                "$set": {
                    "rewardTokenPool.totalReward": &new_total_reward_amount.to_string(),
                    "rewardTokenPool.rewardClaimable": &new_reward_claimable_amount.to_string()
                },
            },
            None,
        )
        .await
    {
        Ok(_) => {
            tracing::info!(
                "Updated sui staking amount for {}: new_total_reward_amount={}, new_reward_claimable_amount={}",
                token_address,
                new_total_reward_amount,
                new_reward_claimable_amount,
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update sui staking amount for {}: {}",
                token_address,
                err
            );
        }
    }
}
*/

pub async fn handle_claim_staking_pool_event(event: &ClaimStakingPoolEvent, event_id: &EventID) {
    let token_address = event.token_address.clone();
    let reward_amount = un_decimals_from_str(&event.reward.to_string(), SUI_DECIMALS);

    let event_uuid = generate_event_uuid(event.timestamp, event_id);

    let update = doc! {
        "$set": {
            "rewardTokenPool.event_uuid": event_uuid,
        },
        "$inc": {
            "rewardTokenPool.rewardClaimed": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
        },
    };

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            update,
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    match result {
        Ok(Some(coin)) => {
            tracing::info!(
                "Updated sui staking amount for {}: new_reward_claimed_amount={}",
                token_address,
                coin.reward_token_pool
                    .as_ref()
                    .unwrap()
                    .reward_claimed
                    .unwrap()
                    .to_string(),
            );
        }
        Ok(None) => {
            tracing::error!("Not to update sui staking amount for {} ", token_address,);
        }
        Err(err) => {
            tracing::error!(
                "Failed to update sui staking amount for {}: {}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_initialize_creator_pool_event(
    event: &InitializeCreatorPoolEvent,
    event_id: &EventID,
) {
    let token_address = event.token_address.clone();
    let creator_pool_address = event.creator_pool.clone();

    let event_uuid = generate_event_uuid(event.timestamp, event_id);
    match COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardCreatorPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardCreatorPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardCreatorPool.event_uuid": event_uuid,
                    "rewardCreatorPool.address": &creator_pool_address
                },
                "$setOnInsert": {
                    "tokenAddress": &token_address,
                },
            },
            FindOneAndUpdateOptions::builder().upsert(true).build(),
        )
        .await
    {
        Ok(_) => {
            tracing::info!(
                "Updated creator pool for {}: {}",
                token_address,
                creator_pool_address
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update creator pool for {}: {:?}",
                token_address,
                err
            );
        }
    }
}

/*
pub async fn handle_deposit_pool_creator_event(event: &DepositPoolCreatorEvent) {
    let token_address = event.token_address.clone();
    let deposit_amount = un_decimals_from_str(&event.amount.to_string(), SUI_DECIMALS);

    let result = COINS_REPOSITORY
        .find_one(doc! {"tokenAddress": &token_address}, None)
        .await;

    let token = match result {
        Ok(Some(token)) => token,
        Ok(None) => {
            tracing::error!(
                "Failed to update sui creator amount for {}: {}",
                token_address,
                "Token not found"
            );
            return;
        }
        Err(err) => {
            tracing::error!(
                "Failed to update sui creator amount for {}: {}",
                token_address,
                err
            );
            return;
        }
    };

    let reward_creator_pool = token.reward_creator_pool.as_ref().unwrap();
    let new_total_reward_amount = Decimal::from_str(&deposit_amount.to_string()).unwrap()
        + reward_creator_pool
            .total_reward
            .as_ref()
            .map(|x| Decimal::from_str(x).unwrap())
            .unwrap_or(Decimal::ZERO);
    let new_reward_claimable_amount = Decimal::from_str(&deposit_amount.to_string()).unwrap()
        + reward_creator_pool
            .reward_claimable
            .as_ref()
            .map(|x| Decimal::from_str(x).unwrap())
            .unwrap_or(Decimal::ZERO);

    match COINS_REPOSITORY
        .update_one(
            doc! {
                "tokenAddress": &token_address,
            },
            doc! {
                "$set": {
                    "rewardCreatorPool.totalReward": &new_total_reward_amount.to_string(),
                    "rewardCreatorPool.rewardClaimable": &new_reward_claimable_amount.to_string(),
                },
            },
            None,
        )
        .await
    {
        Ok(_) => {
            tracing::info!(
                "Updated sui creator amount for {}: new_total_reward_amount={}, new_reward_claimable_amount={}",
                token_address,
                new_total_reward_amount,
                new_reward_claimable_amount,
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update sui creator amount for {}: {}",
                token_address,
                err
            );
        }
    }
}
*/

pub async fn handle_claim_creator_pool_event(event: &ClaimCreatorPoolEvent, event_id: &EventID) {
    let token_address = event.token_address.clone();
    let reward_amount = un_decimals_from_str(&event.reward.to_string(), SUI_DECIMALS);

    let event_uuid = generate_event_uuid(event.timestamp, event_id);
    let result =  COINS_REPOSITORY
        .get_collection().find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardCreatorPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardCreatorPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardCreatorPool.event_uuid": event_uuid,
                },
                "$inc": {
                    "rewardCreatorPool.rewardClaimed": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
                },
            },
            FindOneAndUpdateOptions::builder().return_document(ReturnDocument::After)
                .build(),
        )
        .await;
    match result {
        Ok(Some(coin)) => {
            tracing::info!(
                "Updated sui creator amount for {}: new_reward_claimed_amount={}",
                token_address,
                coin.reward_creator_pool
                    .as_ref()
                    .unwrap()
                    .reward_claimed
                    .unwrap()
                    .to_string(),
            );
        }
        Ok(None) => {
            tracing::warn!("Failed to update sui creator amount for {}", token_address,);
        }
        Err(err) => {
            tracing::error!(
                "Failed to update sui creator amount for {}: {}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_update_reward_for_token_pool(
    event: &UpdateRewardIndexEvent,
    event_id: &EventID,
) {
    let token_address = event.token_address.clone();
    let reward_amount = un_decimals_from_str(&event.reward.to_string(), SUI_DECIMALS);

    let event_uuid = generate_event_uuid(event.timestamp, event_id);

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardTokenPool.event_uuid": event_uuid,
                },
                "$inc": {
                    "rewardTokenPool.totalReward": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
                },
            },
            None,
        )
    .await;
    match result {
        Ok(Some(_coin)) => {
            tracing::info!(
                "Updated reward for token pool for {}: with new reward amount={}",
                token_address,
                reward_amount,
            );
        }
        Ok(None) => {
            tracing::warn!(
                "Failed to update reward for token pool for {}",
                token_address,
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update reward for token pool for {}: {}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_update_reward_for_creator_pool(
    event: &DepositPoolCreatorEvent,
    event_id: &EventID,
) {
    let token_address = event.token_address.clone();
    let reward_amount = un_decimals_from_str(&event.amount.to_string(), SUI_DECIMALS);

    let event_uuid = generate_event_uuid(event.timestamp, event_id);

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {"tokenAddress": &token_address, 
                "$or": [
                    {"rewardCreatorPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardCreatorPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardCreatorPool.event_uuid": event_uuid,
                },
                "$inc": {
                    "rewardCreatorPool.totalReward": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
                },
            },
            None,
        )
    .await;
    println!("handle_update_reward_for_creator_pool result: {:?}", result);
    match result {
        Ok(Some(_coin)) => {
            tracing::info!(
                "Updated reward for creator pool for {}: with new amount={}",
                token_address,
                reward_amount,
            );
        }
        Ok(None) => {
            tracing::warn!(
                "Failed to update reward for creator pool for {}",
                token_address,
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update reward for creator pool for {}: {}",
                token_address,
                err
            );
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubStakingEvent {
    pub token_address: String,
    pub user_address: String,
    pub amount: String,
    pub timestamp: u64,
}

fn emit_staking_event_to_coin_room(
    event: &PubStakingEvent,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(event)?;
    let room = format!("SUBSCRIBE_COIN::{}", token_address);
    let event_type = "UpdateStaking";
    redis_emitter.emit_room(&room, event_type, &pub_data);
    Ok(())
}

fn emit_staking_event_to_staking_room(
    event: &PubStakingEvent,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(event)?;
    let room = "SUBSCRIBE_STAKING";
    let event_type = "UpdateStaking";
    redis_emitter.emit_room(room, event_type, &pub_data);
    Ok(())
}

fn generate_event_uuid(event_timestamp: u64, event_id: &EventID) -> i64 {
    let multiplier = 10000;
    let event_seq = event_id.event_seq;
    (event_timestamp * multiplier + event_seq) as i64
}
