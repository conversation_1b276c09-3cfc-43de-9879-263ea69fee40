use serde::{Deserialize, Serialize};
use serde_with::serde_as;
use serde_with::DisplayFromStr;

use crate::utils::common::{deserialize_sui_address, deserialize_token_type};

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct InitializeStakingPoolEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub initializer: String,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct InitializeCreatorPoolEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub creator_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub initializer: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub creator: String,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct StakeEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_account: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staker: String,
    #[serde_as(as = "DisplayFromStr")]
    pub amount: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct UnstakeEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_account: String,
    pub is_staking_account_deleted: bool,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub unstaker: String,
    #[serde_as(as = "DisplayFromStr")]
    pub amount: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateRewardIndexEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub reward_updater: String,
    #[serde_as(as = "DisplayFromStr")]
    pub reward: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct DepositPoolCreatorEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub creator_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub depositor: String,
    #[serde_as(as = "DisplayFromStr")]
    pub amount: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct ClaimStakingPoolEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub staking_account: String,
    pub is_staking_account_deleted: bool,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub claimer: String,
    #[serde_as(as = "DisplayFromStr")]
    pub reward: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct ClaimCreatorPoolEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub creator_pool: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub claimer: String,
    #[serde_as(as = "DisplayFromStr")]
    pub reward: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub timestamp: u64,
}
