use std::time::Duration;
use sui_sdk::{
    rpc_types::{EventFilter, SuiEvent},
    types::event::EventID,
};
use tokio::time::sleep;

use crate::services::{
    collect_stake_event::{coin_handler, staking_user_handler},
    redis_service::RedisEmitter,
};
use crate::{
    common::STAKING_EVENTS_REPOSITORY,
    services::collect_stake_event::events::{
        ClaimCreatorPoolEvent, ClaimStakingPoolEvent, DepositPoolCreatorEvent,
        InitializeStakingPoolEvent, UpdateRewardIndexEvent,
    },
};
use crate::{
    common::{get_sui_client_instance, LATEST_SIGNATURES_REPOSITORY},
    config::APP_CONFIG,
    db::latest_signatures::EServiceName,
};
use crate::{
    db::{
        staking_events::{EStakingEventType, StakingEvents},
        utils::traits::RepositorySharedMethod,
    },
    services::collect_stake_event::events::{InitializeCreatorPoolEvent, StakeEvent, UnstakeEvent},
};

pub async fn collect_stake_events() -> anyhow::Result<()> {
    let sui_client = get_sui_client_instance();
    let redis_emitter = RedisEmitter::new()?;

    let limit = None;
    let next_cursor_record = LATEST_SIGNATURES_REPOSITORY
        .find_one_by_service_name(&EServiceName::CollectStakeEvent)
        .await?;
    let mut next_cursor = match next_cursor_record {
        Some(x) => Some(serde_json::from_str::<EventID>(&x.signature)?),
        None => None,
    };

    // first, I want to try any filter, but it doesn't work
    // so
    let filter = EventFilter::MoveEventModule {
        package: APP_CONFIG.staking_event_package,
        module: APP_CONFIG.stake_event_module.clone(),
    };

    loop {
        // next_cursor is exclusive
        let result = sui_client
            .event_api()
            .query_events(filter.clone(), next_cursor, limit, false)
            .await;

        let Ok(events) = result else {
            tracing::error!("Failed to query events: {:?}", result.err());
            sleep(Duration::from_millis(800)).await;
            continue;
        };

        if events.data.is_empty() {
            sleep(Duration::from_millis(800)).await;
            continue;
        }
        if events.next_cursor.is_none() {
            tracing::info!("no more events, lazy exit now");
            break;
        }

        process_events(&events.data, &redis_emitter).await?;
        next_cursor = events.next_cursor;
        sleep(Duration::from_millis(800)).await;
    }

    Ok(())
}

async fn process_events(events: &[SuiEvent], redis_emitter: &RedisEmitter) -> anyhow::Result<()> {
    for event in events {
        let event_type;
        let data;

        match event.type_.name.as_str() {
            "InitializeStakingPoolEvent" => {
                let event = serde_json::from_value::<InitializeStakingPoolEvent>(
                    event.parsed_json.clone(),
                )?;
                tracing::info!("Processed event: {:?}", event);

                coin_handler::handle_initialize_staking_pool_event(&event).await;

                event_type = Some(EStakingEventType::InitializeStakingPoolEvent);
                data = Some(bson::to_document(&event)?);
            }

            "InitializeCreatorPoolEvent" => {
                let event_data = serde_json::from_value::<InitializeCreatorPoolEvent>(
                    event.parsed_json.clone(),
                )?;
                tracing::info!("Processed event: {:?}", event_data);

                coin_handler::handle_initialize_creator_pool_event(&event_data, &event.id).await;

                event_type = Some(EStakingEventType::InitializeCreatorPoolEvent);
                data = Some(bson::to_document(&event)?);
            }

            "StakeEvent" => {
                let event_data = serde_json::from_value::<StakeEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed event: {:?}", event_data);

                coin_handler::handle_stake_event(&event_data, redis_emitter, &event.id).await;
                staking_user_handler::handle_stake_event(&event_data).await;

                event_type = Some(EStakingEventType::StakeEvent);
                data = Some(bson::to_document(&event)?);
            }

            "UnstakeEvent" => {
                let event_data = serde_json::from_value::<UnstakeEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed event: {:?}", event_data);

                coin_handler::handle_unstake_event(&event_data, redis_emitter, &event.id).await;
                staking_user_handler::handle_unstake_event(&event_data).await;

                event_type = Some(EStakingEventType::UnstakeEvent);
                data = Some(bson::to_document(&event)?);
            }

            "UpdateRewardIndexEvent" => {
                let event_data =
                    serde_json::from_value::<UpdateRewardIndexEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed event: {:?}", event_data);

                coin_handler::handle_update_reward_for_token_pool(&event_data, &event.id).await;

                event_type = Some(EStakingEventType::UpdateRewardIndexEvent);
                data = Some(bson::to_document(&event)?);
            }

            "DepositPoolCreatorEvent" => {
                let event_data =
                    serde_json::from_value::<DepositPoolCreatorEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed event: {:?}", event_data);

                coin_handler::handle_update_reward_for_creator_pool(&event_data, &event.id).await;

                event_type = Some(EStakingEventType::DepositPoolCreatorEvent);
                data = Some(bson::to_document(&event)?);
            }

            "ClaimStakingPoolEvent" => {
                let event_data =
                    serde_json::from_value::<ClaimStakingPoolEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed event: {:?}", event_data);

                coin_handler::handle_claim_staking_pool_event(&event_data, &event.id).await;
                staking_user_handler::handle_claim_staking_pool_event(&event_data).await;

                event_type = Some(EStakingEventType::ClaimStakingPoolEvent);
                data = Some(bson::to_document(&event)?);
            }

            "ClaimCreatorPoolEvent" => {
                let event_data =
                    serde_json::from_value::<ClaimCreatorPoolEvent>(event.parsed_json.clone())?;
                tracing::info!("Processed event: {:?}", event_data);

                coin_handler::handle_claim_creator_pool_event(&event_data, &event.id).await;

                event_type = Some(EStakingEventType::ClaimCreatorPoolEvent);
                data = Some(bson::to_document(&event)?);
            }

            _ => {
                tracing::warn!("unknown event type: {:?}", event.type_.name);
                continue;
            }
        }

        let staking_event = StakingEvents {
            tx_digest: event.id.tx_digest.to_string(),
            event_seq: event.id.event_seq,
            timestamp: event.timestamp_ms,
            event: event_type.unwrap(),
            data: data.unwrap(),
        };

        match STAKING_EVENTS_REPOSITORY
            .insert_one(staking_event.clone(), None)
            .await
        {
            Ok(_) => {
                tracing::info!("Inserted staking event: {:?}", staking_event);
            }
            Err(err) => {
                tracing::error!("Failed to insert staking event: {:?}", err);
            }
        }

        LATEST_SIGNATURES_REPOSITORY
            .upsert_latest_signature(
                &EServiceName::CollectStakeEvent,
                &serde_json::to_string(&event.id)?,
            )
            .await?;
    }
    Ok(())
}
