use std::str::FromStr;

use bson::{doc, Decimal128};
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument};

use crate::{
    common::STAKING_USERS_REPOSITORY,
    db::utils::traits::RepositorySharedMethod,
    utils::common::{get_real_token_decimals, un_decimals_from_str, SUI_DECIMALS},
};

use super::events::{ClaimStakingPoolEvent, StakeEvent, UnstakeEvent};

pub async fn handle_stake_event(event: &StakeEvent) {
    let token_address = &event.token_address;
    let staker = &event.staker;
    let staking_pool_address = &event.staking_pool;
    let account_object_id = &event.staking_account;

    let staking_amount = un_decimals_from_str(
        &event.amount.to_string(),
        get_real_token_decimals(token_address),
    )
    .to_string();
    let staking_amount_decimal = Decimal128::from_str(&staking_amount).unwrap_or_else(|e| {
        tracing::error!("Failed to parse staking amount: {}", e);
        Decimal128::from_str("0").unwrap()
    });

    let result = STAKING_USERS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "stakingPoolAddress": staking_pool_address,
                "userAddress": staker,
            },
            doc! {
                "$inc": {
                    "stakedAmount": staking_amount_decimal
                },
                "$set": {
                    "lastStake": bson::to_bson(&event.timestamp).unwrap(),
                    "accountObjectId": account_object_id,
                },
                "$setOnInsert": {
                    "stakingPoolAddress": staking_pool_address,
                    "stakingCoinAddress": token_address,
                    "userAddress": staker,
                }
            },
            FindOneAndUpdateOptions::builder()
                .upsert(true)
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    match result {
        Ok(Some(user)) => {
            tracing::info!(
                "Updated staking user: token_address={}, user_address={}, amount={}, new_staked_amount={}",
                token_address,
                staker,
                staking_amount,
                user.staked_amount
            );
        }
        Ok(None) => {
            tracing::error!(
                "Failed to staking user: token_address={}, user_address={}, error={}",
                token_address,
                staker,
                "User not found"
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to staking user: token_address={}, user_address={}, error={}",
                token_address,
                staker,
                err
            );
        }
    }
}

pub async fn handle_unstake_event(event: &UnstakeEvent) {
    let token_address = &event.token_address;
    let unstaker = &event.unstaker;
    let staking_pool_address = &event.staking_pool;

    let unstaking_amount = un_decimals_from_str(
        &event.amount.to_string(),
        get_real_token_decimals(token_address),
    );
    let negative_amount =
        Decimal128::from_str(&(-unstaking_amount).to_string()).unwrap_or_else(|e| {
            tracing::error!("Failed to parse unstaking amount: {}", e);
            Decimal128::from_str("0").unwrap()
        });

    let result = STAKING_USERS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "stakingPoolAddress": staking_pool_address,
                "userAddress": unstaker,
            },
            doc! {
                "$inc": { "stakedAmount": negative_amount },
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    match result {
        Ok(Some(user)) => {
            tracing::info!(
                "Unstaked for user: token_address={}, user_address={}, amount={}, new_staked_amount={}",
                token_address,
                unstaker,
                unstaking_amount,
                user.staked_amount
            );
        }
        Ok(None) => {
            tracing::error!(
                "Failed to unstaking user: token_address={}, user_address={}, error={}",
                token_address,
                unstaker,
                "User not found"
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to unstaking user: token_address={}, user_address={}, error={}",
                token_address,
                unstaker,
                err
            );
        }
    }
}

pub async fn handle_claim_staking_pool_event(event: &ClaimStakingPoolEvent) {
    let token_address = &event.token_address;
    let claimer = &event.claimer;
    let staking_pool_address = &event.staking_pool;
    let undecimal_claimed_reward = un_decimals_from_str(&event.reward.to_string(), SUI_DECIMALS);
    match STAKING_USERS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "userAddress": claimer,
                "stakingCoinAddress": token_address,
                "stakingPoolAddress":staking_pool_address,
            },
            doc! {
                "$inc": {
                    "rewardClaimed": Decimal128::from_str(&undecimal_claimed_reward.to_string()).unwrap(),
                }
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await
    {
        Ok(Some(user)) => {
            tracing::info!(
                "Claimed reward for user: token_address={}, user_address={}, amount={}, new_reward_claimed={}",
                token_address,
                claimer,
                undecimal_claimed_reward,
                user.reward_claimed.unwrap()
            );
        }
        Ok(None) => {
            tracing::error!(
                "Failed to claim reward for user: token_address={}, user_address={}, error={}",
                token_address,
                claimer,
                "User not found"
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to claim reward for user: token_address={}, user_address={}, error={}",
                token_address,
                claimer,
                err
            );
        }
    }
}
