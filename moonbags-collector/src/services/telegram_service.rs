use anyhow::Result;
use reqwest::Client;
use serde_json::json;
use std::sync::LazyLock;
use std::time::Duration;

pub static HTTP_CLIENT: LazyLock<Client> = LazyLock::new(|| {
    Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .expect("Failed to create HTTP client")
});

pub struct TelegramService {
    bot_token: String,
    chat_id: String,
}

impl TelegramService {
    pub fn new(bot_token: String, chat_id: String) -> Self {
        Self { bot_token, chat_id }
    }

    pub async fn send_notification(&self, message: &str) -> Result<()> {
        let url = format!("https://api.telegram.org/bot{}/sendMessage", self.bot_token);

        let response = HTTP_CLIENT
            .post(&url)
            .json(&json!({
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": "HTML"
            }))
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!(
                "Failed to send Telegram notification: {}",
                error_text
            ));
        }

        Ok(())
    }
}
