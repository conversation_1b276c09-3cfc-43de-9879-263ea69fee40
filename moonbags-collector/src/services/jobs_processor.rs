use chrono::Utc;
use serde::{Deserialize, Serialize};
use std::str::FromStr;
use sui_sdk::{
    rpc_types::{
        SuiExecutionStatus, SuiMoveStruct, SuiObjectData, SuiParsedData, SuiTransactionBlockEffects,
    },
    types::{
        base_types::{Se<PERSON>N<PERSON><PERSON>, SuiAddress},
        object::Owner,
        programmable_transaction_builder::ProgrammableTransactionBuilder,
        transaction::{Argument, CallArg, Command, ObjectArg, ProgrammableTransaction},
        Identifier, TypeTag,
    },
};
use tokio::time::{sleep, Duration};

use crate::{
    common::LIQUIDITY_JOBS_REPOSITORY,
    config::APP_CONFIG,
    db::jobs::{Job, JobStatus, JobType},
    services::{
        migrate_dex_handler::update_listed_pool_id, redis_service::RedisEmitter,
        telegram_service::TelegramService,
    },
    utils::sui::{
        turbos_finance::{
            constants::{
                MOONBAGS_CONFIGURATION_OBJECT, MOONBAGS_PACKAGE_ID, POOL_CONFIG_OBJECT, SUI_TYPE,
                TURBOS_FEE_TYPE, TURBOS_FEE_TYPE_OBJECT, TURBOS_FINANCE_CLMM_PACKAGE_ID,
                TURBOS_POSITIONS_OBJECT, TURBOS_VERSIONED_OBJECT,
            },
            tx::{
                burn_position, execute_transaction_with_admin_wallet, get_nft_object_change_in_tx,
                get_pool_created_in_tx, prepare_sui_coins, prepare_token_coin, prepare_token_coins,
                simulate_transaction, DEFAULT_DEADLINE_MS, TICK_INDEX,
            },
        },
        utils::{get_object, price_to_sqrt_price_x64},
        wallet::SuiWallet,
    },
};

const THRESHOLD_LIQUIDITY_FOR_SWAP: u64 = 100_000_000;
const THRESHOLD_SQRT_DIFFERENCE_PERCENT: u128 = 10;

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TurbosSwapEvent {
    pub a_to_b: bool,
    pub amount_a: Option<String>,
    pub amount_b: Option<String>,
    pub fee_amount: Option<String>,
    pub is_exact_in: Option<bool>,
    pub liquidity: Option<String>,
    pub pool: Option<String>,
    pub protocol_fee: Option<String>,
    pub sqrt_price: Option<String>,
}

pub async fn process_jobs(redis_emitter: &RedisEmitter) -> anyhow::Result<()> {
    let telegram_service = if APP_CONFIG.telegram_notifications_enabled {
        match (&APP_CONFIG.telegram_bot_token, &APP_CONFIG.telegram_chat_id) {
            (Some(token), Some(chat_id)) => {
                Some(TelegramService::new(token.clone(), chat_id.clone()))
            }
            _ => {
                tracing::warn!(
                    "Telegram notifications are enabled but bot_token or chat_id is missing"
                );
                None
            }
        }
    } else {
        None
    };

    loop {
        let pending_jobs = LIQUIDITY_JOBS_REPOSITORY.get_pending_jobs(10).await?;
        if pending_jobs.is_empty() {
            sleep(Duration::from_secs(5)).await;
            continue;
        }

        for job in pending_jobs {
            tracing::info!("Processing liquidity job: {:?}", job);

            if let Some(id) = job.id {
                let _ = LIQUIDITY_JOBS_REPOSITORY
                    .update_job_status(id, JobStatus::Processing, None, None)
                    .await;

                let result = process_job(&job, redis_emitter).await;

                match result {
                    Ok(tx_hash) => {
                        let _ = LIQUIDITY_JOBS_REPOSITORY
                            .update_job_status(id, JobStatus::Completed, Some(tx_hash), None)
                            .await;
                    }
                    Err(e) => {
                        tracing::error!("Failed to process job: {}", e);
                        if let Some(telegram) = &telegram_service {
                            let job_info = format!(
                                "Job ID: {}\nType: {:?}\nToken: {}\nPool ID: {}\nError: {}",
                                id,
                                job.job_type,
                                job.token_address,
                                job.pool_id.unwrap_or_default(),
                                e
                            );

                            let message = format!("<b>❌ Job Processing Error</b>\n\n{}", job_info);

                            if let Err(notify_err) = telegram.send_notification(&message).await {
                                tracing::error!(
                                    "Failed to send Telegram notification: {}",
                                    notify_err
                                );
                            }
                        }

                        let _ = LIQUIDITY_JOBS_REPOSITORY
                            .update_job_status(id, JobStatus::Failed, None, Some(e.to_string()))
                            .await;
                    }
                }
            }
        }

        sleep(Duration::from_secs(5)).await;
    }
}

#[allow(unreachable_code)]
async fn process_job(job: &Job, redis_emitter: &RedisEmitter) -> anyhow::Result<String> {
    match job.job_type {
        JobType::DeployPoolAndAddLiquidity => {
            let token_type_tag = TypeTag::from_str(&job.token_address)?;
            let sui_amount = job.sui_amount;
            let token_amount = job.token_amount;

            let mut ptb = ProgrammableTransactionBuilder::new();
            let admin_wallet =
                SuiWallet::new_from_private_key(&APP_CONFIG.admin_wallet_private_key)?;
            let admin_address = admin_wallet.address;

            let module = Identifier::new("pool_factory")?;
            let function = Identifier::new("deploy_pool_and_mint")?;

            let sui_type_tag = TypeTag::from_str(&SUI_TYPE).unwrap();
            let fee_type_tag = TypeTag::from_str(&TURBOS_FEE_TYPE).unwrap();

            let pool_config = ptb.obj(*POOL_CONFIG_OBJECT)?;
            let fee_type = ptb.obj(*TURBOS_FEE_TYPE_OBJECT)?;
            let sqrt_price = ptb.pure(price_to_sqrt_price_x64(
                sui_amount as f64 / token_amount as f64,
                9,
                9,
            ))?;
            let positions = ptb.obj(*TURBOS_POSITIONS_OBJECT)?;

            let vec_sui_coin = prepare_sui_coins(&mut ptb, sui_amount).await?;
            let vec_token_coin = prepare_token_coins(
                &mut ptb,
                admin_address,
                token_type_tag.clone(),
                token_amount,
            )
            .await?;

            let tick_lower_index = ptb.pure(TICK_INDEX)?;
            let tick_lower_index_is_negative = ptb.pure(true)?;
            let tick_upper_index = ptb.pure(TICK_INDEX)?;
            let tick_upper_index_is_negative = ptb.pure(false)?;

            let token_amount = ptb.pure(token_amount)?;
            let sui_amount = ptb.pure(sui_amount)?;
            let token_amount_min = ptb.pure(0_u64)?;
            let sui_amount_in = ptb.pure(0_u64)?;

            let recipient = ptb.pure(admin_address)?;
            let deadline = ptb.pure(Utc::now().timestamp_millis() + DEFAULT_DEADLINE_MS)?;

            let clock = ptb.input(CallArg::CLOCK_IMM)?;
            let versioned = ptb.obj(*TURBOS_VERSIONED_OBJECT)?;

            ptb.command(Command::move_call(
                *TURBOS_FINANCE_CLMM_PACKAGE_ID,
                module,
                function,
                vec![token_type_tag.clone(), sui_type_tag, fee_type_tag],
                vec![
                    pool_config,
                    fee_type,
                    sqrt_price,
                    positions,
                    vec_token_coin,
                    vec_sui_coin,
                    tick_lower_index,
                    tick_lower_index_is_negative,
                    tick_upper_index,
                    tick_upper_index_is_negative,
                    token_amount,
                    sui_amount,
                    token_amount_min,
                    sui_amount_in,
                    recipient,
                    deadline,
                    clock,
                    versioned,
                ],
            ));

            let builder: ProgrammableTransaction = ptb.finish();

            let tx_hash = execute_transaction_with_admin_wallet(builder, admin_wallet).await?;
            tracing::debug!("deploy_pool_and_add_liquidity tx {}", tx_hash);

            let nft_object = get_nft_object_change_in_tx(&tx_hash).await?;
            let pool_object = get_pool_created_in_tx(&tx_hash).await?;

            if let Some(pool) = pool_object {
                update_listed_pool_id(
                    token_type_tag.to_string(),
                    pool.id().to_string(),
                    redis_emitter,
                )
                .await?;
            }

            if let (Some(nft), Some(pool)) = (nft_object, pool_object) {
                burn_position(pool, nft, token_type_tag, false).await?;
            }
            Ok(tx_hash)
        }
        JobType::AddLiquidity => {
            let token_type_tag = TypeTag::from_str(&job.token_address)?;
            let pool_id = job
                .pool_id
                .clone()
                .ok_or_else(|| anyhow::anyhow!("Pool ID is required for AddLiquidity job"))?;
            let is_sui_coin_first = job.is_sui_coin_first.unwrap_or(false);
            let pool_object_data = get_object(&pool_id).await?;

            let admin_wallet =
                SuiWallet::new_from_private_key(&APP_CONFIG.admin_wallet_private_key)?;

            let sui_amount = job.sui_amount;
            let token_amount = job.token_amount;

            let pool_object = ObjectArg::SharedObject {
                id: pool_object_data.object_id,
                initial_shared_version: match pool_object_data.owner {
                    Some(Owner::Shared {
                        initial_shared_version,
                    }) => initial_shared_version,
                    _ => SequenceNumber::from_u64(0),
                },
                mutable: true,
            };

            let admin_address = admin_wallet.address;

            let (amount_a_in_pool, amount_b_in_pool, liquidity) =
                get_amount_in_pool(&pool_object_data)?;

            if liquidity > THRESHOLD_LIQUIDITY_FOR_SWAP {
                return Err(anyhow::anyhow!(
                    "Liquidity is over threshold: {}",
                    liquidity
                ));
            }

            let expected_sqrt_price = if is_sui_coin_first {
                price_to_sqrt_price_x64(token_amount as f64 / sui_amount as f64, 9, 9)
            } else {
                price_to_sqrt_price_x64(sui_amount as f64 / token_amount as f64, 9, 9)
            };

            let (is_swap_sui_to_token, amount_sui_to_increase, amount_token_to_increase) =
                calculate_swap_amount(
                    amount_a_in_pool,
                    amount_b_in_pool,
                    is_sui_coin_first,
                    sui_amount,
                    token_amount,
                )?;

            let simulate_builder = build_add_liquidity_ptb(
                pool_object,
                is_swap_sui_to_token,
                amount_sui_to_increase,
                amount_token_to_increase,
                token_amount,
                sui_amount,
                token_type_tag.clone(),
                is_sui_coin_first,
                admin_address,
                None,
            )
            .await?;

            let simulate_result =
                simulate_transaction(simulate_builder.clone(), &admin_wallet).await?;

            #[allow(irrefutable_let_patterns)]
            if let SuiTransactionBlockEffects::V1(effects) = simulate_result.effects {
                if let SuiExecutionStatus::Failure { error } = effects.status {
                    return Err(anyhow::anyhow!("transaction execution failed: {}", error));
                }
            }

            let swap_event = simulate_result.events.data.iter().find(|event| {
                event
                    .type_
                    .to_string()
                    .contains(&APP_CONFIG.turbos_swap_event)
            });

            if swap_event.is_none() {
                return Err(anyhow::anyhow!("Don't find swap event"));
            }

            let swap_data: TurbosSwapEvent =
                serde_json::from_value(swap_event.unwrap().parsed_json.clone()).unwrap();

            if swap_data.sqrt_price.is_none() {
                return Err(anyhow::anyhow!("Don't find sqrt price"));
            }

            let sqrt_price = swap_data.sqrt_price.unwrap();
            let sqrt_price_u128 = u128::from_str(&sqrt_price)?;

            let threshold = expected_sqrt_price * THRESHOLD_SQRT_DIFFERENCE_PERCENT / 1000;

            let difference = if sqrt_price_u128 > expected_sqrt_price {
                sqrt_price_u128 - expected_sqrt_price
            } else {
                expected_sqrt_price - sqrt_price_u128
            };

            if difference > threshold {
                return Err(anyhow::anyhow!("Sqrt difference is over threshold"));
            }

            let amount_out_min = match (is_swap_sui_to_token, is_sui_coin_first) {
                (true, true) | (false, false) => swap_data.amount_b.as_ref(),
                (true, false) | (false, true) => swap_data.amount_a.as_ref(),
            }
            .ok_or_else(|| anyhow::anyhow!("Missing amount field in swap event"))?
            .parse::<u64>()?;

            let builder = build_add_liquidity_ptb(
                pool_object,
                is_swap_sui_to_token,
                amount_sui_to_increase,
                amount_token_to_increase,
                token_amount,
                sui_amount,
                token_type_tag.clone(),
                is_sui_coin_first,
                admin_address,
                Some(amount_out_min),
            )
            .await?;

            let tx_hash = execute_transaction_with_admin_wallet(builder, admin_wallet).await?;
            tracing::debug!("add_liquidity tx {}", tx_hash);
            update_listed_pool_id(
                token_type_tag.to_string(),
                pool_object_data.object_id.to_string(),
                redis_emitter,
            )
            .await?;

            if let Some(nft_object) = get_nft_object_change_in_tx(&tx_hash).await? {
                burn_position(pool_object, nft_object, token_type_tag, is_sui_coin_first).await?;
            }

            Ok(tx_hash)
        }
        JobType::BurnPosition => {
            let token_type_tag = TypeTag::from_str(&job.token_address)?;
            let pool_id = job
                .pool_id
                .clone()
                .ok_or_else(|| anyhow::anyhow!("Pool ID is required for BurnPosition job"))?;
            let nft_object_id = job
                .nft_object_id
                .clone()
                .ok_or_else(|| anyhow::anyhow!("NFT Object ID is required for BurnPosition job"))?;
            let is_sui_coin_first = job.is_sui_coin_first.unwrap_or(false);

            let pool_object = get_object(&pool_id).await?;
            let pool_object_arg = ObjectArg::SharedObject {
                id: pool_object.object_id,
                initial_shared_version: match pool_object.owner {
                    Some(Owner::Shared {
                        initial_shared_version,
                    }) => initial_shared_version,
                    _ => SequenceNumber::from_u64(0),
                },
                mutable: true,
            };
            let nft_object = get_object(&nft_object_id).await?;

            let nft_object_arg = ObjectArg::ImmOrOwnedObject((
                nft_object.object_id,
                nft_object.version,
                nft_object.digest,
            ));

            let mut ptb = ProgrammableTransactionBuilder::new();
            let admin_wallet =
                SuiWallet::new_from_private_key(&APP_CONFIG.admin_wallet_private_key)?;

            let module = Identifier::new("moonbags")?;
            let function = Identifier::new("burn_turbos_position_nft")?;

            let fee_type_tag = TypeTag::from_str(&TURBOS_FEE_TYPE).unwrap();
            let sui_type_tag = TypeTag::from_str(&SUI_TYPE).unwrap();

            let pool_object = ptb.obj(pool_object_arg)?;
            let configuration = ptb.obj(*MOONBAGS_CONFIGURATION_OBJECT)?;
            let positions = ptb.obj(*TURBOS_POSITIONS_OBJECT)?;
            let nft_object = ptb.obj(nft_object_arg)?;
            let versioned = ptb.obj(*TURBOS_VERSIONED_OBJECT)?;

            let type_arg = if is_sui_coin_first {
                vec![
                    token_type_tag.clone(),
                    sui_type_tag.clone(),
                    token_type_tag.clone(),
                    fee_type_tag.clone(),
                ]
            } else {
                vec![
                    token_type_tag.clone(),
                    token_type_tag.clone(),
                    sui_type_tag.clone(),
                    fee_type_tag.clone(),
                ]
            };

            ptb.command(Command::move_call(
                *MOONBAGS_PACKAGE_ID,
                module,
                function,
                type_arg,
                vec![pool_object, configuration, positions, nft_object, versioned],
            ));

            let builder: ProgrammableTransaction = ptb.finish();

            let tx_hash = execute_transaction_with_admin_wallet(builder, admin_wallet).await?;
            tracing::debug!("burn_position tx {}", tx_hash);
            Ok(tx_hash)
        }
    }
}

async fn setup_tick(
    ptb: &mut ProgrammableTransactionBuilder,
) -> anyhow::Result<(Argument, Argument, Argument, Argument)> {
    let tick_lower_index = ptb.pure(TICK_INDEX)?;
    let tick_lower_index_is_negative = ptb.pure(true)?;
    let tick_upper_index = ptb.pure(TICK_INDEX)?;
    let tick_upper_index_is_negative = ptb.pure(false)?;
    Ok((
        tick_lower_index,
        tick_lower_index_is_negative,
        tick_upper_index,
        tick_upper_index_is_negative,
    ))
}

fn setup_params(
    ptb: &mut ProgrammableTransactionBuilder,
    admin_address: SuiAddress,
) -> anyhow::Result<(Argument, Argument, Argument, Argument)> {
    let recipient = ptb.pure(admin_address)?;
    let deadline = ptb.pure(Utc::now().timestamp_millis() + DEFAULT_DEADLINE_MS)?;
    let clock = ptb.input(CallArg::CLOCK_IMM)?;
    let versioned = ptb.obj(*TURBOS_VERSIONED_OBJECT)?;
    Ok((recipient, deadline, clock, versioned))
}

fn calculate_swap_amount(
    amount_a_in_pool: u64,
    amount_b_in_pool: u64,
    is_sui_coin_first: bool,
    sui_amount: u64,
    token_amount: u64,
) -> anyhow::Result<(bool, f64, f64)> {
    let (sui_amount_exist_in_pool, token_amount_exist_in_pool) = if is_sui_coin_first {
        (amount_a_in_pool, amount_b_in_pool)
    } else {
        (amount_b_in_pool, amount_a_in_pool)
    };

    let ratio_add_liquidity = token_amount as f64 / sui_amount as f64;
    let amount_sui_to_increase =
        (sui_amount_exist_in_pool as f64 * token_amount_exist_in_pool as f64 / ratio_add_liquidity)
            .sqrt()
            - sui_amount_exist_in_pool as f64;

    let amount_token_to_increase = ratio_add_liquidity
        * (sui_amount_exist_in_pool as f64 * token_amount_exist_in_pool as f64
            / ratio_add_liquidity)
            .sqrt()
        - token_amount_exist_in_pool as f64;

    let is_swap_sui_to_token = amount_sui_to_increase > 0.0 && amount_token_to_increase < 0.0;

    Ok((
        is_swap_sui_to_token,
        amount_sui_to_increase,
        amount_token_to_increase,
    ))
}

#[allow(clippy::too_many_arguments)]
fn build_swap_command(
    ptb: &mut ProgrammableTransactionBuilder,
    is_a_to_b: bool,
    coin_arg: Argument,
    amount_in: Argument,
    amount_out_min: Argument,
    recipient: Argument,
    deadline: Argument,
    clock: Argument,
    versioned: Argument,
    pool_object_arg: Argument,
    type_a: TypeTag,
    type_b: TypeTag,
    fee_type: TypeTag,
) -> anyhow::Result<()> {
    let swap_module = Identifier::new("swap_router")?;
    let swap_function = if is_a_to_b {
        Identifier::new("swap_a_b")?
    } else {
        Identifier::new("swap_b_a")?
    };

    let sqrt_price = if is_a_to_b {
        ptb.pure(4295048016u128)?
    } else {
        ptb.pure(79226673515401279992447579055u128)?
    };

    let type_tags = [type_a, type_b, fee_type];

    let is_exact_in = ptb.pure(true)?;

    ptb.command(Command::move_call(
        *TURBOS_FINANCE_CLMM_PACKAGE_ID,
        swap_module,
        swap_function,
        type_tags.to_vec(),
        vec![
            pool_object_arg,
            coin_arg,
            amount_in,
            amount_out_min,
            sqrt_price,
            is_exact_in,
            recipient,
            deadline,
            clock,
            versioned,
        ],
    ));

    Ok(())
}

pub fn get_amount_in_pool(pool_object_data: &SuiObjectData) -> anyhow::Result<(u64, u64, u64)> {
    let fields_map = match pool_object_data.clone().content.unwrap() {
        SuiParsedData::MoveObject(obj) => match obj.fields {
            SuiMoveStruct::WithFields(fields) => fields,
            _ => return Err(anyhow::anyhow!("Pool data is not a WithFields")),
        },
        _ => return Err(anyhow::anyhow!("Pool data is not a MoveObject")),
    };

    let amount_a_in_pool = fields_map
        .get("coin_a")
        .unwrap()
        .to_string()
        .parse::<u64>()?;
    let amount_b_in_pool = fields_map
        .get("coin_b")
        .unwrap()
        .to_string()
        .parse::<u64>()?;
    let liquidity = fields_map
        .get("liquidity")
        .unwrap()
        .to_string()
        .parse::<u64>()?;

    Ok((amount_a_in_pool, amount_b_in_pool, liquidity))
}

#[allow(clippy::too_many_arguments)]
async fn build_add_liquidity_ptb(
    pool_object: ObjectArg,
    is_swap_sui_to_token: bool,
    amount_sui_to_increase: f64,
    amount_token_to_increase: f64,
    token_amount: u64,
    sui_amount: u64,
    token_type_tag: TypeTag,
    is_sui_coin_first: bool,
    admin_address: SuiAddress,
    amount_out_min: Option<u64>,
) -> anyhow::Result<ProgrammableTransaction> {
    let mut ptb = ProgrammableTransactionBuilder::new();
    let module = Identifier::new("position_manager")?;
    let function = Identifier::new("mint")?;
    let sui_type_tag = TypeTag::from_str(&SUI_TYPE).unwrap();
    let fee_type_tag = TypeTag::from_str(&TURBOS_FEE_TYPE).unwrap();
    let pool_object_arg = ptb.obj(pool_object)?;
    let positions = ptb.obj(*TURBOS_POSITIONS_OBJECT)?;

    if is_swap_sui_to_token {
        let sui_amount_after_swap = sui_amount - amount_sui_to_increase.ceil() as u64;
        let vec_sui_coin = prepare_sui_coins(&mut ptb, sui_amount_after_swap).await?;
        let vec_sui_coin_swap = prepare_sui_coins(&mut ptb, amount_sui_to_increase as u64).await?;
        let vec_token_coin = prepare_token_coins(
            &mut ptb,
            admin_address,
            token_type_tag.clone(),
            token_amount,
        )
        .await?;

        let tick_lower_index = ptb.pure(TICK_INDEX)?;
        let tick_lower_index_is_negative = ptb.pure(true)?;
        let tick_upper_index = ptb.pure(TICK_INDEX)?;
        let tick_upper_index_is_negative = ptb.pure(false)?;

        let token_amount = ptb.pure(token_amount)?;
        let sui_amount = ptb.pure(sui_amount_after_swap)?;
        let token_amount_min = ptb.pure(0_u64)?;
        let sui_amount_in = ptb.pure(0_u64)?;

        let (recipient, deadline, clock, versioned) = setup_params(&mut ptb, admin_address)?;

        let amount_in_swap = ptb.pure(amount_sui_to_increase as u64)?;
        let amount_out_min = match amount_out_min {
            Some(amount) => ptb.pure(amount)?,
            None => ptb.pure(0_u64)?,
        };

        if is_sui_coin_first {
            let _ = build_swap_command(
                &mut ptb,
                true,
                vec_sui_coin_swap,
                amount_in_swap,
                amount_out_min,
                recipient,
                deadline,
                clock,
                versioned,
                pool_object_arg,
                sui_type_tag.clone(),
                token_type_tag.clone(),
                fee_type_tag.clone(),
            );

            ptb.command(Command::move_call(
                *TURBOS_FINANCE_CLMM_PACKAGE_ID,
                module,
                function,
                vec![sui_type_tag, token_type_tag.clone(), fee_type_tag],
                vec![
                    pool_object_arg,
                    positions,
                    vec_sui_coin,
                    vec_token_coin,
                    tick_lower_index,
                    tick_lower_index_is_negative,
                    tick_upper_index,
                    tick_upper_index_is_negative,
                    sui_amount,
                    token_amount,
                    sui_amount_in,
                    token_amount_min,
                    recipient,
                    deadline,
                    clock,
                    versioned,
                ],
            ));
        } else {
            let _ = build_swap_command(
                &mut ptb,
                false,
                vec_sui_coin_swap,
                amount_in_swap,
                amount_out_min,
                recipient,
                deadline,
                clock,
                versioned,
                pool_object_arg,
                token_type_tag.clone(),
                sui_type_tag.clone(),
                fee_type_tag.clone(),
            );

            ptb.command(Command::move_call(
                *TURBOS_FINANCE_CLMM_PACKAGE_ID,
                module,
                function,
                vec![token_type_tag.clone(), sui_type_tag, fee_type_tag],
                vec![
                    pool_object_arg,
                    positions,
                    vec_token_coin,
                    vec_sui_coin,
                    tick_lower_index,
                    tick_lower_index_is_negative,
                    tick_upper_index,
                    tick_upper_index_is_negative,
                    token_amount,
                    sui_amount,
                    token_amount_min,
                    sui_amount_in,
                    recipient,
                    deadline,
                    clock,
                    versioned,
                ],
            ));
        }
    } else {
        let vec_sui_coin = prepare_sui_coins(&mut ptb, sui_amount).await?;
        let token_amount_after_swap = token_amount - amount_token_to_increase.abs().floor() as u64;

        let token_coin_argument = prepare_token_coin(
            &mut ptb,
            admin_address,
            token_type_tag.clone(),
            token_amount,
        )
        .await?;

        //token_swap
        let split_token_amount_swap = ptb.pure(amount_token_to_increase.abs().floor() as u64)?;
        let split_token_coin_swap = ptb.command(Command::SplitCoins(
            token_coin_argument,
            vec![split_token_amount_swap],
        ));
        let token_coin_swap_vec =
            ptb.command(Command::MakeMoveVec(None, vec![split_token_coin_swap]));

        //token_add
        let split_token_amount_add = ptb.pure(token_amount_after_swap)?;
        let split_token_coin_vec = ptb.command(Command::SplitCoins(
            token_coin_argument,
            vec![split_token_amount_add],
        ));
        let token_coin_add_vec =
            ptb.command(Command::MakeMoveVec(None, vec![split_token_coin_vec]));

        let (
            tick_lower_index,
            tick_lower_index_is_negative,
            tick_upper_index,
            tick_upper_index_is_negative,
        ) = setup_tick(&mut ptb).await?;

        let token_amount = ptb.pure(token_amount_after_swap)?;
        let sui_amount = ptb.pure(sui_amount)?;
        let token_amount_min = ptb.pure(0_u64)?;
        let sui_amount_in = ptb.pure(0_u64)?;

        let (recipient, deadline, clock, versioned) = setup_params(&mut ptb, admin_address)?;

        let amount_in_swap = ptb.pure(amount_token_to_increase.abs().floor() as u64)?;
        let amount_out_min = ptb.pure(0_u64)?;

        if is_sui_coin_first {
            let _ = build_swap_command(
                &mut ptb,
                false,
                token_coin_swap_vec,
                amount_in_swap,
                amount_out_min,
                recipient,
                deadline,
                clock,
                versioned,
                pool_object_arg,
                sui_type_tag.clone(),
                token_type_tag.clone(),
                fee_type_tag.clone(),
            );

            ptb.command(Command::move_call(
                *TURBOS_FINANCE_CLMM_PACKAGE_ID,
                module,
                function,
                vec![sui_type_tag, token_type_tag.clone(), fee_type_tag],
                vec![
                    pool_object_arg,
                    positions,
                    vec_sui_coin,
                    token_coin_add_vec,
                    tick_lower_index,
                    tick_lower_index_is_negative,
                    tick_upper_index,
                    tick_upper_index_is_negative,
                    sui_amount,
                    token_amount,
                    sui_amount_in,
                    token_amount_min,
                    recipient,
                    deadline,
                    clock,
                    versioned,
                ],
            ));
        } else {
            let _ = build_swap_command(
                &mut ptb,
                true,
                token_coin_swap_vec,
                amount_in_swap,
                amount_out_min,
                recipient,
                deadline,
                clock,
                versioned,
                pool_object_arg,
                token_type_tag.clone(),
                sui_type_tag.clone(),
                fee_type_tag.clone(),
            );

            ptb.command(Command::move_call(
                *TURBOS_FINANCE_CLMM_PACKAGE_ID,
                module,
                function,
                vec![token_type_tag.clone(), sui_type_tag, fee_type_tag],
                vec![
                    pool_object_arg,
                    positions,
                    token_coin_add_vec,
                    vec_sui_coin,
                    tick_lower_index,
                    tick_lower_index_is_negative,
                    tick_upper_index,
                    tick_upper_index_is_negative,
                    token_amount,
                    sui_amount,
                    token_amount_min,
                    sui_amount_in,
                    recipient,
                    deadline,
                    clock,
                    versioned,
                ],
            ));
        }
    }
    let builder: ProgrammableTransaction = ptb.finish();
    Ok(builder)
}
