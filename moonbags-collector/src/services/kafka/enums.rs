use serde::{Deserialize, Serialize};
use strum_macros::{Display, EnumString, VariantNames};
#[derive(
    Debug,
    Serialize,
    Deserialize,
    Clone,
    Copy,
    EnumString,
    VariantNames,
    Display,
    PartialEq,
    Eq,
    Hash,
)]
#[serde(rename_all = "kebab-case")]
#[strum(serialize_all = "kebab-case")]
pub enum KafkaTopic {
    #[serde(rename = "mcap.updated")]
    #[strum(serialize = "mcap.updated")]
    UpdatedMcap,

    #[serde(rename = "mcap.change")]
    #[strum(serialize = "mcap.change")]
    McapChange,

    #[serde(rename = "coin.created")]
    #[strum(serialize = "coin.created")]
    CreatedCoin,

    #[serde(rename = "trade.created")]
    #[strum(serialize = "trade.created")]
    CreatedTrade,

    #[serde(rename = "coin.updated")]
    #[strum(serialize = "coin.updated")]
    Updated<PERSON><PERSON><PERSON>,

    #[serde(rename = "holder.updated")]
    #[strum(serialize = "holder.updated")]
    UpdatedHolder,
}
