use futures::StreamExt;
use rdkafka::producer::FutureRecord;
use rdkafka::{producer::FutureProducer, ClientConfig};
use serde::Serialize;
use std::sync::LazyLock;
use std::time::{Duration, Instant};

use crate::config::APP_CONFIG;

use super::enums::KafkaTopic;

pub const KAFKA_TOPIC_PREFIX: &str = "moonbags.collector";

pub static KAFKA_PRODUCER: LazyLock<FutureProducer> = LazyLock::new(|| {
    let mut binding = ClientConfig::new();
    let config = binding
        .set("bootstrap.servers", APP_CONFIG.kafka_brokers.join(","))
        .set("message.timeout.ms", "10000")
        .set("acks", "all") // Ensure all replicas acknowledge
        .set("retries", "5") // Retry sending messages up to 5 times
        .set("enable.idempotence", "true") // Enable idempotence
        .set("retry.backoff.ms", "100"); // Wait 100 ms before retrying

    let is_ssl_enabled = APP_CONFIG.kafka_ssl_enabled;
    // If SSL is enabled, set the SSL options
    if is_ssl_enabled {
        config.set("security.protocol", "SASL_SSL");

        let sasl_username = APP_CONFIG.kafka_sasl_username.clone();
        let sasl_password = APP_CONFIG.kafka_sasl_password.clone();
        if !sasl_username.is_empty() && !sasl_password.is_empty() {
            config
                .set("sasl.mechanism", "PLAIN")
                .set("sasl.username", &sasl_username)
                .set("sasl.password", &sasl_password);
        }
    }

    config.create().expect("Producer creation error")
});

pub struct KafkaService;

impl KafkaService {
    pub async fn publish_messages<T: Serialize + Clone>(
        topic: KafkaTopic,
        kafka_records: &Vec<T>,
    ) -> anyhow::Result<()> {
        let _start = Instant::now();
        let topic_name = format!("{}.{}", KAFKA_TOPIC_PREFIX, topic);

        let results = futures::stream::iter(kafka_records)
            .map(|record| {
                let payload = serde_json::to_string(record).expect("Failed to serialize record");
                let topic = topic_name.clone();
                async move {
                    let record: FutureRecord<'_, String, String> =
                        FutureRecord::to(&topic).payload(&payload);
                    KAFKA_PRODUCER.send(record, Duration::from_secs(0)).await
                }
            })
            .buffer_unordered(100000)
            .collect::<Vec<_>>()
            .await;

        for result in results {
            if let Err(err) = result {
                tracing::error!("Error sending to Kafka: {:?}", err);
                return Err(err.0.into());
            }
        }

        Ok(())
    }
}
