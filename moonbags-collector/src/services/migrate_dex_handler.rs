use std::{fmt::Display, str::FromStr};

use bson::{doc, DateTime};
use chrono::Utc;
use move_core_types::language_storage::StructTag;
use sui_sdk::types::{base_types::ObjectType, transaction::ObjectArg, TypeTag};

use crate::{
    common::COINS_REPOSITORY,
    db::utils::traits::RepositorySharedMethod,
    utils::sui::{
        turbos_finance::{
            constants::{SUI_TYPE, TURBOS_FEE_TYPE},
            tx::{add_liquidity, check_pool_exists, deploy_pool_and_add_liquidity},
        },
        utils::get_object,
    },
};

use super::{
    coin_handler::{emit_bonding_completed_coin_event, BondingCompletedCoin},
    collect_curve_events::PoolMigratingEvent,
    redis_service::RedisEmitter,
};

pub enum MigrateDex {
    Cetus,
    TurboFinance,
}

impl MigrateDex {
    pub fn from_u8(value: u8) -> Self {
        match value {
            0 => Self::Cetus,
            1 => Self::TurboFinance,
            _ => Self::TurboFinance,
        }
    }
}

impl Display for MigrateDex {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Cetus => write!(f, "Cetus"),
            Self::TurboFinance => write!(f, "TurboFinance"),
        }
    }
}

impl FromStr for MigrateDex {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Cetus" => Ok(Self::Cetus),
            "TurboFinance" => Ok(Self::TurboFinance),
            _ => Err(()),
        }
    }
}

pub async fn handle_migrate_dex_event(
    event: &PoolMigratingEvent,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let sui_amount = event.sui_amount;
    let token_amount = event.token_amount;
    let token_address = event.token_address.clone();
    let token_type_tag = TypeTag::from_str(&token_address).unwrap();

    let pool_id_option = check_pool_exists(
        token_type_tag.clone(),
        TypeTag::from_str(&SUI_TYPE).unwrap(),
        TypeTag::from_str(&TURBOS_FEE_TYPE).unwrap(),
    )
    .await?;

    if let Some(pool_id) = pool_id_option {
        let mut is_sui_coin_first = false;
        let pool_object = get_object(&pool_id).await?;
        if let Some(ObjectType::Struct(move_object_type)) = pool_object.type_ {
            let type_string = move_object_type.to_canonical_string(true);
            if let Ok(struct_tag) = StructTag::from_str(&type_string) {
                let type_params = &struct_tag.type_params;
                if !type_params.is_empty()
                    && type_params[0] == TypeTag::from_str(&SUI_TYPE).unwrap()
                {
                    is_sui_coin_first = true;
                }
            }
        }
        let object_arg = ObjectArg::SharedObject {
            id: pool_object.object_id,
            initial_shared_version: pool_object.version,
            mutable: true,
        };
        add_liquidity(
            object_arg,
            token_type_tag,
            sui_amount,
            token_amount,
            is_sui_coin_first,
            redis_emitter,
        )
        .await?;
    } else {
        deploy_pool_and_add_liquidity(token_type_tag, sui_amount, token_amount).await?;
    }

    Ok(())
}

pub async fn update_listed_pool_id(
    token_address: String,
    pool_address: String,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let event_bonding_completed_coin = BondingCompletedCoin {
        token_address: token_address.clone(),
        listed_pool_id: pool_address.clone(),
        listed_at: Some(Utc::now()),
        bonding_dex: MigrateDex::TurboFinance.to_string().to_string(),
    };

    if let Err(e) = emit_bonding_completed_coin_event(&event_bonding_completed_coin, redis_emitter)
    {
        tracing::error!("Failed to emit bonding completed event to ws server: {}", e);
    }

    let update_object = doc! {
        "$set": {
            "listedPoolId": pool_address,
            "bondingDex": MigrateDex::TurboFinance.to_string(),
            "listedAt": DateTime::from_millis(Utc::now().timestamp_millis()),
        }
    };

    let result = COINS_REPOSITORY
        .update_one(
            doc! {
                "tokenAddress": &token_address,
            },
            update_object,
            None,
        )
        .await;

    let Ok(_) = result else {
        tracing::error!(
            "Failed to update database for coin {}: {:?}",
            token_address,
            result
        );
        return Ok(());
    };
    Ok(())
}
