use std::str::FromStr;

use bson::{doc, <PERSON><PERSON>, Decimal128};
use mongodb::options::FindOneOptions;
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Serialize};
use sui_sdk::{rpc_types::SuiTransactionBlockResponseOptions, types::event::EventID};

use crate::{
    common::{
        get_sui_client_instance, CANDLES_REPOSITORY, COINS_REPOSITORY, TOKEN_PRICES_REPOSITORY,
        TRADES_REPOSITORY,
    },
    db::{
        candles::{Candle, Candles},
        trades::{ETradeType, Trades},
        utils::traits::RepositorySharedMethod,
    },
    utils::common::{un_decimals_from_str, ToF64, SUI_DECIMALS, TOKEN_DECIMALS},
};

use super::{
    collect_curve_events::TradedEvent,
    kafka::{enums::KafkaTopic, kafka_service::KafkaService},
    redis_service::RedisEmitter,
};

const COIN_TOTAL_SUPPLY: u128 = 1000000000000000;

pub async fn handle_traded_event_for_candles_and_trades(
    event: &TradedEvent,
    event_id: &EventID,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let maker = event.user.clone();
    let token_address = event.token_address.clone();
    let tx_digest = event_id.tx_digest;
    let index = event_id.event_seq;

    let sui_client = get_sui_client_instance();
    let response = sui_client
        .read_api()
        .get_transaction_with_options(tx_digest, SuiTransactionBlockResponseOptions::new())
        .await;
    let checkpoint = response.map(|res| res.checkpoint).unwrap_or_default();

    let (virtual_token_reserves, virtual_sui_reserves) =
        (event.virtual_token_reserves, event.virtual_sui_reserves);

    let virtual_token_reserves_without_decimals =
        un_decimals_from_str(&virtual_token_reserves.to_string(), TOKEN_DECIMALS);
    let virtual_sui_reserves_without_decimals =
        un_decimals_from_str(&virtual_sui_reserves.to_string(), SUI_DECIMALS);
    let base_price_quote =
        virtual_sui_reserves_without_decimals / virtual_token_reserves_without_decimals;

    let quote_price_usd = TOKEN_PRICES_REPOSITORY
        .get_cached_token_price("sui")
        .await
        .map_err(|e| {
            tracing::error!("Failed to get SUI price: {}", e);
            e
        })?;

    let sui_amount_without_decimals =
        un_decimals_from_str(&event.sui_amount.to_string(), SUI_DECIMALS);
    let token_amount_without_decimals =
        un_decimals_from_str(&event.token_amount.to_string(), TOKEN_DECIMALS);

    let base_price_usd = (Decimal::from_f64(quote_price_usd).unwrap()
        * Decimal::from_f64(base_price_quote).unwrap())
    .to_f64();
    let volume_usd = sui_amount_without_decimals * quote_price_usd;

    let to_decimal_u64 = |val: u64| Decimal128::from_str(&val.to_string()).unwrap();

    let Ok(token_symbol) = get_symbol_token(&token_address).await else {
        tracing::error!("Failed to get token symbol: {:?}", token_address);
        return Ok(());
    };

    let fee_without_decimals = un_decimals_from_str(&event.fee.to_string(), SUI_DECIMALS);

    let trade_record = Trades {
        token_address: token_address.clone(),
        token_symbol: token_symbol.clone(),
        quote_amount: sui_amount_without_decimals.to_string(),
        base_amount: token_amount_without_decimals.to_string(),
        trade_type: if event.is_buy {
            ETradeType::Buy
        } else {
            ETradeType::Sell
        },
        maker: maker.clone(),
        timestamp: event.ts,
        virtual_sui_reserves: to_decimal_u64(event.virtual_sui_reserves),
        virtual_token_reserves: to_decimal_u64(event.virtual_token_reserves),
        real_sui_reserves: to_decimal_u64(event.real_sui_reserves),
        real_token_reserves: to_decimal_u64(event.real_token_reserves),
        hash: tx_digest.to_string().clone(),
        index: event_id.event_seq,
        price: base_price_quote.to_string(),
        price_usd: base_price_usd.to_string(),
        volume: sui_amount_without_decimals.to_string(),
        volume_usd: volume_usd.to_string(),
        checkpoint,
        fee: fee_without_decimals.clone().to_string(),
    };

    let pub_trade_data = PubTradesData {
        hash: tx_digest.to_string(),
        token_address: token_address.clone(),
        token_symbol,
        index,
        maker: maker.clone(),
        trade_type: trade_record.trade_type,
        base_amount: trade_record.base_amount.to_string(),
        quote_amount: trade_record.quote_amount.to_string(),
        price: trade_record.price.to_string(),
        price_usd: trade_record.price_usd.to_string(),
        volume: trade_record.volume.to_string(),
        volume_usd: trade_record.volume_usd.to_string(),
        timestamp: event.ts,
        virtual_sui_reserves: trade_record.virtual_sui_reserves.to_string(),
        virtual_token_reserves: trade_record.virtual_token_reserves.to_string(),
        checkpoint,
        real_sui_reserves: trade_record.real_sui_reserves.to_string(),
        real_token_reserves: trade_record.real_token_reserves.to_string(),
        fee: fee_without_decimals.to_string(),
    };

    redis_emitter.emit_room(
        &format!("SUBSCRIBE_COIN::{}", event.token_address),
        "CreatedTrade",
        &serde_json::to_string(&pub_trade_data)?,
    );

    redis_emitter.emit_room(
        "SUBSCRIBE_TRADES",
        "CreatedTrade",
        &serde_json::to_string(&pub_trade_data)?,
    );

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::CreatedTrade, &vec![pub_trade_data]).await
    {
        tracing::error!("Failed to publish trade to Kafka: {}", e);
    }

    match TRADES_REPOSITORY
        .insert_one_or_ignore(trade_record, None)
        .await
    {
        Ok(Some(_)) => {
            handle_update_candle_per_swap(
                token_address,
                String::from("sui"),
                event.ts,
                base_price_quote,
                base_price_usd,
                token_amount_without_decimals,
                sui_amount_without_decimals,
            )
            .await?;
        }
        Ok(None) => {
            tracing::info!("Trade record already exists, skipping candle update");
        }
        Err(e) => {
            tracing::error!("Failed to insert trade record: {}", e);
            return Err(e);
        }
    }

    Ok(())
}

async fn get_symbol_token(token_address: &str) -> anyhow::Result<String> {
    let result = COINS_REPOSITORY
        .find_one(
            doc! {
                "tokenAddress": token_address
            },
            None,
        )
        .await;

    if let Ok(Some(coin)) = result {
        return Ok(coin.symbol.unwrap());
    }

    let sui_client = get_sui_client_instance();
    let token_info = sui_client
        .coin_read_api()
        .get_coin_metadata(token_address.to_string())
        .await?;

    match token_info {
        Some(token_info) => Ok(token_info.symbol),
        None => Err(anyhow::anyhow!("Token not found")),
    }
}

#[allow(clippy::too_many_arguments)]
async fn update_candle_per_swap(
    base_token: String,
    _quote_token: String,
    timestamp: u64,
    price: f64,
    price_usd: f64,
    base_amount_without_decimals: f64,
    quote_amount_without_decimals: f64,
) -> anyhow::Result<()> {
    let resolution_options = [1, 60, 900, 3600];
    let resolution_ms_options = resolution_options
        .into_iter()
        .map(|r| r * 1000)
        .collect::<Vec<u64>>();
    let volume_base_f64 = base_amount_without_decimals;
    let volume_quote_f64 = quote_amount_without_decimals;
    let volume_usd_f64 = volume_base_f64 * price_usd;

    let volume_base = Decimal128::from_str(&volume_base_f64.to_string()).unwrap();
    let volume_quote = Decimal128::from_str(&volume_quote_f64.to_string()).unwrap();
    let volume_usd = Decimal128::from_str(&volume_usd_f64.to_string()).unwrap();

    let coin = COINS_REPOSITORY
        .find_one(
            doc! {
                "tokenAddress": &base_token,
            },
            None,
        )
        .await?
        .ok_or_else(|| anyhow::anyhow!("Coin not found"))?;
    let token_base_supply = un_decimals_from_str(&COIN_TOTAL_SUPPLY.to_string(), TOKEN_DECIMALS);

    let market_cap = token_base_supply * price;
    let market_cap_usd = token_base_supply * price_usd;

    for resolution in resolution_ms_options {
        let resolution_timestamp = timestamp - (timestamp % resolution);

        let options = FindOneOptions::builder()
            .sort(doc! { "timestamp": -1_i32 })
            .build();

        let pre_candle = CANDLES_REPOSITORY
            .find_one(
                doc! {"tokenAddress": base_token.clone(), "resolution": resolution as u32},
                options,
            )
            .await?;

        let mut price_low = price;
        let mut price_high = price;
        let mut price_open = price;

        let mut price_usd_low = price_usd;
        let mut price_usd_high = price_usd;
        let mut price_usd_open = price_usd;

        let mut market_cap_low = market_cap;
        let mut market_cap_high = market_cap;
        let mut market_cap_open = market_cap;

        let mut market_cap_usd_low = market_cap_usd;
        let mut market_cap_usd_high = market_cap_usd;
        let mut market_cap_usd_open = market_cap_usd;

        // Case 1
        // when candle exist
        if let Some(candle) = pre_candle
            .clone()
            .filter(|c| c.timestamp == resolution_timestamp)
        {
            let update_doc = doc! {
                "$set": {
                    "price": {
                        "low": Decimal128::from_str(&(price.min(candle.price.low.to_string().parse::<f64>().unwrap_or(price)).to_string())).unwrap(),
                        "high": Decimal128::from_str(&(price.max(candle.price.high.to_string().parse::<f64>().unwrap_or(price)).to_string())).unwrap(),
                        "open": Decimal128::from_str(&candle.price.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&price.to_string()).unwrap(),
                    },
                    "priceUsd": {
                        "low": Decimal128::from_str(&(price_usd.min(candle.price_usd.low.to_string().parse::<f64>().unwrap_or(price_usd)).to_string())).unwrap(),
                        "high": Decimal128::from_str(&(price_usd.max(candle.price_usd.high.to_string().parse::<f64>().unwrap_or(price_usd)).to_string())).unwrap(),
                        "open": Decimal128::from_str(&candle.price_usd.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&price_usd.to_string()).unwrap(),
                    },
                    "marketCap": {
                        "low": Decimal128::from_str(&(market_cap.min(candle.market_cap.low.to_string().parse::<f64>().unwrap_or(market_cap)).to_string())).unwrap(),
                        "high": Decimal128::from_str(&(market_cap.max(candle.market_cap.high.to_string().parse::<f64>().unwrap_or(market_cap)).to_string())).unwrap(),
                        "open": Decimal128::from_str(&candle.market_cap.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&market_cap.to_string()).unwrap(),
                    },
                    "marketCapUsd": {
                        "low": Decimal128::from_str(&(market_cap_usd.min(candle.market_cap_usd.low.to_string().parse::<f64>().unwrap_or(market_cap_usd)).to_string())).unwrap(),
                        "high": Decimal128::from_str(&(market_cap_usd.max(candle.market_cap_usd.high.to_string().parse::<f64>().unwrap_or(market_cap_usd)).to_string())).unwrap(),
                        "open": Decimal128::from_str(&candle.market_cap_usd.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&market_cap_usd.to_string()).unwrap(),
                    },
                    "volumeQuote": (candle.volume_quote.parse::<f64>().unwrap_or(0.0) + volume_quote_f64).to_string(),
                    "volumeBase": (candle.volume_base.parse::<f64>().unwrap_or(0.0) + volume_base_f64).to_string(),
                    "volumeUsd": (candle.volume_usd.parse::<f64>().unwrap_or(0.0) + volume_usd_f64).to_string(),
                }
            };

            CANDLES_REPOSITORY
                .update_one(doc! {"tokenAddress": base_token.clone(), "resolution": resolution as u32, "timestamp": Bson::Int64(resolution_timestamp as i64)}, update_doc, None)
                .await
                .map_err(|err| tracing::error!("Error updating candle: {:#?}", err))
                .unwrap();

            continue;
        }

        // Case 2
        // when have prev candle and update new candle base on prev candle to create
        if let Some(candle) = pre_candle
            .clone()
            .filter(|c| c.timestamp != resolution_timestamp)
        {
            price_low = price.min(
                candle
                    .price
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(price),
            );
            price_high = price_high.max(
                candle
                    .price
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(price_high),
            );
            price_open = candle
                .price
                .close
                .to_string()
                .parse::<f64>()
                .unwrap_or(price_open);

            price_usd_low = price_usd.min(
                candle
                    .price_usd
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(price_usd),
            );
            price_usd_high = price_usd_high.max(
                candle
                    .price_usd
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(price_usd_high),
            );
            price_usd_open = candle
                .price_usd
                .close
                .to_string()
                .parse::<f64>()
                .unwrap_or(price_usd_open);

            market_cap_low = market_cap.min(
                candle
                    .market_cap
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(market_cap),
            );
            market_cap_high = market_cap_high.max(
                candle
                    .market_cap
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(market_cap_high),
            );
            market_cap_open = candle
                .market_cap
                .close
                .to_string()
                .parse::<f64>()
                .unwrap_or(market_cap_open);

            market_cap_usd_low = market_cap_usd.min(
                candle
                    .market_cap_usd
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(market_cap_usd),
            );
            market_cap_usd_high = market_cap_usd_high.max(
                candle
                    .market_cap_usd
                    .close
                    .to_string()
                    .parse::<f64>()
                    .unwrap_or(market_cap_usd_high),
            );
            market_cap_usd_open = candle
                .market_cap_usd
                .close
                .to_string()
                .parse::<f64>()
                .unwrap_or(market_cap_usd_open);
        }

        // Case 3
        // Not exist candle
        if pre_candle.is_none() {
            let init_virtual_sui_reserves = coin
                .init_virtual_sui_reserves
                .unwrap_or(Decimal128::from_str("0").unwrap())
                .to_f64();
            let init_virtual_token_reserves = coin
                .init_virtual_token_reserves
                .unwrap_or(Decimal128::from_str("0").unwrap())
                .to_f64();

            let init_virtual_sui_reserves_without_decimals =
                un_decimals_from_str(&init_virtual_sui_reserves.to_string(), SUI_DECIMALS);

            let init_virtual_token_reserves_without_decimals =
                un_decimals_from_str(&init_virtual_token_reserves.to_string(), TOKEN_DECIMALS);

            let init_token_price_sui = init_virtual_sui_reserves_without_decimals
                / init_virtual_token_reserves_without_decimals;

            // sui price usd
            let sui_price_usd = price_usd / price;

            // update low and open price by init price
            price_low = init_token_price_sui;
            price_open = init_token_price_sui;

            price_usd_low = init_token_price_sui * sui_price_usd;
            price_usd_open = init_token_price_sui * sui_price_usd;
        }

        // when new candle crate
        // For 2 case:
        // - Not exist candle (Case 3)
        // - Have prev candle and update new candle (Case 2)
        let new_candle = Candles {
            token_address: base_token.clone(),
            timestamp: resolution_timestamp,
            resolution: resolution as u32,
            price: Candle {
                low: Decimal128::from_str(&price_low.to_string()).unwrap(),
                high: Decimal128::from_str(&price_high.to_string()).unwrap(),
                open: Decimal128::from_str(&price_open.to_string()).unwrap(),
                close: Decimal128::from_str(&price.to_string()).unwrap(),
            },
            price_usd: Candle {
                low: Decimal128::from_str(&price_usd_low.to_string()).unwrap(),
                high: Decimal128::from_str(&price_usd_high.to_string()).unwrap(),
                open: Decimal128::from_str(&price_usd_open.to_string()).unwrap(),
                close: Decimal128::from_str(&price_usd.to_string()).unwrap(),
            },
            market_cap: Candle {
                low: Decimal128::from_str(&market_cap_low.to_string()).unwrap(),
                high: Decimal128::from_str(&market_cap_high.to_string()).unwrap(),
                open: Decimal128::from_str(&market_cap_open.to_string()).unwrap(),
                close: Decimal128::from_str(&market_cap.to_string()).unwrap(),
            },
            market_cap_usd: Candle {
                low: Decimal128::from_str(&market_cap_usd_low.to_string()).unwrap(),
                high: Decimal128::from_str(&market_cap_usd_high.to_string()).unwrap(),
                open: Decimal128::from_str(&market_cap_usd_open.to_string()).unwrap(),
                close: Decimal128::from_str(&market_cap_usd.to_string()).unwrap(),
            },
            volume_quote: volume_quote.to_string(),
            volume_base: volume_base.to_string(),
            volume_usd: volume_usd.to_string(),
        };

        match CANDLES_REPOSITORY
            .insert_one_or_ignore(&new_candle, None)
            .await
        {
            Ok(_) => continue,
            Err(err) => {
                return Err(err);
            }
        }
    }

    Ok(())
}

#[allow(clippy::too_many_arguments)]
async fn handle_update_candle_per_swap(
    base_token: String,
    quote_token: String,
    timestamp: u64,
    price: f64,
    price_usd: f64,
    base_amount_without_decimals: f64,
    quote_amount_without_decimals: f64,
) -> anyhow::Result<()> {
    const MAX_RETRY_COUNT: u32 = 5;

    let mut retry_count = 0;
    while retry_count <= MAX_RETRY_COUNT {
        match update_candle_per_swap(
            base_token.clone(),
            quote_token.clone(),
            timestamp,
            price,
            price_usd,
            base_amount_without_decimals,
            quote_amount_without_decimals,
        )
        .await
        {
            Ok(_) => break,
            Err(err) => {
                if !err.to_string().to_lowercase().contains("duplicate key") {
                    tracing::error!("Error when updating candle per swap: {:#?}", err);
                    break;
                }

                retry_count += 1;

                if retry_count == MAX_RETRY_COUNT {
                    tracing::error!(
                        "handle_update_candle_per_swap too many retries, token address: {:?}",
                        base_token
                    );
                }

                tokio::time::sleep(tokio::time::Duration::from_millis(15)).await;
            }
        }
    }

    Ok(())
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubTradesData {
    pub checkpoint: Option<u64>,
    pub timestamp: u64,
    pub hash: String,
    pub token_address: String,
    pub token_symbol: String,
    pub index: u64,
    pub maker: String,
    pub trade_type: ETradeType,
    pub base_amount: String,
    pub quote_amount: String,
    pub price: String,
    pub price_usd: String,
    pub volume: String,
    pub volume_usd: String,
    pub virtual_sui_reserves: String,
    pub virtual_token_reserves: String,
    pub real_sui_reserves: String,
    pub real_token_reserves: String,
    pub fee: String,
}
