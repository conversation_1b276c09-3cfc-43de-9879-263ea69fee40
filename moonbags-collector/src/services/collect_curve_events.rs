use bson::doc;
use serde::{Deserialize, Serialize};
use serde_with::serde_as;
use serde_with::DisplayFromStr;
use std::time::Duration;
use sui_sdk::{
    rpc_types::{EventFilter, SuiEvent},
    types::event::EventID,
};
use tokio::time::sleep;

use crate::services::coin_handler::handle_traded_event_for_coin;
use crate::services::migrate_dex_handler::handle_migrate_dex_event;
use crate::services::trade_handler::handle_traded_event_for_candles_and_trades;
use crate::utils::common::{deserialize_sui_address, deserialize_token_type};
use crate::{
    common::{get_sui_client_instance, LATEST_SIGNATURES_REPOSITORY},
    config::APP_CONFIG,
    db::latest_signatures::EServiceName,
    services::{
        coin_handler::{handle_complete_event_for_coin, handle_create_event_for_coin},
        holder_handler::handle_traded_event_for_holder,
    },
};

use super::redis_service::RedisEmitter;

pub async fn collect_curve_events() -> anyhow::Result<()> {
    let sui_client = get_sui_client_instance();
    let redis_emitter = RedisEmitter::new()?;

    let limit = None;
    let next_cursor_record = LATEST_SIGNATURES_REPOSITORY
        .find_one_by_service_name(&EServiceName::CollectCurveEvent)
        .await?;
    let mut next_cursor = match next_cursor_record {
        Some(x) => Some(serde_json::from_str::<EventID>(&x.signature)?),
        None => None,
    };

    // first, I want to try any filter, but it doesn't work
    // so
    let filter = EventFilter::MoveEventModule {
        package: APP_CONFIG.bonding_event_package,
        module: APP_CONFIG.event_module.clone(),
    };

    loop {
        // next_cursor is exclusive
        let result = sui_client
            .event_api()
            .query_events(filter.clone(), next_cursor, limit, false)
            .await;

        let Ok(events) = result else {
            tracing::error!("Failed to query events: {:?}", result.err());
            sleep(Duration::from_millis(10)).await;
            continue;
        };

        if events.data.is_empty() {
            tracing::info!("no events, sleep for 800ms");
            sleep(Duration::from_millis(10)).await;
            continue;
        }
        if events.next_cursor.is_none() {
            tracing::info!("no more events, lazy exit now");
            break;
        }

        // check diff now with timestamMs in events
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        let diff = now - events.data[0].timestamp_ms.unwrap();
        tracing::info!("======= diff: {:?}", diff);

        process_events(&events.data, &redis_emitter).await?;
        next_cursor = events.next_cursor;
        sleep(Duration::from_millis(10)).await;
    }

    Ok(())
}

async fn process_events(events: &[SuiEvent], redis_emitter: &RedisEmitter) -> anyhow::Result<()> {
    let grouped_events: Vec<Vec<&SuiEvent>> = group_events_by_tx_digest(events);

    for event_group in grouped_events {
        for event in event_group {
            match event.type_.name.as_str() {
                "CreatedEvent" => {
                    let created_event =
                        serde_json::from_value::<CreatedEvent>(event.parsed_json.clone())?;
                    tracing::info!("Processed create event: {:?}", created_event);
                    handle_create_event_for_coin(&created_event, redis_emitter).await?;
                }
                "CreatedEventV2" => {
                    let created_event =
                        serde_json::from_value::<CreatedEvent>(event.parsed_json.clone())?;
                    tracing::info!("Processed create event: {:?}", created_event);
                    handle_create_event_for_coin(&created_event, redis_emitter).await?;
                }
                "TradedEvent" => {
                    let traded_event =
                        serde_json::from_value::<TradedEvent>(event.parsed_json.clone())?;

                    tracing::info!("Processed trade event: {:?}", traded_event);

                    handle_traded_event_for_holder(
                        &traded_event,
                        event.id.tx_digest.to_string(),
                        redis_emitter,
                    )
                    .await?;
                    handle_traded_event_for_candles_and_trades(
                        &traded_event,
                        &event.id,
                        redis_emitter,
                    )
                    .await?;
                    handle_traded_event_for_coin(&traded_event, redis_emitter).await?;
                }
                "TradedEventV2" => {
                    let traded_event =
                        serde_json::from_value::<TradedEvent>(event.parsed_json.clone())?;

                    tracing::info!("Processed trade event: {:?}", traded_event);

                    handle_traded_event_for_holder(
                        &traded_event,
                        event.id.tx_digest.to_string(),
                        redis_emitter,
                    )
                    .await?;
                    handle_traded_event_for_candles_and_trades(
                        &traded_event,
                        &event.id,
                        redis_emitter,
                    )
                    .await?;
                    handle_traded_event_for_coin(&traded_event, redis_emitter).await?;
                }
                "ConfigChangedEventV2" => {
                    let config_event =
                        serde_json::from_value::<ConfigChangedEvent>(event.parsed_json.clone())?;
                    tracing::info!("Processed config change event: {:?}", config_event);
                }
                "OwnershipTransferredEventV2" => {
                    let ownership_event = serde_json::from_value::<OwnershipTransferredEvent>(
                        event.parsed_json.clone(),
                    )?;
                    tracing::info!("Processed ownership transfer event: {:?}", ownership_event);
                }
                "PoolCompletedEvent" => {
                    let pool_event =
                        serde_json::from_value::<PoolCompletedEvent>(event.parsed_json.clone())?;
                    tracing::info!("Processed pool completion event: {:?}", pool_event);
                    handle_complete_event_for_coin(
                        &pool_event,
                        &event.id.tx_digest.to_string(),
                        redis_emitter,
                    )
                    .await?;
                }
                "PoolCompletedEventV2" => {
                    let pool_event =
                        serde_json::from_value::<PoolCompletedEvent>(event.parsed_json.clone())?;
                    tracing::info!("Processed pool completion event: {:?}", pool_event);
                    handle_complete_event_for_coin(
                        &pool_event,
                        &event.id.tx_digest.to_string(),
                        redis_emitter,
                    )
                    .await?;
                }
                "PoolMigratingEvent" => {
                    let pool_event =
                        serde_json::from_value::<PoolMigratingEvent>(event.parsed_json.clone())?;
                    tracing::info!("Processed pool migration event: {:?}", pool_event);
                    if let Err(e) = handle_migrate_dex_event(&pool_event, redis_emitter).await {
                        tracing::error!("Failed to handle migrate dex event: {:?}", e);
                    }
                }
                _ => {
                    tracing::warn!("unknown event type: {:?}", event.type_.name);
                }
            }

            LATEST_SIGNATURES_REPOSITORY
                .upsert_latest_signature(
                    &EServiceName::CollectCurveEvent,
                    &serde_json::to_string(&event.id)?,
                )
                .await?;
        }
    }
    Ok(())
}

fn group_events_by_tx_digest(events: &[SuiEvent]) -> Vec<Vec<&SuiEvent>> {
    let mut grouped_events: Vec<Vec<&SuiEvent>> = Vec::new();

    for event in events {
        let digest = event.id.tx_digest.to_string();

        if grouped_events.is_empty() {
            grouped_events.push(vec![event]);
            continue;
        }

        let last_group = grouped_events.last_mut().unwrap();
        let last_event = last_group.last().unwrap();
        if last_event.id.tx_digest.to_string() == digest {
            // move CreatedEvent to the front of the array
            if event.type_.name.as_str() == "CreatedEventV2"
                || event.type_.name.as_str() == "CreatedEvent"
            {
                last_group.insert(0, event);
                continue;
            }
            last_group.push(event);
        } else {
            grouped_events.push(vec![event]);
        }
    }

    grouped_events
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct ConfigChangedEvent {
    #[serde_as(as = "DisplayFromStr")]
    pub old_platform_fee: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub new_platform_fee: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub old_graduated_fee: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub new_graduated_fee: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub old_initial_virtual_token_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub new_initial_virtual_token_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub old_remain_token_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub new_remain_token_reserves: u64,
    pub old_token_decimals: u8,
    pub new_token_decimals: u8,
    pub old_init_platform_fee_withdraw: u16,
    pub new_init_platform_fee_withdraw: u16,
    pub old_init_creator_fee_withdraw: u16,
    pub new_init_creator_fee_withdraw: u16,
    pub old_init_stake_fee_withdraw: u16,
    pub new_init_stake_fee_withdraw: u16,
    pub old_init_platform_stake_fee_withdraw: u16,
    pub new_init_platform_stake_fee_withdraw: u16,
    pub old_token_platform_type_name: String,
    pub new_token_platform_type_name: String,
    #[serde_as(as = "DisplayFromStr")]
    pub ts: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct OwnershipTransferredEvent {
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub old_admin: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub new_admin: String,
    #[serde_as(as = "DisplayFromStr")]
    pub ts: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct PoolCompletedEvent {
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    pub lp: String,
    #[serde_as(as = "DisplayFromStr")]
    pub ts: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct PoolMigratingEvent {
    pub bonding_dex: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub sui_amount: u64,
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde_as(as = "DisplayFromStr")]
    pub token_amount: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub ts: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct TradedEvent {
    pub is_buy: bool,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub user: String,
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    #[serde_as(as = "DisplayFromStr")]
    pub sui_amount: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub token_amount: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub virtual_sui_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub virtual_token_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub real_sui_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub real_token_reserves: u64,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub pool_id: String,
    #[serde_as(as = "DisplayFromStr")]
    pub fee: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub ts: u64,
}

#[serde_as]
#[derive(Debug, Serialize, Deserialize)]
pub struct CreatedEvent {
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub description: String,
    pub twitter: String,
    pub telegram: String,
    pub website: String,
    #[serde(deserialize_with = "deserialize_token_type")]
    pub token_address: String,
    pub bonding_curve: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub pool_id: String,
    #[serde(deserialize_with = "deserialize_sui_address")]
    pub created_by: String,
    #[serde_as(as = "DisplayFromStr")]
    pub virtual_sui_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub virtual_token_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub real_sui_reserves: u64,
    #[serde_as(as = "DisplayFromStr")]
    pub real_token_reserves: u64,
    pub platform_fee_withdraw: u16,
    pub creator_fee_withdraw: u16,
    pub stake_fee_withdraw: u16,
    pub platform_stake_fee_withdraw: u16,
    #[serde_as(as = "DisplayFromStr")]
    pub threshold: u64,
    pub bonding_dex: Option<u8>,
    #[serde_as(as = "DisplayFromStr")]
    pub ts: u64,
}
