use std::str::FromStr;

use bson::{doc, DateTime, Decimal128};
use chrono::Utc;
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Serialize};
use sui_sdk::types::digests::TransactionDigest;

use crate::{
    common::{get_sui_client_instance, COINS_REPOSITORY, TOKEN_PRICES_REPOSITORY},
    config::APP_CONFIG,
    db::{
        coins::{Coins, Socials},
        utils::traits::RepositorySharedMethod,
    },
    services::{
        kafka::{enums::KafkaTopic, kafka_service::KafkaService},
        migrate_dex_handler::MigrateDex,
    },
    utils::common::{
        calculate_bonding_curve_progress, generate_coin_slug, get_market_cap_sui, ToF64,
        CETUS_CREATE_POOL_EVENT, FEE_DENOMINATOR, TOKEN_DECIMALS,
    },
};

use super::{
    collect_curve_events::{CreatedEvent, PoolCompletedEvent, TradedEvent},
    redis_service::RedisEmitter,
};

pub async fn handle_create_event_for_coin(
    event: &CreatedEvent,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let platform_fee_withdraw_rate = (Decimal::from_u16(event.platform_fee_withdraw).unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let creator_fee_withdraw_rate = (Decimal::from_u16(event.creator_fee_withdraw).unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let stake_fee_withdraw_rate = (Decimal::from_u16(event.stake_fee_withdraw).unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let platform_stake_fee_withdraw_rate = (Decimal::from_u16(event.platform_stake_fee_withdraw)
        .unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let remain_token_reserves = Decimal::from_str(&event.virtual_token_reserves.to_string())
        .unwrap()
        - Decimal::from_str(&event.real_token_reserves.to_string()).unwrap();

    // Calculate threshold by breaking down the calculation to avoid overflow
    let virtual_sui = Decimal::from_str(&event.virtual_sui_reserves.to_string()).unwrap();
    let real_token = Decimal::from_str(&event.real_token_reserves.to_string()).unwrap();
    let threshold = (virtual_sui / remain_token_reserves) * real_token;
    let bonding_dex = MigrateDex::from_u8(event.bonding_dex.unwrap_or(0));

    let slug = generate_coin_slug(&event.name);

    let update_doc = doc! {
        "$set": {
            "poolAddress": event.pool_id.to_string(),
            "name": event.name.clone(),
            "symbol": event.symbol.clone(),
            "slug": slug.clone(),
            "description": event.description.clone(),
            "logoUri": event.uri.clone(),
            "socials": {
                "telegram": event.telegram.clone(),
                "website": event.website.clone(),
                "x": event.twitter.clone(),
            },
            "creatorAddress": event.created_by.to_string(),
            "threshold": Decimal128::from_str(&threshold.round().to_string()).unwrap(),
            "feeRates":{
                "platformFeeWithdrawRate": Decimal128::from_str(&platform_fee_withdraw_rate).unwrap(),
                "creatorFeeWithdrawRate": Decimal128::from_str(&creator_fee_withdraw_rate).unwrap(),
                "stakeFeeWithdrawRate": Decimal128::from_str(&stake_fee_withdraw_rate).unwrap(),
                "platformStakeFeeWithdrawRate": Decimal128::from_str(&platform_stake_fee_withdraw_rate).unwrap(),
            },
            "createdAt": DateTime::from_millis(event.ts as i64),
            "realSuiReserves": Decimal128::from_str(&event.real_sui_reserves.to_string()).unwrap(),
            "realTokenReserves": Decimal128::from_str(&event.real_token_reserves.to_string()).unwrap(),
            "mcap": Decimal128::from_str("0").unwrap(),
            "mcapUsd": Decimal128::from_str("0").unwrap(),
            "virtualSuiReserves": Decimal128::from_str(&event.virtual_sui_reserves.to_string()).unwrap(),
            "virtualTokenReserves": Decimal128::from_str(&event.virtual_token_reserves.to_string()).unwrap(),
            "initVirtualSuiReserves": Decimal128::from_str(&event.virtual_sui_reserves.to_string()).unwrap(),
            "initVirtualTokenReserves": Decimal128::from_str(&event.virtual_token_reserves.to_string()).unwrap(),
            "bondingCurve": 0.0,
            "bondingDex": bonding_dex.to_string(),
        }
    };

    if let Err(e) = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": event.token_address.to_string(),
            },
            update_doc,
            FindOneAndUpdateOptions::builder().upsert(true).build(),
        )
        .await
    {
        tracing::error!(
            "Failed to insert database for coin {}: {}",
            event.token_address.to_string(),
            e
        );
        return Ok(());
    }

    tracing::info!("Coin inserted: {}", event.token_address.to_string());

    // Send create coin event to ws server
    let pub_coin = PubCreatedCoins {
        token_address: event.token_address.to_string(),
        pool_address: event.pool_id.to_string(),
        name: event.name.clone(),
        symbol: event.symbol.clone(),
        slug: slug.clone(),
        logo_uri: event.uri.clone(),
        description: event.description.clone(),
        socials: Socials {
            telegram: event.telegram.clone(),
            website: event.website.clone(),
            x_social: event.twitter.clone(),
        },
        creator_address: event.created_by.to_string(),
        virtual_sui_reserves: event.virtual_sui_reserves,
        virtual_token_reserves: event.virtual_token_reserves,
        real_token_reserves: event.real_token_reserves,
        threshold: threshold.round().to_string().parse::<u64>().unwrap(),
        create_at: DateTime::from_millis(event.ts as i64).to_chrono(),
    };

    if let Err(e) = emit_create_coin_event(&pub_coin, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) = KafkaService::publish_messages(KafkaTopic::CreatedCoin, &vec![pub_coin]).await {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }

    Ok(())
}

pub async fn handle_traded_event_for_coin(
    event: &TradedEvent,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let token_address = event.token_address.to_string();

    let token = match find_coin_by_token_address(&token_address).await {
        Ok(coin) => coin,
        Err(e) => {
            tracing::error!("{}", e);
            return Ok(());
        }
    };

    let mcap = get_market_cap_sui(
        event.virtual_token_reserves,
        event.virtual_sui_reserves,
        token.init_virtual_token_reserves.unwrap().to_f64(),
        TOKEN_DECIMALS,
    );
    let bonding_curve_progress = calculate_bonding_curve_progress(
        event.real_sui_reserves,
        token.threshold.unwrap().to_string().parse::<u64>().unwrap(),
    );

    let sui_price_usd = TOKEN_PRICES_REPOSITORY
        .get_cached_token_price("sui")
        .await?;
    let bg_sui_price_usd = Decimal::from_str(&sui_price_usd.to_string()).unwrap();
    let bg_mcap = Decimal::from_str(&mcap.to_string()).unwrap();
    let mcap_usd = (bg_mcap * bg_sui_price_usd).to_f64();

    let update_object = doc! {
        "realSuiReserves": Decimal128::from_str(&event.real_sui_reserves.to_string()).unwrap(),
        "realTokenReserves": Decimal128::from_str(&event.real_token_reserves.to_string()).unwrap(),
        "virtualSuiReserves": Decimal128::from_str(&event.virtual_sui_reserves.to_string()).unwrap(),
        "virtualTokenReserves": Decimal128::from_str(&event.virtual_token_reserves.to_string()).unwrap(),
        "mcap": Decimal128::from_str(&mcap.to_string()).unwrap(),
        "mcapUsd": Decimal128::from_str(&mcap_usd.to_string()).unwrap(),
        "bondingCurve": bonding_curve_progress,
    };

    let pub_updated_coin = PubUpdatedCoins {
        mcap,
        mcap_usd,
        virtual_sui_reserves: event.virtual_sui_reserves,
        virtual_token_reserves: event.virtual_token_reserves,
        real_sui_reserves: event.real_sui_reserves,
        real_token_reserves: event.real_token_reserves,
        bonding_curve_progress,
        token_address: token_address.clone(),
    };

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
            },
            vec![
                doc! {
                    "$set": {
                        "prevMcap": "$mcap",
                        "prevRealSuiReserves": "$realSuiReserves",
                    }
                },
                doc! {
                    "$set": update_object.clone(),
                },
            ],
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update database for coin {}: {:?}",
            &token_address,
            result.err()
        );
        return Ok(());
    };

    if opt_coin.is_none() {
        tracing::error!(
            "Failed to update database for coin {}: {}",
            &token_address,
            "Coin not found"
        );
        return Ok(());
    }

    tracing::info!(
        "Coin info updated for: coin={}, data={:?}",
        &token_address,
        &update_object
    );

    // Send mcap change event
    let coin = opt_coin.unwrap();
    let mcap_change = if let Some(prev_mcap) = coin.prev_mcap {
        mcap - prev_mcap.to_f64()
    } else {
        mcap
    };

    if let Err(e) = emit_bonding_change_event(
        &PubBondingChange {
            bonding_curve_progress,
            token_address: token_address.clone(),
        },
        redis_emitter,
    ) {
        tracing::error!("Failed to emit bonding change event: {}", e);
    }

    let pub_mcap_change = PubMcapChange {
        token_address: token_address.clone(),
        mcap_change,
    };

    if let Err(e) = emit_mcap_change_event(&pub_mcap_change, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::McapChange, &vec![pub_mcap_change]).await
    {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }

    // Send new mcap event
    let pub_updated_mcap = PubUpdatedMcap {
        mcap,
        mcap_usd,
        token_address: token_address.clone(),
    };

    if let Err(e) = emit_updated_mcap_event(&pub_updated_mcap, &token_address, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::UpdatedMcap, &vec![pub_updated_mcap]).await
    {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }

    // Send update coin event
    if let Err(e) = emit_update_coin_event(&pub_updated_coin, &token_address, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::UpdatedCoin, &vec![pub_updated_coin]).await
    {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }

    Ok(())
}

pub async fn handle_complete_event_for_coin(
    event: &PoolCompletedEvent,
    transaction_digest: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let sui_client = get_sui_client_instance();
    let transaction_events = sui_client
        .event_api()
        .get_events(TransactionDigest::from_str(transaction_digest).unwrap())
        .await?;

    let create_pool_event = transaction_events.iter().find(|event| {
        event.type_.to_canonical_string(true)
            == format!("{}::{}", APP_CONFIG.cetus_package, CETUS_CREATE_POOL_EVENT)
    });

    if create_pool_event.is_none() {
        tracing::info!(
            "Failed to find create Cetus pool event: {}",
            transaction_digest
        );
        return Ok(());
    }

    let create_pool_event = create_pool_event.unwrap();
    let pool_address = create_pool_event
        .parsed_json
        .get("pool_id")
        .unwrap()
        .as_str()
        .unwrap()
        .trim_matches('"');

    tracing::info!(
        "Successfully created Cetus pool: {} for token: {}",
        pool_address,
        event.token_address.to_string()
    );

    let update_object = doc! {
        "$set": {
            "listedPoolId": pool_address,
            "bondingDex": MigrateDex::Cetus.to_string(),
            "listedAt": DateTime::from_millis(event.ts as i64),
        }
    };

    let result = COINS_REPOSITORY
        .update_one(
            doc! {
                "tokenAddress": &event.token_address.to_string(),
            },
            update_object,
            None,
        )
        .await;

    let Ok(result) = result else {
        tracing::error!(
            "Failed to update database for coin {}: {:?}",
            &event.token_address.to_string(),
            result.err()
        );
        return Ok(());
    };

    let event_bonding_completed_coin = BondingCompletedCoin {
        token_address: event.token_address.to_string(),
        listed_pool_id: pool_address.to_string(),
        listed_at: Some(DateTime::from_millis(event.ts as i64).to_chrono()),
        bonding_dex: MigrateDex::Cetus.to_string().to_string(),
    };

    // Send update coin event
    if let Err(e) = emit_bonding_completed_coin_event(&event_bonding_completed_coin, redis_emitter)
    {
        tracing::error!("Failed to emit bonding completed event to ws server: {}", e);
    }

    tracing::info!(
        "Coin info updated for: coin={}, data={:?}",
        &event.token_address.to_string(),
        &result
    );
    Ok(())
}

async fn find_coin_by_token_address(token_address: &str) -> anyhow::Result<Coins, anyhow::Error> {
    let result = COINS_REPOSITORY
        .find_one(doc! { "tokenAddress": token_address }, None)
        .await;

    let Ok(opt_coin) = result else {
        return Err(anyhow::anyhow!(
            "Failed to find coin for token address {}: {:?}",
            token_address,
            result.err()
        ));
    };

    let Some(coin) = opt_coin else {
        return Err(anyhow::anyhow!(
            "Failed to find coin for token address {}: {}",
            token_address,
            "Coin not found"
        ));
    };
    Ok(coin)
}

fn emit_create_coin_event(
    coin: &PubCreatedCoins,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(coin)?;
    let room = "SUBSCRIBE_NEW_COIN";
    let event = "CreatedCoin";

    redis_emitter.emit_room(room, event, &pub_data);

    Ok(())
}

fn emit_mcap_change_event(
    pub_mcap_change: &PubMcapChange,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(pub_mcap_change)?;
    let room = "SUBSCRIBE_MCAP";
    let event = "McapChange";

    redis_emitter.emit_room(room, event, &pub_data);
    Ok(())
}

fn emit_updated_mcap_event(
    pub_data: &PubUpdatedMcap,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(pub_data)?;
    let room = format!("SUBSCRIBE_MCAP::{}", token_address);
    let event = "UpdatedMcap";

    redis_emitter.emit_room(&room, event, &pub_data);
    Ok(())
}

fn emit_bonding_change_event(
    pub_data: &PubBondingChange,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(pub_data)?;
    let room = "SUBSCRIBE_BONDING".to_string();
    let event = "BondingChange";

    redis_emitter.emit_room(&room, event, &pub_data);
    Ok(())
}

fn emit_update_coin_event(
    coin: &PubUpdatedCoins,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(coin)?;
    let room = format!("SUBSCRIBE_COIN::{}", token_address);
    let event = "UpdatedCoin";

    redis_emitter.emit_room(&room, event, &pub_data);
    Ok(())
}

pub fn emit_bonding_completed_coin_event(
    payload: &BondingCompletedCoin,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let string_payload = serde_json::to_string(payload)?;
    let room = format!("SUBSCRIBE_COIN::{}", payload.token_address);
    let event = "BondingCompleted";

    redis_emitter.emit_room(&room, event, &string_payload);

    emit_bonding_change_event(
        &PubBondingChange {
            token_address: payload.token_address.clone(),
            bonding_curve_progress: 100.0,
        },
        redis_emitter,
    )?;

    Ok(())
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubCreatedCoins {
    pub token_address: String,
    pub pool_address: String,
    pub name: String,
    pub symbol: String,
    pub slug: String,
    pub description: String,
    pub logo_uri: String,
    pub socials: Socials,
    pub creator_address: String,
    pub virtual_sui_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_token_reserves: u64,
    pub threshold: u64,
    pub create_at: chrono::DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubUpdatedCoins {
    pub mcap: f64,
    pub mcap_usd: f64,
    pub virtual_sui_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_sui_reserves: u64,
    pub real_token_reserves: u64,
    pub bonding_curve_progress: f64,
    pub token_address: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct BondingCompletedCoin {
    pub token_address: String,
    pub listed_pool_id: String,
    pub listed_at: Option<chrono::DateTime<Utc>>,
    pub bonding_dex: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubMcapChange {
    pub token_address: String,
    pub mcap_change: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubUpdatedMcap {
    pub token_address: String,
    pub mcap: f64,
    pub mcap_usd: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubBondingChange {
    pub token_address: String,
    pub bonding_curve_progress: f64,
}
