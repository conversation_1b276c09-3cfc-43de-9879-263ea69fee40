use moonbags_collector::{
    common::initialize_sui_client, config::APP_CONFIG, db::common::setup_mongodb,
    services::collect_curve_events::collect_curve_events, utils::tracing::init_standard_tracing,
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    initialize_sui_client().await;

    tracing::info!(
        "collect_curve_events with package={} and module={}",
        APP_CONFIG.bonding_event_package,
        APP_CONFIG.event_module
    );

    collect_curve_events().await?;
    Ok(())
}
