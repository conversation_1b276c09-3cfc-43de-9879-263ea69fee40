use moonbags_collector::{
    common::initialize_sui_client,
    config::APP_CONFIG,
    db::common::setup_mongodb,
    services::{jobs_processor::process_jobs, redis_service::RedisEmitter},
    utils::tracing::init_standard_tracing,
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    initialize_sui_client().await;

    tracing::info!("Starting jobs processor");

    let redis_emitter = RedisEmitter::new()?;

    process_jobs(&redis_emitter).await?;

    Ok(())
}
