use moonbags_collector::{
    common::initialize_sui_client,
    utils::{sui::turbos_finance::tx::burn_position, tracing::init_standard_tracing},
};
use std::str::FromStr;
use sui_sdk::types::{
    base_types::{ObjectID, SequenceNumber},
    digests::ObjectDigest,
    transaction::ObjectArg,
    TypeTag,
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    initialize_sui_client().await;

    burn_position(
        ObjectArg::SharedObject {
            id: ObjectID::from_hex_literal(
                "0x93cb1ac04d40effc2a0c01d3b86408e6114f5e17ae4c2065d15d6fe9945134d9",
            )
            .unwrap(),
            initial_shared_version: 566586443.into(),
            mutable: true,
        },
        ObjectArg::ImmOrOwnedObject((
            ObjectID::from_hex_literal(
                "0x313655e259fee62dfea77cfbd6101bef6a6665dfa3cbab30e77227c5df4f3000",
            )
            .unwrap(),
            SequenceNumber::from_u64(566741390),
            ObjectDigest::from_str("EssXxPwFQeZGXHDz97HqDU2DwStmxhfqRE1fyLYymBeC").unwrap(),
        )),
        TypeTag::from_str(
            "0x1302c4b9f98f2eb2e4e25e2632272e60bb32235099d05a74e6a65618a7e1b0eb::tst::TST",
        )
        .unwrap(),
        false,
    )
    .await?;
    Ok(())
}
