use moonbags_collector::{
    common::initialize_sui_client, config::APP_CONFIG, db::common::setup_mongodb,
    services::collect_token_lock_event::collector::collect_token_lock_events,
    utils::tracing::init_standard_tracing,
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    initialize_sui_client().await;

    tracing::info!(
        "collect_token_lock_events with package={} and module={}",
        APP_CONFIG.staking_event_package,
        APP_CONFIG.token_lock_event_module
    );

    collect_token_lock_events().await?;
    Ok(())
}
