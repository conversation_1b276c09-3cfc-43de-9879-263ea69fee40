use std::sync::LazyLock;

use sui_sdk::{SuiClient, SuiClientBuilder};
use tokio::sync::OnceCell;

use crate::{
    config::APP_CONFIG,
    db::{
        candles::CandlesRepository,
        coin_lock::CoinLockRepository,
        coins::CoinsRepository,
        common::{get_mongodb_instance, MONGODB},
        holders::HoldersRepository,
        jobs::JobsRepository,
        latest_signatures::LatestSignaturesRepository,
        staking_events::StakingEventsRepository,
        staking_users::StakingUsersRepository,
        token_prices::TokenPricesRepository,
        trades::TradesRepository,
        utils::traits::RepositorySharedMethod,
    },
};

pub static SUI_CLIENT: OnceCell<SuiClient> = OnceCell::const_new();
pub async fn initialize_sui_client() -> &'static SuiClient {
    SUI_CLIENT
        .get_or_init(|| async move {
            SuiClientBuilder::default()
                .build(APP_CONFIG.fullnode_url.clone())
                .await
                .unwrap()
        })
        .await
}

pub fn get_sui_client_instance() -> &'static SuiClient {
    SUI_CLIENT.get().expect("sui client not initialized")
}

pub static LATEST_SIGNATURES_REPOSITORY: LazyLock<LatestSignaturesRepository> =
    LazyLock::new(|| LatestSignaturesRepository::new(get_mongodb_instance()));
pub static HOLDERS_REPOSITORY: LazyLock<HoldersRepository> =
    LazyLock::new(|| HoldersRepository::new(MONGODB.get().unwrap()));
pub static COINS_REPOSITORY: LazyLock<CoinsRepository> =
    LazyLock::new(|| CoinsRepository::new(MONGODB.get().unwrap()));
pub static TOKEN_PRICES_REPOSITORY: LazyLock<TokenPricesRepository> =
    LazyLock::new(|| TokenPricesRepository::new(MONGODB.get().unwrap()));
pub static CANDLES_REPOSITORY: LazyLock<CandlesRepository> =
    LazyLock::new(|| CandlesRepository::new(MONGODB.get().unwrap()));
pub static TRADES_REPOSITORY: LazyLock<TradesRepository> =
    LazyLock::new(|| TradesRepository::new(MONGODB.get().unwrap()));
pub static STAKING_EVENTS_REPOSITORY: LazyLock<StakingEventsRepository> =
    LazyLock::new(|| StakingEventsRepository::new(MONGODB.get().unwrap()));
pub static STAKING_USERS_REPOSITORY: LazyLock<StakingUsersRepository> =
    LazyLock::new(|| StakingUsersRepository::new(MONGODB.get().unwrap()));
pub static COIN_LOCK_REPOSITORY: LazyLock<CoinLockRepository> =
    LazyLock::new(|| CoinLockRepository::new(MONGODB.get().unwrap()));
pub static LIQUIDITY_JOBS_REPOSITORY: LazyLock<JobsRepository> =
    LazyLock::new(|| JobsRepository::new(MONGODB.get().unwrap()));
