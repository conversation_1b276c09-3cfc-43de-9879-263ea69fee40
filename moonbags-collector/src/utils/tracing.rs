use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::config::APP_CONFIG;

pub fn init_standard_tracing(crate_name: &str) {
    let level = &APP_CONFIG.log_level;
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
                format!("{crate_name}={level},moonbags_collector={level}").into()
            }),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
}
