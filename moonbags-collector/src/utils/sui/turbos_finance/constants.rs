use std::{str::FromStr, sync::LazyLock};

use sui_sdk::types::{
    base_types::{ObjectID, SequenceNumber},
    digests::ObjectDigest,
    transaction::ObjectArg,
};

use crate::config::APP_CONFIG;

pub static TURBOS_FINANCE_CLMM_PACKAGE_ID: LazyLock<ObjectID> = LazyLock::new(|| {
    ObjectID::from_hex_literal(&APP_CONFIG.turbos_finance_clmm_package_id).unwrap()
});

pub static TURBOS_POSITION_NFT_TYPE: LazyLock<&str> =
    LazyLock::new(|| &APP_CONFIG.turbos_position_nft_type);

pub static TURBOS_FINANCE_ADDRESS: LazyLock<&str> =
    LazyLock::new(|| &APP_CONFIG.turbos_finance_address);

pub static TURBOS_DYNAMIC_TABLE_ADDRESS: LazyLock<&str> =
    LazyLock::new(|| &APP_CONFIG.turbos_dynamic_table_address);

pub static MOONBAGS_PACKAGE_ID: LazyLock<ObjectID> =
    LazyLock::new(|| APP_CONFIG.moonbags_package_id);

pub static MOONBAGS_CONFIGURATION_OBJECT: LazyLock<ObjectArg> = LazyLock::new(|| {
    let parts: Vec<&str> = APP_CONFIG
        .moonbags_configuration_object
        .split('_')
        .collect();
    let id = ObjectID::from_hex_literal(parts[0]).unwrap();
    let version = parts[1].parse::<u64>().unwrap().into();
    ObjectArg::SharedObject {
        id,
        initial_shared_version: version,
        mutable: true,
    }
});

pub static POOL_CONFIG_OBJECT: LazyLock<ObjectArg> = LazyLock::new(|| ObjectArg::SharedObject {
    id: ObjectID::from_hex_literal(&APP_CONFIG.turbos_pool_config_object_id).unwrap(),
    initial_shared_version: APP_CONFIG.turbos_pool_config_initial_shared_version.into(),
    mutable: true,
});

pub static TURBOS_FEE_TYPE_OBJECT: LazyLock<ObjectArg> = LazyLock::new(|| {
    ObjectArg::ImmOrOwnedObject((
        ObjectID::from_hex_literal(&APP_CONFIG.turbos_fee_type_object_id).unwrap(),
        SequenceNumber::from_u64(APP_CONFIG.turbos_fee_type_sequence_number),
        ObjectDigest::from_str(&APP_CONFIG.turbos_fee_type_object_digest).unwrap(),
    ))
});

pub static TURBOS_POSITIONS_OBJECT: LazyLock<ObjectArg> =
    LazyLock::new(|| ObjectArg::SharedObject {
        id: ObjectID::from_hex_literal(&APP_CONFIG.turbos_positions_object_id).unwrap(),
        initial_shared_version: APP_CONFIG.turbos_positions_initial_shared_version.into(),
        mutable: true,
    });

pub static TURBOS_VERSIONED_OBJECT: LazyLock<ObjectArg> =
    LazyLock::new(|| ObjectArg::SharedObject {
        id: ObjectID::from_hex_literal(&APP_CONFIG.turbos_versioned_object_id).unwrap(),
        initial_shared_version: APP_CONFIG.turbos_versioned_initial_shared_version.into(),
        mutable: false,
    });

pub static SUI_TYPE: LazyLock<&str> = LazyLock::new(|| &APP_CONFIG.sui_type);

pub static TURBOS_FEE_TYPE: LazyLock<&str> = LazyLock::new(|| &APP_CONFIG.turbos_fee_type);
