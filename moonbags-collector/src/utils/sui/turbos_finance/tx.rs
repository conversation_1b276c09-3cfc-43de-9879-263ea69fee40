use serde_json::Value;
use sha2::{Digest, Sha256};
use shared_crypto::intent::Intent;
use std::str::FromStr;
use sui_keys::keystore::{AccountKeystore, InMemKeystore};
use sui_sdk::rpc_types::{
    DryRunTransactionBlockResponse, ObjectChange, SuiMoveStruct, SuiMoveValue, SuiParsedData,
};
use sui_sdk::types::base_types::SuiAddress;
use sui_sdk::types::digests::TransactionDigest;
use sui_sdk::types::dynamic_field::DynamicFieldName;
use sui_sdk::types::transaction::ProgrammableTransaction;
use sui_sdk::{
    rpc_types::{
        SuiExecutionStatus, SuiTransactionBlockEffects, SuiTransactionBlockResponseOptions,
    },
    types::{
        base_types::ObjectID,
        crypto::Signature,
        programmable_transaction_builder::ProgrammableTransactionBuilder,
        transaction::{Argument, CallArg, Command, ObjectArg, Transaction, TransactionData},
        TypeTag,
    },
};

use crate::services::migrate_dex_handler::update_listed_pool_id;
use crate::services::redis_service::RedisEmitter;
use crate::{
    common::{LIQUIDITY_JOBS_REPOSITORY, SUI_CLIENT},
    utils::sui::{utils::get_amount_onchain, wallet::SuiWallet},
};

use super::constants::{
    TURBOS_DYNAMIC_TABLE_ADDRESS, TURBOS_FINANCE_ADDRESS, TURBOS_POSITION_NFT_TYPE,
};

pub const DEFAULT_GAS_BUDGET: u64 = 40_000_000;
pub const DEFAULT_DEADLINE_MS: i64 = 1000 * 5 * 60;
pub const TICK_INDEX: u32 = 443600;

pub async fn prepare_token_coins(
    ptb: &mut ProgrammableTransactionBuilder,
    admin_address: SuiAddress,
    token_type_tag: TypeTag,
    token_amount: u64,
) -> anyhow::Result<Argument> {
    let (coin_objects, coin_amounts) = match get_amount_onchain(admin_address, token_type_tag).await
    {
        Ok(result) => result,
        Err(e) => {
            tracing::error!("Failed to get balance: {}", e);
            return Err(anyhow::anyhow!("Failed to get balance: {}", e));
        }
    };

    if token_amount > coin_amounts {
        return Err(anyhow::anyhow!("Insufficient balance"));
    }

    let main_coin = ptb.input(CallArg::Object(ObjectArg::ImmOrOwnedObject(
        coin_objects[0],
    )))?;

    if coin_objects.len() > 1 {
        let coin_need_to_merge = coin_objects
            .iter()
            .enumerate()
            .filter(|(i, _obj)| *i != 0)
            .map(|(_, obj)| ptb.input(CallArg::Object(ObjectArg::ImmOrOwnedObject(*obj))))
            .filter_map(Result::ok)
            .collect::<Vec<_>>();

        ptb.command(Command::MergeCoins(main_coin, coin_need_to_merge));
    }

    Ok(ptb.command(Command::MakeMoveVec(None, vec![main_coin])))
}

pub async fn prepare_token_coin(
    ptb: &mut ProgrammableTransactionBuilder,
    admin_address: SuiAddress,
    token_type_tag: TypeTag,
    token_amount: u64,
) -> anyhow::Result<Argument> {
    let (coin_objects, coin_amounts) = match get_amount_onchain(admin_address, token_type_tag).await
    {
        Ok(result) => result,
        Err(e) => {
            tracing::error!("Failed to get balance: {}", e);
            return Err(anyhow::anyhow!("Failed to get balance: {}", e));
        }
    };

    if token_amount > coin_amounts {
        return Err(anyhow::anyhow!("Insufficient balance"));
    }

    let main_coin = ptb.input(CallArg::Object(ObjectArg::ImmOrOwnedObject(
        coin_objects[0],
    )))?;

    if coin_objects.len() > 1 {
        let coin_need_to_merge = coin_objects
            .iter()
            .enumerate()
            .filter(|(i, _obj)| *i != 0)
            .map(|(_, obj)| ptb.input(CallArg::Object(ObjectArg::ImmOrOwnedObject(*obj))))
            .filter_map(Result::ok)
            .collect::<Vec<_>>();

        ptb.command(Command::MergeCoins(main_coin, coin_need_to_merge));
    }

    Ok(main_coin)
}

pub async fn prepare_sui_coins(
    ptb: &mut ProgrammableTransactionBuilder,
    sui_amount: u64,
) -> anyhow::Result<Argument> {
    let split_sui_amount = ptb.pure(sui_amount)?;
    let split_sui_coin = ptb.command(Command::SplitCoins(
        Argument::GasCoin,
        vec![split_sui_amount],
    ));

    Ok(ptb.command(Command::MakeMoveVec(None, vec![split_sui_coin])))
}

pub async fn execute_transaction_with_admin_wallet(
    ptb: ProgrammableTransaction,
    admin_wallet: SuiWallet,
) -> anyhow::Result<String> {
    let admin_address = admin_wallet.address;

    let (coins, gas_price) = tokio::try_join!(
        SUI_CLIENT
            .get()
            .unwrap()
            .coin_read_api()
            .get_coins(admin_address, None, None, None),
        SUI_CLIENT
            .get()
            .unwrap()
            .read_api()
            .get_reference_gas_price(),
    )?;

    let tx_data = TransactionData::new_programmable(
        admin_address,
        coins.data.iter().map(|coin| coin.object_ref()).collect(),
        ptb,
        DEFAULT_GAS_BUDGET,
        gas_price,
    );

    let mut keystore = InMemKeystore::default();
    keystore.add_key(Some("".to_string()), admin_wallet.keypair)?;

    let signature = keystore.sign_secure(&admin_address, &tx_data, Intent::sui_transaction())?;

    let (is_success, err_message, tx_hash) = execute_transaction(tx_data, vec![signature]).await;
    if !is_success {
        return Err(anyhow::anyhow!(
            "Failed to execute transaction: {}",
            err_message
        ));
    }

    Ok(tx_hash)
}

pub async fn simulate_transaction(
    ptb: ProgrammableTransaction,
    admin_wallet: &SuiWallet,
) -> anyhow::Result<DryRunTransactionBlockResponse> {
    let admin_address = admin_wallet.address;

    let (coins, gas_price) = tokio::try_join!(
        SUI_CLIENT
            .get()
            .unwrap()
            .coin_read_api()
            .get_coins(admin_address, None, None, None),
        SUI_CLIENT
            .get()
            .unwrap()
            .read_api()
            .get_reference_gas_price(),
    )?;

    let tx_data = TransactionData::new_programmable(
        admin_address,
        coins.data.iter().map(|coin| coin.object_ref()).collect(),
        ptb,
        DEFAULT_GAS_BUDGET,
        gas_price,
    );

    let simulate_result = SUI_CLIENT
        .get()
        .unwrap()
        .read_api()
        .dry_run_transaction_block(tx_data)
        .await?;

    Ok(simulate_result)
}

pub async fn deploy_pool_and_add_liquidity(
    token_type_tag: TypeTag,
    sui_amount: u64,
    token_amount: u64,
) -> Result<(), anyhow::Error> {
    let job_id = LIQUIDITY_JOBS_REPOSITORY
        .create_deploy_pool_job(
            token_type_tag.to_canonical_string(true),
            sui_amount,
            token_amount,
        )
        .await?;

    tracing::info!(
        "Created deploy_pool_and_add_liquidity job with ID: {:?}",
        job_id
    );
    Ok(())
}

pub async fn add_liquidity(
    pool_object: ObjectArg,
    token_type_tag: TypeTag,
    sui_amount: u64,
    token_amount: u64,
    is_sui_coin_first: bool,
    redis_emitter: &RedisEmitter,
) -> Result<(), anyhow::Error> {
    let job_id = LIQUIDITY_JOBS_REPOSITORY
        .create_add_job(
            token_type_tag.to_canonical_string(true),
            pool_object.id().to_canonical_string(true),
            sui_amount,
            token_amount,
            is_sui_coin_first,
        )
        .await?;

    tracing::info!("Created add_liquidity job with ID: {:?}", job_id);

    update_listed_pool_id(
        token_type_tag.to_canonical_string(true),
        pool_object.id().to_canonical_string(true),
        redis_emitter,
    )
    .await?;

    Ok(())
}

pub async fn burn_position(
    pool_object: ObjectArg,
    nft_object: ObjectArg,
    token_type_tag: TypeTag,
    is_sui_coin_first: bool,
) -> Result<(), anyhow::Error> {
    let job_id = LIQUIDITY_JOBS_REPOSITORY
        .create_burn_position_job(
            token_type_tag.to_canonical_string(true),
            pool_object.id().to_canonical_string(true),
            nft_object.id().to_canonical_string(true),
            is_sui_coin_first,
        )
        .await?;

    tracing::info!("Created burn_position job with ID: {:?}", job_id);

    Ok(())
}

async fn execute_transaction(
    tx_data: TransactionData,
    signature: Vec<Signature>,
) -> (bool, String, String) {
    match SUI_CLIENT
        .get()
        .unwrap()
        .quorum_driver_api()
        .execute_transaction_block(
            Transaction::from_data(tx_data, signature),
            SuiTransactionBlockResponseOptions::with_effects(
                SuiTransactionBlockResponseOptions::default(),
            ),
            None,
        )
        .await
    {
        Ok(res) => {
            if res.effects.is_none() {
                return (
                    false,
                    "Transaction response with no effects".to_string(),
                    res.digest.to_string(),
                );
            }
            match res.effects.unwrap() {
                SuiTransactionBlockEffects::V1(effects_v1) => match effects_v1.status {
                    SuiExecutionStatus::Success => (true, "".to_string(), res.digest.to_string()),
                    SuiExecutionStatus::Failure { error } => {
                        (false, error.to_string(), res.digest.to_string())
                    }
                },
            }
        }
        Err(error) => (false, error.to_string(), "".to_string()),
    }
}

pub async fn check_pool_exists(
    token_type_tag: TypeTag,
    sui_type_tag: TypeTag,
    fee_type_tag: TypeTag,
) -> Result<Option<String>, anyhow::Error> {
    let pool_key = pool_key(&token_type_tag, &sui_type_tag, &fee_type_tag);
    let pool_config = SUI_CLIENT
        .get()
        .unwrap()
        .read_api()
        .get_dynamic_field_object(
            ObjectID::from_hex_literal(&TURBOS_DYNAMIC_TABLE_ADDRESS).unwrap(),
            DynamicFieldName {
                type_: TypeTag::from_str("0x2::object::ID")?,
                value: Value::String(pool_key.to_string()),
            },
        )
        .await;

    let pool_config = match pool_config {
        Ok(config) => config,
        Err(_) => return Ok(None),
    };

    let pool_config_data = match pool_config.data {
        Some(data) => data,
        None => return Ok(None),
    };

    let content = match pool_config_data.content {
        Some(content) => content,
        None => return Ok(None),
    };

    let fields = match content {
        SuiParsedData::MoveObject(move_object) => match &move_object.fields {
            SuiMoveStruct::WithFields(fields) => fields.get("value").cloned(),
            _ => None,
        },
        _ => None,
    };

    if let Some(SuiMoveValue::Struct(SuiMoveStruct::WithTypes { fields, .. })) = fields {
        let pool_id = fields.get("pool_id").cloned();
        Ok(pool_id.map(|id| id.to_string()))
    } else {
        Ok(None)
    }
}

fn compare_types(arg0: &[u8], arg1: &[u8]) -> bool {
    let v0 = arg0.len();
    let v1 = arg1.len();
    let mut v2 = 0;
    while v2 < v0 && v2 < v1 {
        let v3 = arg0[v2];
        let v4 = arg1[v2];
        if v3 < v4 {
            return false;
        }
        if v3 > v4 {
            return true;
        }
        v2 += 1;
    }
    v0 >= v1
}

pub fn pool_key(token_type: &TypeTag, sui_type: &TypeTag, fee_type: &TypeTag) -> ObjectID {
    let mut combined_bytes = Vec::new();
    let token_bytes = token_type.to_canonical_string(false).into_bytes();
    let sui_bytes = sui_type.to_canonical_string(false).into_bytes();

    let (first, second) = if !compare_types(&token_bytes, &sui_bytes) {
        (token_bytes, sui_bytes)
    } else {
        (sui_bytes, token_bytes)
    };

    combined_bytes.extend_from_slice(&first);
    combined_bytes.extend_from_slice(&second);
    combined_bytes.extend_from_slice(&fee_type.to_canonical_string(false).into_bytes());

    let hash = Sha256::digest(&combined_bytes);
    ObjectID::from_bytes(hash.as_slice()).expect("Failed to create ObjectID from hash")
}

pub async fn get_nft_object_change_in_tx(tx_hash: &str) -> anyhow::Result<Option<ObjectArg>> {
    let tx = SUI_CLIENT
        .get()
        .unwrap()
        .read_api()
        .get_transaction_with_options(
            TransactionDigest::from_str(tx_hash)?,
            SuiTransactionBlockResponseOptions {
                show_object_changes: true,
                ..Default::default()
            },
        )
        .await?;

    let object_changes = tx.object_changes.unwrap_or_default();

    for object_change in object_changes {
        if let ObjectChange::Created {
            object_id,
            object_type,
            version,
            digest,
            ..
        } = object_change
        {
            if object_type.to_canonical_string(true) == *TURBOS_POSITION_NFT_TYPE {
                return Ok(Some(ObjectArg::ImmOrOwnedObject((
                    object_id, version, digest,
                ))));
            }
        }
    }

    Ok(None)
}

pub async fn get_pool_created_in_tx(tx_hash: &str) -> anyhow::Result<Option<ObjectArg>> {
    let tx = SUI_CLIENT
        .get()
        .unwrap()
        .read_api()
        .get_transaction_with_options(
            TransactionDigest::from_str(tx_hash)?,
            SuiTransactionBlockResponseOptions {
                show_object_changes: true,
                ..Default::default()
            },
        )
        .await?;

    let object_changes = tx.object_changes.unwrap_or_default();
    let turbo_finance_address = *TURBOS_FINANCE_ADDRESS;

    for object_change in object_changes {
        if let ObjectChange::Created {
            object_id,
            object_type,
            version,
            ..
        } = object_change
        {
            if object_type.address.to_canonical_string(true) == *turbo_finance_address
                && object_type.module.to_string() == "pool"
                && object_type.name.to_string() == "Pool"
            {
                return Ok(Some(ObjectArg::SharedObject {
                    id: object_id,
                    initial_shared_version: version,
                    mutable: true,
                }));
            }
        }
    }

    Ok(None)
}
