use anyhow::Result;
use sui_sdk::types::{base_types::<PERSON><PERSON><PERSON>dd<PERSON>, crypto::SuiKeyPair};

#[derive(Debug)]
pub struct SuiWallet {
    pub keypair: Su<PERSON>KeyPair,
    pub address: <PERSON><PERSON><PERSON>dd<PERSON>,
}

impl SuiWallet {
    pub fn new_from_private_key(private_key: &str) -> Result<Self> {
        let keypair =
            SuiKeyPair::decode(private_key).map_err(|_| anyhow::anyhow!("invalid private key"))?;
        let address = SuiAddress::from(&keypair.public());
        Ok(Self { keypair, address })
    }
}
