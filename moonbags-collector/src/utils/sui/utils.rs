use std::str::FromStr;

use sui_sdk::{
    rpc_types::{
        ObjectChange, SuiObjectData, SuiObjectDataOptions, SuiTransactionBlockResponseOptions,
    },
    types::{
        base_types::{ObjectID, ObjectRef, SuiAddress},
        digests::TransactionDigest,
        TypeTag,
    },
};

use crate::common::SUI_CLIENT;

pub const Q64: u128 = (u64::MAX as u128) + 1;

pub fn multiplier(decimals: u8) -> f64 {
    (10_i32).checked_pow(decimals.into()).unwrap() as f64
}

pub fn price_to_x64(price: f64) -> u128 {
    (price * Q64 as f64) as u128
}

pub fn price_to_sqrt_price_x64(price: f64, decimals_0: u8, decimals_1: u8) -> u128 {
    let price_with_decimals = price * multiplier(decimals_1) / multiplier(decimals_0);
    price_to_x64(price_with_decimals.sqrt())
}

pub async fn get_amount_onchain(
    wallet_address: SuiAddress,
    token_address: TypeTag,
) -> anyhow::Result<(Vec<ObjectRef>, u64)> {
    let Ok(coin) = SUI_CLIENT
        .get()
        .unwrap()
        .coin_read_api()
        .get_coins(wallet_address, Some(token_address.to_string()), None, None)
        .await
    else {
        return Err(anyhow::anyhow!("Failed to get balance"));
    };

    Ok((
        coin.data
            .clone()
            .into_iter()
            .map(|coin| coin.object_ref())
            .collect(),
        coin.data.into_iter().map(|coin| coin.balance).sum::<u64>(),
    ))
}

pub async fn get_object(object_id: &str) -> anyhow::Result<SuiObjectData> {
    let object = SUI_CLIENT
        .get()
        .unwrap()
        .read_api()
        .get_object_with_options(
            ObjectID::from_str(object_id)?,
            SuiObjectDataOptions {
                show_type: true,
                show_owner: true,
                show_content: true,
                ..Default::default()
            },
        )
        .await?;
    let data: SuiObjectData = object.data.unwrap();
    Ok(data)
}

pub async fn get_nft_object_change_in_tx(tx_hash: &str) -> anyhow::Result<Option<ObjectID>> {
    let tx = SUI_CLIENT
        .get()
        .unwrap()
        .read_api()
        .get_transaction_with_options(
            TransactionDigest::from_str(tx_hash)?,
            SuiTransactionBlockResponseOptions {
                show_object_changes: true,
                ..Default::default()
            },
        )
        .await?;

    let object_changes = tx.object_changes.unwrap();

    for object_change in object_changes {
        if let ObjectChange::Created {
            object_id,
            object_type,
            sender: _,
            owner: _,
            version: _,
            digest: _,
        } = object_change
        {
            if object_type.to_canonical_string(true) == "0x91bfbc386a41afcfd9b2533058d7e915a1d3829089cc268ff4333d54d6339ca1::position_nft::TurbosPositionNFT" {
                return Ok(Some(object_id));
            }
        }
    }

    Ok(None)
}
