use std::str::FromStr;

use bson::Decimal128;
use nanoid::nanoid;
use rust_decimal::{Decimal, MathematicalOps};
use rust_decimal_macros::dec;
use serde::{Deserialize, Deserializer};
use slugify::slugify;
use sui_sdk::types::{parse_sui_address, parse_sui_type_tag};

pub const SUI_DECIMALS: u8 = 9;
pub const TOKEN_DECIMALS: u8 = 6;
pub const FEE_DENOMINATOR: u64 = 10000;
pub const CETUS_CREATE_POOL_EVENT: &str = "factory::CreatePoolEvent";

pub trait ToF64 {
    fn to_f64(&self) -> f64;
}

impl ToF64 for Decimal128 {
    fn to_f64(&self) -> f64 {
        self.to_string().parse::<f64>().unwrap_or_else(|e| {
            tracing::error!("Failed to parse Decimal128 to f64: {}", e);
            0.0
        })
    }
}

impl ToF64 for Decimal {
    fn to_f64(&self) -> f64 {
        self.to_string().parse::<f64>().unwrap_or_else(|e| {
            tracing::error!("Failed to parse decimal number to f64: {}", e);
            0.0
        })
    }
}

pub fn get_market_cap_sui(
    virtual_token_reserves: u64,
    virtual_sui_reserves: u64,
    init_virtual_token_reserves: f64,
    token_decimals: u8,
) -> f64 {
    if virtual_token_reserves == 0 {
        0.0
    } else {
        let un_decimal_virtual_sui_reserves =
            un_decimals_from_str(&virtual_sui_reserves.to_string(), SUI_DECIMALS);
        let un_decimal_virtual_token_reserves =
            un_decimals_from_str(&virtual_token_reserves.to_string(), token_decimals);
        let price = Decimal::from_str(&un_decimal_virtual_sui_reserves.to_string()).unwrap()
            / Decimal::from_str(&un_decimal_virtual_token_reserves.to_string()).unwrap();

        price.to_f64()
            * un_decimals_from_str(&init_virtual_token_reserves.to_string(), token_decimals)
    }
}

pub fn calculate_bonding_curve_progress(real_sui_reserves: u64, threshold: u64) -> f64 {
    let bonding_progress = if real_sui_reserves >= threshold {
        1.0
    } else {
        (Decimal::from(real_sui_reserves) / Decimal::from(threshold)).to_f64()
    };
    // round the number off to 4 decimal places
    (bonding_progress * 1e6).floor() / 1e4
}

pub fn un_decimals_from_str(number: &str, decimal: u8) -> f64 {
    (Decimal::from_str(number).unwrap() / dec!(10).powi(decimal as i64)).to_f64()
}

pub fn deserialize_sui_address<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: Deserializer<'de>,
{
    let deserializer_address = String::deserialize(deserializer)?;
    let address = match deserializer_address.starts_with("0x") {
        true => deserializer_address,
        false => format!("0x{}", deserializer_address),
    };
    Ok(parse_sui_address(&address).unwrap().to_string())
}

pub fn deserialize_token_type<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: Deserializer<'de>,
{
    let deserializer_address = String::deserialize(deserializer)?;
    let address = match deserializer_address.starts_with("0x") {
        true => deserializer_address,
        false => format!("0x{}", deserializer_address),
    };
    Ok(parse_sui_type_tag(&address)
        .unwrap()
        .to_canonical_string(true))
}

pub fn get_real_token_decimals(token_address: &str) -> u8 {
    if token_address
        == "0x16ab6a14d76a90328a6b04f06b0a0ce952847017023624e0c37bf8aa314c39ba::shr::SHR"
    {
        return 9;
    }
    if token_address == "0x2::sui::SUI"
        || token_address
            == "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI"
    {
        return 9;
    }
    TOKEN_DECIMALS
}

pub fn generate_coin_slug(token_name: &str) -> String {
    const ALPHANUMERIC: [char; 52] = [
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
        'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
        'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
    ];

    let normalized_name = slugify!(token_name);
    let nano_id = nanoid!(10, &ALPHANUMERIC);

    format!("{}-sui-{}", normalized_name, nano_id)
}
