use alloy::{primitives::utils::format_ether, rpc::types::Log};
use bson::doc;
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument};
use rust_decimal::Decimal;

use crate::{
    abis::MoonbagsStake::{
        ClaimCreatorPoolEvent, ClaimStakingPoolEvent, DepositPoolCreatorEvent,
        InitializeCreatorPoolEvent, InitializeStakingPoolEvent, UnstakeEvent,
        UpdateRewardIndexEvent,
    },
    common::COINS_REPOSITORY,
    utils::common::get_real_token_decimals,
};

use std::str::FromStr;

use alloy::primitives::utils::format_units;
use bson::Decimal128;
use serde::{Deserialize, Serialize};

use crate::{
    abis::MoonbagsStake::StakeEvent, db::utils::traits::RepositorySharedMethod,
    services::redis_service::REDIS_EMITTER,
};
use anyhow::Result;

pub async fn handle_initialize_staking_pool_event(event: &InitializeStakingPoolEvent) {
    let token_address = &event.tokenAddress.to_string();
    // TODO: Add fields for staking token pool initialization
    match COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
            },
            doc! {
                "$setOnInsert": {
                    "tokenAddress": &token_address,
                },
            },
            FindOneAndUpdateOptions::builder().upsert(true).build(),
        )
        .await
    {
        Ok(_) => {
            tracing::info!("Updated staking pool for {}", token_address);
        }
        Err(err) => {
            tracing::error!(
                "Failed to update staking pool for {}: {:?}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_initialize_creator_pool_event(event: &InitializeCreatorPoolEvent, log: &Log) {
    let token_address = event.tokenAddress.to_string();
    let timestamp_ms = event.timestamp.to::<u64>() * 1000;

    let event_uuid = generate_event_uuid(timestamp_ms, log.log_index.unwrap());
    // TODO: Add fields for creator token pool initialization
    match COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardCreatorPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardCreatorPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardCreatorPool.event_uuid": event_uuid,
                },
                "$setOnInsert": {
                    "tokenAddress": &token_address,
                },
            },
            FindOneAndUpdateOptions::builder().upsert(true).build(),
        )
        .await
    {
        Ok(_) => {
            tracing::info!("Updated creator pool for {}", token_address);
        }
        Err(err) => {
            tracing::error!(
                "Failed to update creator pool for {}: {:?}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_stake_event(event: &StakeEvent, log: &Log) {
    let token_address = &event.tokenAddress.to_string();
    let staking_amount =
        format_units(event.amount, get_real_token_decimals(token_address)).unwrap();
    let timestamp_ms = event.timestamp.to::<u64>() * 1000;
    let event_uuid = generate_event_uuid(timestamp_ms, log.log_index.unwrap());
    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$inc": {
                    "rewardTokenPool.stakedAmount": Decimal128::from_str(&staking_amount).unwrap()
                },
                "$set": {
                    "rewardTokenPool.event_uuid": event_uuid,
                },
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update token staking amount for {}: {:?}",
            token_address,
            result.err()
        );
        return;
    };
    let Some(updated_coin) = opt_coin else {
        tracing::error!(
            "Failed to update token staking amount for {}: {}",
            token_address,
            "Token not found"
        );
        return;
    };

    let new_staked_amount = updated_coin
        .reward_token_pool
        .and_then(|pool| pool.staked_amount)
        .map(|amount| amount.to_string())
        .unwrap();

    tracing::info!(
        "Updated token staking amount for {}: amount={}, new_staked_amount={}",
        token_address,
        staking_amount,
        new_staked_amount
    );

    let pub_event = PubStakingEvent {
        token_address: token_address.clone(),
        amount: staking_amount.to_string(),
        user_address: event.staker.to_string().clone(),
        timestamp: event.timestamp.to::<u64>() * 1000,
    };

    if let Err(e) = emit_staking_event_to_coin_room(&pub_event, token_address) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) = emit_staking_event_to_staking_room(&pub_event) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }
}

pub async fn handle_unstake_event(event: &UnstakeEvent, log: &Log) {
    let token_address = event.tokenAddress.to_string();
    let unstaking_amount =
        format_units(event.amount, get_real_token_decimals(&token_address)).unwrap();
    let negative_amount = -Decimal::from_str(&unstaking_amount).unwrap();
    let timestamp_ms = event.timestamp.to::<u64>() * 1000;

    let event_uuid = generate_event_uuid(timestamp_ms, log.log_index.unwrap());
    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address, 
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$inc": {
                    "rewardTokenPool.stakedAmount": Decimal128::from_str(&negative_amount.to_string()).unwrap()
                },
                "$set": {
                    "rewardTokenPool.event_uuid": event_uuid,
                },
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update token staking amount for {}: {:?}",
            token_address,
            result.err()
        );
        return;
    };

    let Some(updated_coin) = opt_coin else {
        tracing::error!(
            "Failed to update token staking amount for {}: {}",
            token_address,
            "Token not found"
        );
        return;
    };

    let new_staked_amount = updated_coin
        .reward_token_pool
        .and_then(|pool| pool.staked_amount)
        .map(|amount| amount.to_string())
        .unwrap();

    tracing::info!(
        "Updated token staking amount for {}: amount=-{}, new_staked_amount={}",
        token_address,
        unstaking_amount,
        new_staked_amount
    );

    let pub_event = PubStakingEvent {
        token_address: token_address.clone(),
        amount: negative_amount.to_string(),
        user_address: event.unstaker.to_string(),
        timestamp: timestamp_ms,
    };

    if let Err(e) = emit_staking_event_to_coin_room(&pub_event, &token_address) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) = emit_staking_event_to_staking_room(&pub_event) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }
}

pub async fn handle_update_reward_for_token_pool(
    event: &UpdateRewardIndexEvent,
    log: &Log,
) -> Result<()> {
    let token_address = event.tokenAddress.to_string();
    let reward_amount = format_ether(event.reward);
    let timestamp_ms = event.timestamp.to::<u64>() * 1000;
    let event_uuid = generate_event_uuid(timestamp_ms, log.log_index.unwrap());

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardTokenPool.event_uuid": event_uuid,
                },
                "$inc": {
                    "rewardTokenPool.totalReward": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
                },
            },
            None,
        )
    .await;
    match result {
        Ok(Some(_coin)) => {
            tracing::info!(
                "Updated reward for token pool for {}: with new reward amount={}",
                token_address,
                reward_amount,
            );
        }
        Ok(None) => {
            tracing::warn!(
                "Failed to update reward for token pool for {}",
                token_address,
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update reward for token pool for {}: {}",
                token_address,
                err
            );
        }
    }

    Ok(())
}

pub async fn handle_update_reward_for_creator_pool(event: &DepositPoolCreatorEvent, log: &Log) {
    let token_address = event.tokenAddress.to_string();
    let reward_amount = format_ether(event.amount);
    let timestamp_ms = event.timestamp.to::<u64>() * 1000;
    let event_uuid = generate_event_uuid(timestamp_ms, log.log_index.unwrap());

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {"tokenAddress": &token_address, 
                "$or": [
                    {"rewardCreatorPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardCreatorPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardCreatorPool.event_uuid": event_uuid,
                },
                "$inc": {
                    "rewardCreatorPool.totalReward": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
                },
            },
            None,
        )
    .await;

    match result {
        Ok(Some(_coin)) => {
            tracing::info!(
                "Updated reward for creator pool for {}: with new amount={}",
                token_address,
                reward_amount,
            );
        }
        Ok(None) => {
            tracing::warn!(
                "Failed to update reward for creator pool for {}",
                token_address,
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to update reward for creator pool for {}: {}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_claim_staking_pool_event(event: &ClaimStakingPoolEvent, log: &Log) {
    let token_address = event.tokenAddress.to_string();
    let reward_amount = format_ether(event.reward);
    let timestamp_ms = event.timestamp.to::<u64>() * 1000;
    let event_uuid = generate_event_uuid(timestamp_ms, log.log_index.unwrap());

    let update = doc! {
        "$set": {
            "rewardTokenPool.event_uuid": event_uuid,
        },
        "$inc": {
            "rewardTokenPool.rewardClaimed": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
        },
    };

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardTokenPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardTokenPool.event_uuid": {"$exists": false}}
                ],
            },
            update,
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    match result {
        Ok(Some(coin)) => {
            tracing::info!(
                "Updated hype staking amount for {}: new_reward_claimed_amount={}",
                token_address,
                coin.reward_token_pool
                    .as_ref()
                    .unwrap()
                    .reward_claimed
                    .unwrap()
                    .to_string(),
            );
        }
        Ok(None) => {
            tracing::error!("Not to update hype staking amount for {} ", token_address,);
        }
        Err(err) => {
            tracing::error!(
                "Failed to update hype staking amount for {}: {}",
                token_address,
                err
            );
        }
    }
}

pub async fn handle_claim_creator_pool_event(event: &ClaimCreatorPoolEvent, log: &Log) {
    let token_address = event.tokenAddress.to_string();
    let reward_amount = format_ether(event.reward);
    let timestamp_ms = event.timestamp.to::<u64>() * 1000;
    let event_uuid = generate_event_uuid(timestamp_ms, log.log_index.unwrap());
    let result =  COINS_REPOSITORY
        .get_collection().find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
                "$or": [
                    {"rewardCreatorPool.event_uuid": {"$lt": event_uuid}},
                    {"rewardCreatorPool.event_uuid": {"$exists": false}}
                ],
            },
            doc! {
                "$set": {
                    "rewardCreatorPool.event_uuid": event_uuid,
                },
                "$inc": {
                    "rewardCreatorPool.rewardClaimed": Decimal128::from_str(&reward_amount.to_string()).unwrap(),
                },
            },
            FindOneAndUpdateOptions::builder().return_document(ReturnDocument::After)
                .build(),
        )
        .await;
    match result {
        Ok(Some(coin)) => {
            tracing::info!(
                "Updated creator amount for {}: new_reward_claimed_amount={}",
                token_address,
                coin.reward_creator_pool
                    .as_ref()
                    .unwrap()
                    .reward_claimed
                    .unwrap()
                    .to_string(),
            );
        }
        Ok(None) => {
            tracing::warn!("Failed to update creator amount for {}", token_address,);
        }
        Err(err) => {
            tracing::error!(
                "Failed to update creator amount for {}: {}",
                token_address,
                err
            );
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubStakingEvent {
    pub token_address: String,
    pub user_address: String,
    pub amount: String,
    pub timestamp: u64,
}

fn emit_staking_event_to_coin_room(
    event: &PubStakingEvent,
    token_address: &str,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(event)?;
    let room = format!("SUBSCRIBE_COIN::{token_address}");
    let event_type = "UpdateStaking";
    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(&room, event_type, &pub_data);
    Ok(())
}

fn emit_staking_event_to_staking_room(event: &PubStakingEvent) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(event)?;
    let room = "SUBSCRIBE_STAKING";
    let event_type = "UpdateStaking";
    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(room, event_type, &pub_data);
    Ok(())
}

fn generate_event_uuid(event_timestamp: u64, event_seq: u64) -> i64 {
    let multiplier = 10000;
    (event_timestamp * multiplier + event_seq) as i64
}
