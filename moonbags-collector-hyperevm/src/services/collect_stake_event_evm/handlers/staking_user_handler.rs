use std::str::FromStr;

use alloy::primitives::utils::{format_ether, format_units};
use bson::{doc, Decimal128};
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument};

use crate::{
    abis::MoonbagsStake::{ClaimStakingPoolEvent, StakeEvent, UnstakeEvent},
    common::STAKING_USERS_REPOSITORY,
    db::utils::traits::RepositorySharedMethod,
    utils::common::get_real_token_decimals,
};

pub async fn handle_stake_event(event: &StakeEvent) {
    let token_address = &event.tokenAddress.to_string();
    let staker = &event.staker.to_string();

    let staking_amount =
        format_units(event.amount, get_real_token_decimals(token_address)).unwrap();
    let staking_amount_decimal = Decimal128::from_str(&staking_amount).unwrap_or_else(|e| {
        tracing::error!("Failed to parse staking amount: {}", e);
        Decimal128::from_str("0").unwrap()
    });

    let result = STAKING_USERS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "stakingCoinAddress": token_address,
                "userAddress": staker,
            },
            doc! {
                "$inc": {
                    "stakedAmount": staking_amount_decimal
                },
                "$set": {
                    "lastStake": bson::to_bson(&event.timestamp).unwrap(),
                },
                "$setOnInsert": {
                    "stakingCoinAddress": token_address,
                    "userAddress": staker,
                }
            },
            FindOneAndUpdateOptions::builder()
                .upsert(true)
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    match result {
        Ok(Some(user)) => {
            tracing::info!(
                "Updated staking user: token_address={}, user_address={}, amount={}, new_staked_amount={}",
                token_address,
                staker,
                staking_amount,
                user.staked_amount
            );
        }
        Ok(None) => {
            tracing::error!(
                "Failed to staking user: token_address={}, user_address={}, error={}",
                token_address,
                staker,
                "User not found"
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to staking user: token_address={}, user_address={}, error={}",
                token_address,
                staker,
                err
            );
        }
    }
}

pub async fn handle_unstake_event(event: &UnstakeEvent) {
    let token_address = &event.tokenAddress.to_string();
    let unstaker = &event.unstaker.to_string();

    let unstaking_amount =
        format_units(event.amount, get_real_token_decimals(token_address)).unwrap();
    let negative_amount =
        Decimal128::from_str(&format!("-{unstaking_amount}")).unwrap_or_else(|e| {
            tracing::error!("Failed to parse unstaking amount: {}", e);
            Decimal128::from_str("0").unwrap()
        });
    let result = STAKING_USERS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "stakingCoinAddress": token_address,
                "userAddress": unstaker,
            },
            doc! {
                "$inc": { "stakedAmount": negative_amount },
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    match result {
        Ok(Some(user)) => {
            tracing::info!(
                "Unstaked for user: token_address={}, user_address={}, amount={}, new_staked_amount={}",
                token_address,
                unstaker,
                unstaking_amount,
                user.staked_amount
            );
        }
        Ok(None) => {
            tracing::error!(
                "Failed to unstaking user: token_address={}, user_address={}, error={}",
                token_address,
                unstaker,
                "User not found"
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to unstaking user: token_address={}, user_address={}, error={}",
                token_address,
                unstaker,
                err
            );
        }
    }
}

pub async fn handle_claim_staking_pool_event(event: &ClaimStakingPoolEvent) {
    let token_address = &event.tokenAddress.to_string();
    let claimer = &event.claimer.to_string();
    let undecimal_claimed_reward = format_ether(event.reward);
    match STAKING_USERS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "userAddress": claimer,
                "stakingCoinAddress": token_address,
            },
            doc! {
                "$inc": {
                    "rewardClaimed": Decimal128::from_str(&undecimal_claimed_reward.to_string()).unwrap(),
                }
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await
    {
        Ok(Some(user)) => {
            tracing::info!(
                "Claimed reward for user: token_address={}, user_address={}, amount={}, new_reward_claimed={}",
                token_address,
                claimer,
                undecimal_claimed_reward,
                user.reward_claimed.unwrap()
            );
        }
        Ok(None) => {
            tracing::error!(
                "Failed to claim reward for user: token_address={}, user_address={}, error={}",
                token_address,
                claimer,
                "User not found"
            );
        }
        Err(err) => {
            tracing::error!(
                "Failed to claim reward for user: token_address={}, user_address={}, error={}",
                token_address,
                claimer,
                err
            );
        }
    }
}
