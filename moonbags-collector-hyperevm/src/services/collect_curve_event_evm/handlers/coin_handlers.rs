use std::str::FromStr;

use alloy::primitives::U256;
use anyhow::Result;
use bson::{doc, DateTime, Decimal128};
use chrono::Utc;
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Serialize};
use tracing;

use crate::{
    abis::MoonbagsLaunchpad::{TokenCreated, Trade, V3PoolCreated},
    common::{COINS_REPOSITORY, TOKEN_PRICES_REPOSITORY},
    db::{
        coins::{Coins, Socials},
        utils::traits::RepositorySharedMethod,
    },
    services::{
        kafka::{enums::KafkaTopic, kafka_service::KafkaService},
        redis_service::REDIS_EMITTER,
    },
    utils::common::{
        calculate_bonding_curve_progress, generate_coin_slug, get_market_cap_hype, MigrateDex,
        ToF64, FEE_DENOMINATOR,
    },
};

pub async fn handle_token_created_event(event: &TokenCreated) {
    let platform_fee_withdraw_rate = (Decimal::from_u16(event.platformFeeWithdraw).unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let creator_fee_withdraw_rate = (Decimal::from_u16(event.creatorFeeWithdraw).unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let stake_fee_withdraw_rate = (Decimal::from_u16(event.stakeFeeWithdraw).unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let platform_stake_fee_withdraw_rate = (Decimal::from_u16(event.platformStakeFeeWithdraw)
        .unwrap()
        / Decimal::from_u64(FEE_DENOMINATOR).unwrap())
    .to_string();
    let remain_token_reserves = event.virtualTokenReserves - event.realTokenReserves;

    // Calculate threshold by breaking down the calculation to avoid overflow
    let virtual_hype = event.virtualHypeReserves;
    let real_token = event.realTokenReserves;
    let threshold = (virtual_hype / remain_token_reserves) * real_token;
    let bonding_dex = "Hyperswap".to_string();
    let slug = generate_coin_slug(&event.name);

    let update_doc = doc! {
        "$set": {
            "name": event.name.clone(),
            "symbol": event.symbol.clone(),
            "slug": slug.clone(),
            "description": event.description.clone(),
            "logoUri": event.uri.clone(),
            "socials": {
                "telegram": event.telegram.clone(),
                "website": event.website.clone(),
                "x": event.twitter.clone(),
            },
            "creatorAddress": event.creator.to_string(),
            "threshold": Decimal128::from_str(&threshold.to_string()).unwrap(),
            "feeRates":{
                "platformFeeWithdrawRate": Decimal128::from_str(&platform_fee_withdraw_rate).unwrap(),
                "creatorFeeWithdrawRate": Decimal128::from_str(&creator_fee_withdraw_rate).unwrap(),
                "stakeFeeWithdrawRate": Decimal128::from_str(&stake_fee_withdraw_rate).unwrap(),
                "platformStakeFeeWithdrawRate": Decimal128::from_str(&platform_stake_fee_withdraw_rate).unwrap(),
            },
            "createdAt": DateTime::from_millis(event.timestamp.to::<i64>() * 1000),
            "realHypeReserves": Decimal128::from_str(&event.realHypeReserves.to_string()).unwrap(),
            "realTokenReserves": Decimal128::from_str(&event.realTokenReserves.to_string()).unwrap(),
            "mcap": Decimal128::from_str("0").unwrap(),
            "mcapUsd": Decimal128::from_str("0").unwrap(),
            "virtualHypeReserves": Decimal128::from_str(&event.virtualHypeReserves.to_string()).unwrap(),
            "virtualTokenReserves": Decimal128::from_str(&event.virtualTokenReserves.to_string()).unwrap(),
            "initVirtualHypeReserves": Decimal128::from_str(&event.virtualHypeReserves.to_string()).unwrap(),
            "initVirtualTokenReserves": Decimal128::from_str(&event.virtualTokenReserves.to_string()).unwrap(),
            "bondingCurve": 0.0,
            "bondingDex": bonding_dex.to_string(),
        }
    };

    if let Err(e) = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": event.token.to_string(),
            },
            update_doc,
            FindOneAndUpdateOptions::builder().upsert(true).build(),
        )
        .await
    {
        tracing::error!(
            "Failed to insert database for coin {}: {}",
            event.token.to_string(),
            e
        );
        return;
    }

    tracing::info!("Coin inserted: {}", event.token.to_string());

    // Send create coin event to ws server
    let pub_coin = PubCreatedCoins {
        token_address: event.token.to_string(),
        name: event.name.clone(),
        symbol: event.symbol.clone(),
        slug: slug.clone(),
        logo_uri: event.uri.clone(),
        description: event.description.clone(),
        socials: Socials {
            telegram: event.telegram.clone(),
            website: event.website.clone(),
            x_social: event.twitter.clone(),
        },
        creator_address: event.creator.to_string(),
        virtual_hype_reserves: event.virtualHypeReserves.to_string(),
        virtual_token_reserves: event.virtualTokenReserves.to_string(),
        real_token_reserves: event.realTokenReserves.to_string(),
        threshold: threshold.to::<u64>().to_string(),
        create_at: event.timestamp.to::<i64>() * 1000,
    };

    if let Err(e) = emit_create_coin_event(&pub_coin) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) = KafkaService::publish_messages(KafkaTopic::CreatedCoin, &vec![pub_coin]).await {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }
}

fn emit_create_coin_event(coin: &PubCreatedCoins) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(coin)?;
    let room = "SUBSCRIBE_NEW_COIN";
    let event = "CreatedCoin";

    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(room, event, &pub_data);

    Ok(())
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubCreatedCoins {
    pub token_address: String,
    pub name: String,
    pub symbol: String,
    pub slug: String,
    pub description: String,
    pub logo_uri: String,
    pub socials: Socials,
    pub creator_address: String,
    pub virtual_hype_reserves: String,
    pub virtual_token_reserves: String,
    pub real_token_reserves: String,
    pub threshold: String,
    pub create_at: i64,
}

pub async fn handle_v3_pool_created_event(event: &V3PoolCreated) {
    let timestamp_ms = event.timestamp.to::<i64>() * 1000;
    let update_object = doc! {
        "$set": {
            "listedPoolId": event.v3Pool.to_string(),
            "bondingDex": MigrateDex::Hyperswap.to_string(),
            "listedAt": DateTime::from_millis(timestamp_ms),
        }
    };

    let result = COINS_REPOSITORY
        .update_one(
            doc! {
                "tokenAddress": &event.token.to_string(),
            },
            update_object,
            None,
        )
        .await;

    let Ok(result) = result else {
        tracing::error!(
            "Failed to update database for coin {}: {:?}",
            &event.token.to_string(),
            result.err()
        );
        return;
    };

    let event_bonding_completed_coin = BondingCompletedCoin {
        token_address: event.token.to_string(),
        listed_pool_id: event.v3Pool.to_string(),
        listed_at: DateTime::from_millis(timestamp_ms).to_chrono(),
        bonding_dex: MigrateDex::Hyperswap.to_string(),
    };

    // Send update coin event
    if let Err(e) = emit_bonding_completed_coin_event(&event_bonding_completed_coin) {
        tracing::error!("Failed to emit bonding completed event to ws server: {}", e);
    }

    tracing::info!(
        "Coin info updated for: coin={}, data={:?}",
        &event.token.to_string(),
        &result
    );
}

pub fn emit_bonding_completed_coin_event(payload: &BondingCompletedCoin) -> anyhow::Result<()> {
    let string_payload = serde_json::to_string(payload)?;
    let room = format!("SUBSCRIBE_COIN::{}", payload.token_address);
    let event = "BondingCompleted";

    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(&room, event, &string_payload);

    emit_bonding_change_event(&PubBondingChange {
        token_address: payload.token_address.clone(),
        bonding_curve_progress: 100.0,
    })?;

    Ok(())
}

pub async fn handle_traded_event(event: &Trade) -> Result<()> {
    let token_address = event.token.to_string();

    let token = match find_coin_by_token_address(&token_address).await {
        Ok(coin) => coin,
        Err(e) => {
            tracing::error!("{}", e);
            return Ok(());
        }
    };

    let mcap = get_market_cap_hype(
        event.virtualTokenReserves,
        event.virtualHypeReserves,
        token
            .init_virtual_token_reserves
            .unwrap()
            .to_string()
            .parse::<U256>()
            .unwrap(),
    );
    let bonding_curve_progress = calculate_bonding_curve_progress(
        event
            .realHypeReserves
            .to_string()
            .parse::<Decimal>()
            .unwrap(),
        token
            .threshold
            .unwrap()
            .to_string()
            .parse::<Decimal>()
            .unwrap(),
    );

    let hype_price_usd = TOKEN_PRICES_REPOSITORY.get_cached_hype_price().await?;
    let bg_hype_price_usd = Decimal::from_str(&hype_price_usd).unwrap();
    let bg_mcap = Decimal::from_str(&mcap.to_string()).unwrap();
    let mcap_usd = (bg_mcap * bg_hype_price_usd).to_f64();

    let update_object = doc! {
        "realHypeReserves": Decimal128::from_str(&event.realHypeReserves.to_string()).unwrap(),
        "realTokenReserves": Decimal128::from_str(&event.realTokenReserves.to_string()).unwrap(),
        "virtualHypeReserves": Decimal128::from_str(&event.virtualHypeReserves.to_string()).unwrap(),
        "virtualTokenReserves": Decimal128::from_str(&event.virtualTokenReserves.to_string()).unwrap(),
        "mcap": Decimal128::from_str(&mcap.to_string()).unwrap(),
        "mcapUsd": Decimal128::from_str(&mcap_usd.to_string()).unwrap(),
        "bondingCurve": bonding_curve_progress,
    };

    let pub_updated_coin = PubUpdatedCoins {
        mcap: bg_mcap.to_f64(),
        mcap_usd,
        virtual_hype_reserves: event.virtualHypeReserves.to_string(),
        virtual_token_reserves: event.virtualTokenReserves.to_string(),
        real_hype_reserves: event.realHypeReserves.to_string(),
        real_token_reserves: event.realTokenReserves.to_string(),
        bonding_curve_progress,
        token_address: token_address.clone(),
    };

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
            },
            vec![
                doc! {
                    "$set": {
                        "prevMcap": "$mcap",
                        "prevRealHypeReserves": "$realHypeReserves",
                    }
                },
                doc! {
                    "$set": update_object.clone(),
                },
            ],
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update database for coin {}: {:?}",
            &token_address,
            result.err()
        );
        return Ok(());
    };

    if opt_coin.is_none() {
        tracing::error!(
            "Failed to update database for coin {}: {}",
            &token_address,
            "Coin not found"
        );
        return Ok(());
    }

    tracing::info!(
        "Coin info updated for: coin={}, data={:?}",
        &token_address,
        &update_object
    );

    // Send mcap change event
    let coin = opt_coin.unwrap();
    let mcap_change = if let Some(prev_mcap) = coin.prev_mcap {
        bg_mcap.to_f64() - prev_mcap.to_f64()
    } else {
        bg_mcap.to_f64()
    };

    if let Err(e) = emit_bonding_change_event(&PubBondingChange {
        bonding_curve_progress,
        token_address: token_address.clone(),
    }) {
        tracing::error!("Failed to emit bonding change event: {}", e);
    }

    let pub_mcap_change = PubMcapChange {
        token_address: token_address.clone(),
        mcap_change,
    };

    if let Err(e) = emit_mcap_change_event(&pub_mcap_change) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::McapChange, &vec![pub_mcap_change]).await
    {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }

    // Send new mcap event
    let pub_updated_mcap = PubUpdatedMcap {
        mcap: bg_mcap.to_f64(),
        mcap_usd,
        token_address: token_address.clone(),
    };

    if let Err(e) = emit_updated_mcap_event(&pub_updated_mcap, &token_address) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::UpdatedMcap, &vec![pub_updated_mcap]).await
    {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }

    // Send update coin event
    if let Err(e) = emit_update_coin_event(&pub_updated_coin, &token_address) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::UpdatedCoin, &vec![pub_updated_coin]).await
    {
        tracing::error!("Failed to publish messages to Kafka: {}", e);
    }

    Ok(())
}

async fn find_coin_by_token_address(token_address: &str) -> anyhow::Result<Coins, anyhow::Error> {
    let result = COINS_REPOSITORY
        .find_one(doc! { "tokenAddress": token_address }, None)
        .await;

    let Ok(opt_coin) = result else {
        return Err(anyhow::anyhow!(
            "Failed to find coin for token address {}: {:?}",
            token_address,
            result.err()
        ));
    };

    let Some(coin) = opt_coin else {
        return Err(anyhow::anyhow!(
            "Failed to find coin for token address {}: {}",
            token_address,
            "Coin not found"
        ));
    };
    Ok(coin)
}

fn emit_mcap_change_event(pub_mcap_change: &PubMcapChange) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(pub_mcap_change)?;
    let room = "SUBSCRIBE_MCAP";
    let event = "McapChange";

    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(room, event, &pub_data);
    Ok(())
}

fn emit_updated_mcap_event(pub_data: &PubUpdatedMcap, token_address: &str) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(pub_data)?;
    let room = format!("SUBSCRIBE_MCAP::{token_address}");
    let event = "UpdatedMcap";

    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(&room, event, &pub_data);
    Ok(())
}

fn emit_update_coin_event(coin: &PubUpdatedCoins, token_address: &str) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(coin)?;
    let room = format!("SUBSCRIBE_COIN::{token_address}");
    let event = "UpdatedCoin";

    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(&room, event, &pub_data);
    Ok(())
}

fn emit_bonding_change_event(pub_data: &PubBondingChange) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(pub_data)?;
    let room = "SUBSCRIBE_BONDING".to_string();
    let event = "BondingChange";

    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(&room, event, &pub_data);
    Ok(())
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubUpdatedCoins {
    pub mcap: f64,
    pub mcap_usd: f64,
    pub virtual_hype_reserves: String,
    pub virtual_token_reserves: String,
    pub real_hype_reserves: String,
    pub real_token_reserves: String,
    pub bonding_curve_progress: f64,
    pub token_address: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubMcapChange {
    pub token_address: String,
    pub mcap_change: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubUpdatedMcap {
    pub token_address: String,
    pub mcap: f64,
    pub mcap_usd: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubBondingChange {
    pub token_address: String,
    pub bonding_curve_progress: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct BondingCompletedCoin {
    pub token_address: String,
    pub listed_pool_id: String,
    pub listed_at: chrono::DateTime<Utc>,
    pub bonding_dex: String,
}
