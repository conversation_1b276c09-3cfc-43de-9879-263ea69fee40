use std::str::FromStr;

use crate::{
    abis::{IERC20Metadata, MoonbagsLaunchpad::Trade},
    common::{
        get_evm_provider_instance, CANDLES_REPOSITORY, COINS_REPOSITORY, TOKEN_PRICES_REPOSITORY,
        TRADES_REPOSITORY,
    },
    db::{
        candles::{Candle, Candles},
        trades::{ETradeType, Trades},
        utils::traits::RepositorySharedMethod,
    },
    services::{
        kafka::{enums::KafkaTopic, kafka_service::KafkaService},
        redis_service::REDIS_EMITTER,
    },
    utils::common::{COIN_TOTAL_SUPPLY, TOKEN_DECIMALS},
};
use ::serde::{Deserialize, Serialize};
use alloy::{
    primitives::{
        utils::{format_ether, format_units},
        Address, U256,
    },
    rpc::types::Log,
};
use anyhow::Result;
use bson::{doc, <PERSON><PERSON>, Decimal128};
use mongodb::options::FindOneOptions;
use rust_decimal::Decimal;

pub async fn handle_traded_event_for_candles_and_trades(event: &Trade, log: &Log) -> Result<()> {
    let maker = event.user.clone().to_string();
    let token_address = event.token.clone().to_string();
    let tx_digest = log
        .transaction_hash
        .map(|h| h.to_string())
        .unwrap_or_default();
    let index = log.transaction_index.unwrap_or_default();

    let (virtual_token_reserves, virtual_hype_reserves) =
        (event.virtualTokenReserves, event.virtualHypeReserves);

    let virtual_token_reserves_without_decimals =
        Decimal::from_str(&format_units(virtual_token_reserves, TOKEN_DECIMALS).unwrap()).unwrap();
    let virtual_hype_reserves_without_decimals =
        Decimal::from_str(&format_ether(virtual_hype_reserves)).unwrap();
    let base_price_quote =
        virtual_hype_reserves_without_decimals / virtual_token_reserves_without_decimals;

    let quote_price_usd_str = TOKEN_PRICES_REPOSITORY
        .get_cached_hype_price()
        .await
        .map_err(|e| {
            tracing::error!("Failed to get HYPE price: {}", e);
            e
        })?;
    let quote_price_usd = Decimal::from_str(&quote_price_usd_str).unwrap();

    let hype_amount_without_decimals = Decimal::from_str(&format_ether(event.hypeAmount)).unwrap();
    let token_amount_without_decimals =
        Decimal::from_str(&format_units(event.tokenAmount, TOKEN_DECIMALS).unwrap()).unwrap();
    let base_price_usd = quote_price_usd * base_price_quote;
    let volume_usd = hype_amount_without_decimals * quote_price_usd;

    let Ok(token_symbol) = get_symbol_token(&token_address).await else {
        tracing::error!("Failed to get token symbol: {:?}", token_address);
        return Ok(());
    };
    let fee_without_decimals = format_ether(event.fee);

    let to_decimal_u256 =
        |value: U256| -> Decimal128 { Decimal128::from_str(&value.to_string()).unwrap() };
    let timestamp_ms = (event.timestamp * U256::from(1000)).to::<u64>();

    let trade_record = Trades {
        token_address: token_address.clone(),
        token_symbol: token_symbol.clone(),
        quote_amount: hype_amount_without_decimals.to_string(),
        base_amount: token_amount_without_decimals.to_string(),
        trade_type: if event.isBuy {
            ETradeType::Buy
        } else {
            ETradeType::Sell
        },
        maker: maker.clone(),
        timestamp: timestamp_ms,
        virtual_hype_reserves: to_decimal_u256(event.virtualHypeReserves),
        virtual_token_reserves: to_decimal_u256(event.virtualTokenReserves),
        real_hype_reserves: to_decimal_u256(event.realHypeReserves),
        real_token_reserves: to_decimal_u256(event.realTokenReserves),
        hash: tx_digest.to_string().clone(),
        index: log.log_index.unwrap_or_default(),
        price: base_price_quote.to_string(),
        price_usd: base_price_usd.to_string(),
        volume: hype_amount_without_decimals.to_string(),
        volume_usd: volume_usd.to_string(),
        checkpoint: log.block_number,
        fee: fee_without_decimals.clone().to_string(),
    };

    let pub_trade_data = PubTradesData {
        hash: tx_digest.to_string(),
        token_address: token_address.clone(),
        token_symbol,
        index,
        maker: maker.clone(),
        trade_type: trade_record.trade_type,
        base_amount: trade_record.base_amount.to_string(),
        quote_amount: trade_record.quote_amount.to_string(),
        price: trade_record.price.to_string(),
        price_usd: trade_record.price_usd.to_string(),
        volume: trade_record.volume.to_string(),
        volume_usd: trade_record.volume_usd.to_string(),
        timestamp: timestamp_ms,
        virtual_hype_reserves: trade_record.virtual_hype_reserves.to_string(),
        virtual_token_reserves: trade_record.virtual_token_reserves.to_string(),
        checkpoint: log.block_number,
        real_hype_reserves: trade_record.real_hype_reserves.to_string(),
        real_token_reserves: trade_record.real_token_reserves.to_string(),
        fee: fee_without_decimals.to_string(),
    };

    REDIS_EMITTER.get().unwrap().emit_room(
        &format!("SUBSCRIBE_COIN::{}", event.token),
        "CreatedTrade",
        &serde_json::to_string(&pub_trade_data)?,
    );

    REDIS_EMITTER.get().unwrap().emit_room(
        "SUBSCRIBE_TRADES",
        "CreatedTrade",
        &serde_json::to_string(&pub_trade_data)?,
    );

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::CreatedTrade, &vec![pub_trade_data]).await
    {
        tracing::error!("Failed to publish trade to Kafka: {}", e);
    }

    match TRADES_REPOSITORY
        .insert_one_or_ignore(trade_record, None)
        .await
    {
        Ok(Some(_)) => {
            handle_update_candle_per_swap(
                token_address,
                String::from("hype"),
                (event.timestamp * U256::from(1000)).to::<u64>(),
                base_price_quote,
                base_price_usd,
                token_amount_without_decimals,
                hype_amount_without_decimals,
            )
            .await?;
        }
        Ok(None) => {
            tracing::info!("Trade record already exists, skipping candle update");
        }
        Err(e) => {
            tracing::error!("Failed to insert trade record: {}", e);
            return Err(e);
        }
    }
    Ok(())
}

async fn get_symbol_token(token_address: &str) -> anyhow::Result<String> {
    let result = COINS_REPOSITORY
        .find_one(
            doc! {
                "tokenAddress": token_address
            },
            None,
        )
        .await;

    if let Ok(Some(coin)) = result {
        return Ok(coin.symbol.unwrap_or_else(|| "UNKNOWN".to_string()));
    }

    let provider = get_evm_provider_instance();
    let token_addr: Address = token_address
        .parse()
        .map_err(|e| anyhow::anyhow!("Invalid token address: {}", e))?;

    let contract = IERC20Metadata::new(token_addr, provider);
    // symbol() function signature hash: 0x95d89b41
    let symbol = contract.symbol().call().await.map_err(|e| {
        tracing::error!("Failed to get symbol for token {}: {}", token_address, e);
        e
    })?;

    Ok(symbol)
}

#[allow(clippy::too_many_arguments)]
async fn update_candle_per_swap(
    base_token: String,
    _quote_token: String,
    timestamp: u64,
    price: Decimal,
    price_usd: Decimal,
    base_amount_without_decimals: Decimal,
    quote_amount_without_decimals: Decimal,
) -> anyhow::Result<()> {
    let resolution_options = [1, 60, 900, 3600];
    let resolution_ms_options = resolution_options
        .into_iter()
        .map(|r| r * 1000)
        .collect::<Vec<u64>>();
    let volume_base_decimal = base_amount_without_decimals;
    let volume_quote_decimal = quote_amount_without_decimals;
    let volume_usd_decimal = volume_base_decimal * price_usd;

    let volume_base = Decimal128::from_str(&volume_base_decimal.to_string()).unwrap();
    let volume_quote = Decimal128::from_str(&volume_quote_decimal.to_string()).unwrap();
    let volume_usd = Decimal128::from_str(&volume_usd_decimal.to_string()).unwrap();

    let coin = COINS_REPOSITORY
        .find_one(
            doc! {
                "tokenAddress": &base_token,
            },
            None,
        )
        .await?
        .ok_or_else(|| anyhow::anyhow!("Coin not found"))?;
    let token_base_supply =
        Decimal::from_str(&format_units(COIN_TOTAL_SUPPLY, TOKEN_DECIMALS).unwrap()).unwrap();

    let market_cap = token_base_supply * price;
    let market_cap_usd = token_base_supply * price_usd;

    for resolution in resolution_ms_options {
        let resolution_timestamp = timestamp - (timestamp % resolution);

        let options = FindOneOptions::builder()
            .sort(doc! { "timestamp": -1_i32 })
            .build();

        let pre_candle = CANDLES_REPOSITORY
            .find_one(
                doc! {"tokenAddress": base_token.clone(), "resolution": resolution as u32},
                options,
            )
            .await?;

        let mut price_low = price;
        let mut price_high = price;
        let mut price_open = price;

        let mut price_usd_low = price_usd;
        let mut price_usd_high = price_usd;
        let mut price_usd_open = price_usd;

        let mut market_cap_low = market_cap;
        let mut market_cap_high = market_cap;
        let mut market_cap_open = market_cap;

        let mut market_cap_usd_low = market_cap_usd;
        let mut market_cap_usd_high = market_cap_usd;
        let mut market_cap_usd_open = market_cap_usd;

        // Case 1
        // when candle exist
        if let Some(candle) = pre_candle
            .clone()
            .filter(|c| c.timestamp == resolution_timestamp)
        {
            let candle_price_low =
                Decimal::from_str(&candle.price.low.to_string()).unwrap_or(price);
            let candle_price_high =
                Decimal::from_str(&candle.price.high.to_string()).unwrap_or(price);
            let candle_price_usd_low =
                Decimal::from_str(&candle.price_usd.low.to_string()).unwrap_or(price_usd);
            let candle_price_usd_high =
                Decimal::from_str(&candle.price_usd.high.to_string()).unwrap_or(price_usd);
            let candle_market_cap_low =
                Decimal::from_str(&candle.market_cap.low.to_string()).unwrap_or(market_cap);
            let candle_market_cap_high =
                Decimal::from_str(&candle.market_cap.high.to_string()).unwrap_or(market_cap);
            let candle_market_cap_usd_low =
                Decimal::from_str(&candle.market_cap_usd.low.to_string()).unwrap_or(market_cap_usd);
            let candle_market_cap_usd_high =
                Decimal::from_str(&candle.market_cap_usd.high.to_string())
                    .unwrap_or(market_cap_usd);

            let volume_quote_decimal = Decimal::from_str(&candle.volume_quote)
                .unwrap_or(Decimal::ZERO)
                + volume_quote_decimal;
            let volume_base_decimal = Decimal::from_str(&candle.volume_base)
                .unwrap_or(Decimal::ZERO)
                + volume_base_decimal;
            let volume_usd_decimal =
                Decimal::from_str(&candle.volume_usd).unwrap_or(Decimal::ZERO) + volume_usd_decimal;

            let update_doc = doc! {
                "$set": {
                    "price": {
                        "low": Decimal128::from_str(&price.min(candle_price_low).to_string()).unwrap(),
                        "high": Decimal128::from_str(&price.max(candle_price_high).to_string()).unwrap(),
                        "open": Decimal128::from_str(&candle.price.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&price.to_string()).unwrap(),
                    },
                    "priceUsd": {
                        "low": Decimal128::from_str(&price_usd.min(candle_price_usd_low).to_string()).unwrap(),
                        "high": Decimal128::from_str(&price_usd.max(candle_price_usd_high).to_string()).unwrap(),
                        "open": Decimal128::from_str(&candle.price_usd.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&price_usd.to_string()).unwrap(),
                    },
                    "marketCap": {
                        "low": Decimal128::from_str(&market_cap.min(candle_market_cap_low).to_string()).unwrap(),
                        "high": Decimal128::from_str(&market_cap.max(candle_market_cap_high).to_string()).unwrap(),
                        "open": Decimal128::from_str(&candle.market_cap.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&market_cap.to_string()).unwrap(),
                    },
                    "marketCapUsd": {
                        "low": Decimal128::from_str(&market_cap_usd.min(candle_market_cap_usd_low).to_string()).unwrap(),
                        "high": Decimal128::from_str(&market_cap_usd.max(candle_market_cap_usd_high).to_string()).unwrap(),
                        "open": Decimal128::from_str(&candle.market_cap_usd.open.to_string()).unwrap(),
                        "close": Decimal128::from_str(&market_cap_usd.to_string()).unwrap(),
                    },
                    "volumeQuote": volume_quote_decimal.to_string(),
                    "volumeBase": volume_base_decimal.to_string(),
                    "volumeUsd": volume_usd_decimal.to_string(),
                }
            };

            CANDLES_REPOSITORY
                .update_one(
                    doc! {
                        "tokenAddress": base_token.clone(),
                        "resolution": resolution as u32,
                        "timestamp": Bson::Int64(resolution_timestamp as i64)
                    },
                    update_doc,
                    None,
                )
                .await
                .map_err(|err| tracing::error!("Error updating candle: {:#?}", err))
                .unwrap();

            continue;
        }

        // Case 2
        // when have prev candle and update new candle base on prev candle to create
        if let Some(candle) = pre_candle
            .clone()
            .filter(|c| c.timestamp != resolution_timestamp)
        {
            let candle_price_close =
                Decimal::from_str(&candle.price.close.to_string()).unwrap_or(price);
            let candle_price_usd_close =
                Decimal::from_str(&candle.price_usd.close.to_string()).unwrap_or(price_usd);
            let candle_market_cap_close =
                Decimal::from_str(&candle.market_cap.close.to_string()).unwrap_or(market_cap);
            let candle_market_cap_usd_close =
                Decimal::from_str(&candle.market_cap_usd.close.to_string())
                    .unwrap_or(market_cap_usd);

            price_low = price.min(candle_price_close);
            price_high = price_high.max(candle_price_close);
            price_open = candle_price_close;

            price_usd_low = price_usd.min(candle_price_usd_close);
            price_usd_high = price_usd_high.max(candle_price_usd_close);
            price_usd_open = candle_price_usd_close;

            market_cap_low = market_cap.min(candle_market_cap_close);
            market_cap_high = market_cap_high.max(candle_market_cap_close);
            market_cap_open = candle_market_cap_close;

            market_cap_usd_low = market_cap_usd.min(candle_market_cap_usd_close);
            market_cap_usd_high = market_cap_usd_high.max(candle_market_cap_usd_close);
            market_cap_usd_open = candle_market_cap_usd_close;
        }

        // Case 3
        // Not exist candle
        if pre_candle.is_none() {
            let init_virtual_hype_reserves = coin
                .init_virtual_hype_reserves
                .unwrap_or(Decimal128::from_str("0").unwrap());
            let init_virtual_token_reserves = coin
                .init_virtual_token_reserves
                .unwrap_or(Decimal128::from_str("0").unwrap());

            let init_virtual_hype_reserves_without_decimals = Decimal::from_str(&format_ether(
                U256::from_str(&init_virtual_hype_reserves.to_string()).unwrap_or_default(),
            ))
            .unwrap();

            let init_virtual_token_reserves_without_decimals = Decimal::from_str(
                &format_units(
                    U256::from_str(&init_virtual_token_reserves.to_string()).unwrap_or_default(),
                    TOKEN_DECIMALS,
                )
                .unwrap(),
            )
            .unwrap();

            let init_token_price_hype =
                if init_virtual_token_reserves_without_decimals != Decimal::ZERO {
                    init_virtual_hype_reserves_without_decimals
                        / init_virtual_token_reserves_without_decimals
                } else {
                    Decimal::ZERO
                };

            // hype price usd
            let hype_price_usd = price_usd / price;

            // update low and open price by init price
            price_low = init_token_price_hype;
            price_open = init_token_price_hype;

            price_usd_low = init_token_price_hype * hype_price_usd;
            price_usd_open = init_token_price_hype * hype_price_usd;
        }

        // when new candle crate
        // For 2 case:
        // - Not exist candle (Case 3)
        // - Have prev candle and update new candle (Case 2)
        let new_candle = Candles {
            token_address: base_token.clone(),
            timestamp: resolution_timestamp,
            resolution: resolution as u32,
            price: Candle {
                low: Decimal128::from_str(&price_low.to_string()).unwrap(),
                high: Decimal128::from_str(&price_high.to_string()).unwrap(),
                open: Decimal128::from_str(&price_open.to_string()).unwrap(),
                close: Decimal128::from_str(&price.to_string()).unwrap(),
            },
            price_usd: Candle {
                low: Decimal128::from_str(&price_usd_low.to_string()).unwrap(),
                high: Decimal128::from_str(&price_usd_high.to_string()).unwrap(),
                open: Decimal128::from_str(&price_usd_open.to_string()).unwrap(),
                close: Decimal128::from_str(&price_usd.to_string()).unwrap(),
            },
            market_cap: Candle {
                low: Decimal128::from_str(&market_cap_low.to_string()).unwrap(),
                high: Decimal128::from_str(&market_cap_high.to_string()).unwrap(),
                open: Decimal128::from_str(&market_cap_open.to_string()).unwrap(),
                close: Decimal128::from_str(&market_cap.to_string()).unwrap(),
            },
            market_cap_usd: Candle {
                low: Decimal128::from_str(&market_cap_usd_low.to_string()).unwrap(),
                high: Decimal128::from_str(&market_cap_usd_high.to_string()).unwrap(),
                open: Decimal128::from_str(&market_cap_usd_open.to_string()).unwrap(),
                close: Decimal128::from_str(&market_cap_usd.to_string()).unwrap(),
            },
            volume_quote: volume_quote.to_string(),
            volume_base: volume_base.to_string(),
            volume_usd: volume_usd.to_string(),
        };

        match CANDLES_REPOSITORY
            .insert_one_or_ignore(&new_candle, None)
            .await
        {
            Ok(_) => continue,
            Err(err) => {
                return Err(err);
            }
        }
    }

    Ok(())
}

#[allow(clippy::too_many_arguments)]
async fn handle_update_candle_per_swap(
    base_token: String,
    quote_token: String,
    timestamp: u64,
    price: Decimal,
    price_usd: Decimal,
    base_amount_without_decimals: Decimal,
    quote_amount_without_decimals: Decimal,
) -> anyhow::Result<()> {
    const MAX_RETRY_COUNT: u32 = 5;

    let mut retry_count = 0;
    while retry_count <= MAX_RETRY_COUNT {
        match update_candle_per_swap(
            base_token.clone(),
            quote_token.clone(),
            timestamp,
            price,
            price_usd,
            base_amount_without_decimals,
            quote_amount_without_decimals,
        )
        .await
        {
            Ok(_) => break,
            Err(err) => {
                if !err.to_string().to_lowercase().contains("duplicate key") {
                    tracing::error!("Error when updating candle per swap: {:#?}", err);
                    break;
                }

                retry_count += 1;

                if retry_count == MAX_RETRY_COUNT {
                    tracing::error!(
                        "handle_update_candle_per_swap too many retries, token address: {:?}",
                        base_token
                    );
                }

                tokio::time::sleep(tokio::time::Duration::from_millis(15)).await;
            }
        }
    }

    Ok(())
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubTradesData {
    pub checkpoint: Option<u64>,
    pub timestamp: u64,
    pub hash: String,
    pub token_address: String,
    pub token_symbol: String,
    pub index: u64,
    pub maker: String,
    pub trade_type: ETradeType,
    pub base_amount: String,
    pub quote_amount: String,
    pub price: String,
    pub price_usd: String,
    pub volume: String,
    pub volume_usd: String,
    pub virtual_hype_reserves: String,
    pub virtual_token_reserves: String,
    pub real_hype_reserves: String,
    pub real_token_reserves: String,
    pub fee: String,
}
