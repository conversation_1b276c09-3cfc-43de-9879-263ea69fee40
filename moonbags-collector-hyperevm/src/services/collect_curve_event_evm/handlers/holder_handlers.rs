use std::str::FromStr;

use crate::{
    abis::MoonbagsLaunchpad::Trade,
    common::HOLDERS_REPOSITORY,
    db::utils::traits::RepositorySharedMethod,
    services::{
        kafka::{enums::KafkaTopic, kafka_service::KafkaService},
        redis_service::REDIS_EMITTER,
    },
    utils::common::TOKEN_DECIMALS,
};
use ::serde::{Deserialize, Serialize};
use alloy::{
    primitives::{utils::format_units, U256},
    rpc::types::Log,
};
use anyhow::Result;
use bson::{doc, Decimal128};
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument};
use rust_decimal::Decimal;

pub async fn handle_traded_event(event: &Trade, log: &Log) -> Result<()> {
    let is_buy = event.isBuy;
    let token_address = event.token.to_string();
    let user_address = event.user.to_string();
    let timestamp_ms = (event.timestamp * U256::from(1000)).to::<u64>();
    let un_decimals_amount =
        Decimal::from_str(&format_units(event.tokenAmount, TOKEN_DECIMALS).unwrap())?;
    let change_amount = if is_buy {
        un_decimals_amount
    } else {
        -un_decimals_amount
    };

    let tx_digest = log
        .transaction_hash
        .map(|h| h.to_string())
        .unwrap_or_default();

    upsert_holder(
        token_address.clone(),
        user_address.clone(),
        change_amount,
        tx_digest.clone(),
        timestamp_ms,
    )
    .await?;

    let pub_holder = PubHolders {
        token_address: token_address.clone(),
        user_address: user_address.clone(),
        change_amount: change_amount.to_string(),
        last_hash: tx_digest,
        last_synced_at: timestamp_ms,
    };

    if let Err(e) = emit_update_holder_event(&pub_holder, &token_address) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    if let Err(e) =
        KafkaService::publish_messages(KafkaTopic::UpdatedHolder, &vec![pub_holder]).await
    {
        tracing::error!("Failed to publish to kafka: {}", e);
    }

    Ok(())
}

async fn upsert_holder(
    token_address: String,
    user_address: String,
    change_amount: Decimal,
    last_hash: String,
    last_synced_at: u64,
) -> anyhow::Result<()> {
    let filter = doc! {
        "tokenAddress": &token_address,
        "userAddress": &user_address,
    };

    let update = doc! {
        "$inc": {
            "amount": Decimal128::from_str(&change_amount.to_string()).unwrap()
        },
        "$set": {
            "lastHash": &last_hash,
            "lastSyncedAt": bson::to_bson(&last_synced_at)?
        },
        "$setOnInsert": {
            "tokenAddress": &token_address,
            "userAddress": &user_address,
        }
    };

    let options = FindOneAndUpdateOptions::builder()
        .upsert(true)
        .return_document(ReturnDocument::After)
        .build();

    match HOLDERS_REPOSITORY
        .get_collection()
        .find_one_and_update(filter, update, options)
        .await
    {
        Ok(Some(updated_holder)) => {
            tracing::info!(
                "Holder info updated for: holder={}, token={}, amount={}",
                &user_address,
                &token_address,
                updated_holder.amount
            );
        }
        Ok(None) => {
            tracing::warn!(
                "Unexpected None result from find_one_and_update for holder={}, token={}",
                &user_address,
                &token_address
            );
        }
        Err(e) => {
            tracing::error!(
                "Failed to upsert holder info for holder={}, token={}, change_amount={}: {}",
                &user_address,
                &token_address,
                change_amount,
                e
            );
            return Err(e.into());
        }
    }

    Ok(())
}

fn emit_update_holder_event(holder: &PubHolders, token_address: &str) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(holder)?;
    let room = format!("SUBSCRIBE_COIN::{token_address}");
    let event = "UpdatedHolder";

    REDIS_EMITTER
        .get()
        .unwrap()
        .emit_room(&room, event, &pub_data);

    Ok(())
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubHolders {
    pub token_address: String,
    pub user_address: String,
    pub change_amount: String,
    pub last_hash: String,
    pub last_synced_at: u64,
}
