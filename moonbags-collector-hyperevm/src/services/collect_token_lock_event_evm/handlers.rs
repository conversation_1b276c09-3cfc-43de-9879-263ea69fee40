use std::str::FromStr;

use alloy::primitives::utils::format_units;
use anyhow::Result;
use bson::{doc, Decimal128};
use chrono::Utc;
use mongodb::options::FindOneAndUpdateOptions;

use crate::{
    abis::TokenLock::{LockCreated, TokensWithdrawn},
    common::{COINS_REPOSITORY, COIN_LOCK_REPOSITORY},
    db::{
        coin_lock::{CoinLock, TokenInfo},
        utils::traits::RepositorySharedMethod,
    },
    utils::common::TOKEN_DECIMALS,
};

// Event handlers for TokenLock events
pub async fn handle_lock_created_event(event: &LockCreated) -> Result<()> {
    let token_address = event.tokenAddress.to_string();
    let locker = event.locker.to_string();
    let recipient = event.recipient.to_string();
    let start_time_ms = event.startTime.to::<u64>() * 1000;
    let end_time_ms = event.endTime.to::<u64>() * 1000;
    let coin_info_result = COINS_REPOSITORY
        .get_collection()
        .find_one(doc! { "tokenAddress": &token_address }, None)
        .await?;

    let (symbol, logo_uri) = match coin_info_result {
        Some(coin_info) => (
            coin_info.symbol.unwrap_or_default(),
            coin_info.logo_uri.unwrap_or_default(),
        ),
        None => {
            tracing::warn!("Token not found in coins repository: {}", token_address);
            ("".to_string(), "".to_string())
        }
    };

    let coin_lock = CoinLock {
        contract_id: event.lockId.to_string(),
        locker: locker.clone(),
        recipient: recipient.clone(),
        token_info: TokenInfo {
            token_address: token_address.clone(),
            symbol,
            logo_uri,
        },
        amount: Decimal128::from_str(
            &format_units(event.amount, TOKEN_DECIMALS)
                .unwrap()
                .to_string(),
        )
        .unwrap(),
        fee: Decimal128::from_str(
            &format_units(event.amount, TOKEN_DECIMALS)
                .unwrap()
                .to_string(),
        )
        .unwrap(),
        start_time: start_time_ms,
        end_time: end_time_ms,
        closed: false,
        created_at: Utc::now().into(),
        updated_at: Utc::now().into(),
    };

    let filter = doc! {
        "contractId": &event.lockId.to_string(),
    };

    let token_info_bson = bson::to_bson(&coin_lock.token_info)
        .map_err(|e| anyhow::anyhow!("Failed to serialize token info: {}", e))?;

    let update = doc! {
        "$set": {
            "locker": &coin_lock.locker,
            "recipient": &coin_lock.recipient,
            "amount": &coin_lock.amount,
            "tokenInfo": token_info_bson,
            "fee": &coin_lock.fee,
            "startTime": coin_lock.start_time as i64,
            "endTime": coin_lock.end_time as i64,
            "closed": false,
            "updatedAt": Utc::now(),
        }
    };
    let options = FindOneAndUpdateOptions::builder().upsert(true).build();

    match COIN_LOCK_REPOSITORY
        .find_one_and_update(filter, update, options)
        .await
    {
        Ok(_) => {
            tracing::info!(
                "Successfully saved lock created event: contract={}, locker={}, recipient={}, amount={}, start_time={}, end_time={}",
                event.lockId,
                locker,
                recipient,
                event.amount.to_string(),
                start_time_ms,
                end_time_ms,
            );
            Ok(())
        }
        Err(e) => {
            tracing::error!("Failed to save lock created event: {}", e);
            Err(anyhow::anyhow!("Failed to save lock created event: {}", e))
        }
    }
}

pub async fn handle_tokens_withdrawn_event(event: &TokensWithdrawn) -> Result<()> {
    let contract_id = event.lockId.to_string();
    let result = COIN_LOCK_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "contractId": &contract_id,
            },
            doc! { "$set": { "closed": true } },
            None,
        )
        .await;

    match result {
        Ok(Some(_)) => {
            tracing::info!("Lock fully withdrawn: contract={}", contract_id);

            Ok(())
        }
        Ok(None) => {
            tracing::warn!(
                "No matching lock record found for withdrawal: contract={}. Cannot process withdrawal.",
                contract_id
            );
            Ok(())
        }
        Err(e) => {
            tracing::error!("Failed to update lock record: {}", e);
            Err(anyhow::anyhow!("Failed to update lock record: {}", e))
        }
    }
}
