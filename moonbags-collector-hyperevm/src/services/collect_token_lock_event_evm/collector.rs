use alloy::{
    hex,
    primitives::Address,
    providers::Provider,
    rpc::types::{BlockNumberOrTag, Filter},
    sol_types::SolEventInterface,
};
use anyhow::Result;
use bson::{doc, Bson};
use std::time::Duration;
use tokio::time::sleep;
use tracing::{error, info, warn};

use crate::{
    abis::TokenLock::{self, TokenLockEvents},
    common::{
        get_evm_provider_instance, EthereumProvider, EVENT_LOGS_REPOSITORY,
        PROCESSED_BLOCKS_REPOSITORY,
    },
    config::APP_CONFIG,
    db::{
        event_logs::EventLogs, processed_blocks::EServiceName,
        utils::traits::RepositorySharedMethod,
    },
    services::collect_token_lock_event_evm::handlers::{
        handle_lock_created_event, handle_tokens_withdrawn_event,
    },
};

pub struct EvmCollectorConfig {
    pub service_name: EServiceName,
    pub rpc_url: String,
    pub contract_address: String,
    pub start_block: u64,
    pub batch_size: u64,
    pub delayed_blocks: u64,
    pub block_time_ms: u64,
}

impl EvmCollectorConfig {
    pub fn for_token_lock() -> Self {
        Self {
            service_name: EServiceName::CollectTokenLockEventEvm,
            rpc_url: APP_CONFIG.evm_rpc_url.clone(),
            contract_address: APP_CONFIG.token_lock_address.clone(),
            start_block: APP_CONFIG.evm_start_block,
            batch_size: APP_CONFIG.evm_batch_size,
            delayed_blocks: APP_CONFIG.evm_delayed_blocks,
            block_time_ms: APP_CONFIG.evm_block_time_ms,
        }
    }
}

pub async fn collect_token_lock_events() -> Result<()> {
    let config = EvmCollectorConfig::for_token_lock();
    info!(
        "Starting TokenLock event collector with service: {:?}",
        config.service_name
    );

    let provider = get_evm_provider_instance();

    loop {
        if let Err(e) = scan_blocks(provider, &config).await {
            error!("Error in TokenLock event scanning: {:?}", e);
            sleep(Duration::from_millis(config.block_time_ms)).await;
        } else {
            sleep(Duration::from_millis(config.block_time_ms / 4)).await;
        }
    }
}

async fn scan_blocks(provider: &EthereumProvider, config: &EvmCollectorConfig) -> Result<()> {
    let latest_block = provider.get_block_number().await?;

    // Get last processed block
    let last_processed = PROCESSED_BLOCKS_REPOSITORY
        .find_one_by_service_name(&config.service_name)
        .await?
        .map(|block| block.block_number)
        .unwrap_or(config.start_block);

    // Calculate the range to scan
    let delayed_latest = latest_block.saturating_sub(config.delayed_blocks);
    let to_block = std::cmp::min(delayed_latest, last_processed + config.batch_size);

    if to_block <= last_processed {
        info!(
            "No new blocks to process. Latest: {}, Delayed: {}, Last processed: {}",
            latest_block, delayed_latest, last_processed
        );
        return Ok(());
    }

    let from_block = last_processed + 1;

    info!(
        "Scanning {} events from block {} to block {}",
        config.service_name, from_block, to_block
    );

    // Create filter for the contract events
    let contract_address: Address = config.contract_address.parse()?;
    let filter = Filter::new()
        .address(contract_address)
        .from_block(BlockNumberOrTag::Number(from_block))
        .to_block(BlockNumberOrTag::Number(to_block));

    // Get logs
    let logs = provider.get_logs(&filter).await?;

    if logs.is_empty() {
        info!("No events found in blocks {} to {}", from_block, to_block);
    } else {
        info!(
            "Found {} events in blocks {} to {}",
            logs.len(),
            from_block,
            to_block
        );

        // Process each log
        for log in logs {
            if let Err(e) = process_log(&log).await {
                error!("Error processing log: {:?}", e);
                continue;
            }
        }
    }

    PROCESSED_BLOCKS_REPOSITORY
        .upsert_processed_block(&config.service_name, to_block)
        .await?;

    Ok(())
}

async fn process_log(log: &alloy::rpc::types::Log) -> Result<()> {
    let primitive_log = alloy::primitives::Log {
        address: log.address(),
        data: alloy::primitives::LogData::new(log.topics().to_vec(), log.data().data.clone())
            .unwrap(),
    };
    let tx_hash = log
        .transaction_hash
        .ok_or_else(|| anyhow::anyhow!("Transaction hash not found"))?;
    let log_index = log
        .log_index
        .ok_or_else(|| anyhow::anyhow!("Log index not found"))?;

    let event_log = EVENT_LOGS_REPOSITORY
        .find_one(
            doc! {
                "txHash": tx_hash.to_string(),
                "logIndex": Bson::Int64(log_index as i64),
            },
            None,
        )
        .await?;

    if event_log.is_some() {
        info!(
            "Log already processed: tx_hash={}, log_index={}",
            tx_hash, log_index
        );
        return Ok(());
    }

    if let Ok(decoded) = TokenLock::TokenLockEvents::decode_log(&primitive_log) {
        let event_name: String;
        let clone_log = log.clone();

        match decoded.data {
            TokenLockEvents::LockCreated(event) => {
                info!("Processing LockCreated event: {:?}", event);
                event_name = "LockCreated".to_string();
                handle_lock_created_event(&event).await?;
            }
            TokenLockEvents::TokensWithdrawn(event) => {
                info!("Processing TokensWithdrawn event: {:?}", event);
                event_name = "TokensWithdrawn".to_string();
                handle_tokens_withdrawn_event(&event).await?;
            }
            _ => {
                warn!("Unhandled event type: {:?}", decoded.data);
                return Ok(());
            }
        }

        let event_log = EventLogs {
            tx_hash: tx_hash.to_string(),
            topics: log.topics().iter().map(|t| t.to_string()).collect(),
            data: format!("0x{}", hex::encode(&clone_log.inner.data.data)),
            block_number: log.block_number.unwrap_or_default(),
            block_timestamp: log.block_timestamp.unwrap_or_default() * 1000,
            log_index,
            event_name,
        };
        EVENT_LOGS_REPOSITORY.insert_one(event_log, None).await?;
    } else {
        warn!("Failed to decode log: {:?}", log);
    }

    Ok(())
}
