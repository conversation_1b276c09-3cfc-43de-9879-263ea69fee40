use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::CoinLock;

#[derive(Debug, Clone)]
pub struct CoinLockRepository {
    collection: Collection<CoinLock>,
}

impl RepositorySharedMethod for CoinLockRepository {
    type Schema = CoinLock;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::CoinLock.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}
