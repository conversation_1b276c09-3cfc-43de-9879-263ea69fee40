use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::StakingUsers;

#[derive(Debug, Clone)]
pub struct StakingUsersRepository {
    collection: Collection<StakingUsers>,
}

impl RepositorySharedMethod for StakingUsersRepository {
    type Schema = StakingUsers;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::StakingUsers.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}
