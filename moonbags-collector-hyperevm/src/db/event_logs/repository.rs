use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::EventLogs;

#[derive(Debug, Clone)]
pub struct EventLogsRepository {
    collection: Collection<EventLogs>,
}

impl RepositorySharedMethod for EventLogsRepository {
    type Schema = EventLogs;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::EventLogs.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}
