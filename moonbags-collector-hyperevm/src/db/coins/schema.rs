use bson::{DateTime, Decimal128};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Coins {
    pub token_address: String,
    pub pool_address: Option<String>,
    pub name: Option<String>,
    pub symbol: Option<String>,
    pub slug: Option<String>,
    pub description: Option<String>,
    pub logo_uri: Option<String>,
    pub socials: Option<Socials>,
    pub creator_address: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reply_count: Option<String>,
    pub mcap: Option<Decimal128>,
    pub mcap_usd: Option<Decimal128>,
    pub virtual_hype_reserves: Option<Decimal128>,
    pub virtual_token_reserves: Option<Decimal128>,
    pub init_virtual_hype_reserves: Option<Decimal128>,
    pub init_virtual_token_reserves: Option<Decimal128>,
    pub real_hype_reserves: Option<Decimal128>,
    pub real_token_reserves: Option<Decimal128>,
    pub threshold: Option<Decimal128>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub prev_mcap: Option<Decimal128>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub prev_real_hype_reserves: Option<Decimal128>,
    pub bonding_curve: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listed_at: Option<DateTime>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listed_pool_id: Option<String>,
    pub created_at: Option<DateTime>,
    pub reward_token_pool: Option<RewardTokenPool>,
    pub reward_shro_pool: Option<RewardShroPool>,
    pub reward_creator_pool: Option<RewardCreatorPool>,
    pub fee_rates: Option<FeeRates>,
    pub total_rewards: Option<Decimal128>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Socials {
    pub telegram: String,
    pub website: String,
    #[serde(rename = "x")]
    pub x_social: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct RewardTokenPool {
    pub address: Option<String>,
    pub staked_amount: Option<Decimal128>,
    pub reward_claimed: Option<Decimal128>,
    pub total_reward: Option<Decimal128>,
    pub event_uuid: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct RewardShroPool {
    pub address: Option<String>,
    pub reward_claimed: Option<Decimal128>,
    pub event_uuid: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct RewardCreatorPool {
    pub address: Option<String>,
    pub reward_claimed: Option<Decimal128>,
    pub total_reward: Option<Decimal128>,
    pub event_uuid: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct FeeRates {
    pub platform_fee_withdraw_rate: Option<Decimal128>,
    pub creator_fee_withdraw_rate: Option<Decimal128>,
    pub stake_fee_withdraw_rate: Option<Decimal128>,
    pub platform_stake_fee_withdraw_rate: Option<Decimal128>,
}
