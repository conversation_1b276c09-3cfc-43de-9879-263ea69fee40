use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::Candles;

#[derive(Debug, <PERSON>lone)]
pub struct CandlesRepository {
    collection: Collection<Candles>,
}

impl RepositorySharedMethod for CandlesRepository {
    type Schema = Candles;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::Candles.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

#[derive(Debug, <PERSON>lone, Copy)]
pub enum QueryTimeFrame {
    FiveMinutes,
    OneHour,
    SixHours,
    TwentyFourHours,
}
