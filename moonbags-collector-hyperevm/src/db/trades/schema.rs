use bson::Decimal128;
use serde::{Deserialize, Serialize};
use strum_macros::{EnumString, VariantNames};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Trades {
    pub checkpoint: Option<u64>,
    pub hash: String,
    pub token_address: String,
    pub token_symbol: String,
    pub index: u64,
    pub maker: String,
    pub trade_type: ETradeType,
    pub base_amount: String,
    pub quote_amount: String,
    pub price: String,
    pub price_usd: String,
    pub volume: String,
    pub volume_usd: String,
    pub timestamp: u64,
    pub virtual_hype_reserves: Decimal128,
    pub virtual_token_reserves: Decimal128,
    pub real_hype_reserves: Decimal128,
    pub real_token_reserves: Decimal128,
    pub fee: String,
}

#[derive(
    Debug,
    Serialize,
    Deserialize,
    Clone,
    Copy,
    EnumString,
    VariantNames,
    strum_macros::Display,
    PartialEq,
    Eq,
    Hash,
)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum ETradeType {
    Buy,
    Sell,
}
