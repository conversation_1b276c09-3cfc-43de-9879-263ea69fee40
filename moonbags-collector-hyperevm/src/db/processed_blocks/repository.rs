use bson::doc;
use chrono::Utc;
use mongodb::{Collection, Database};

use crate::db::{common::EDatabase, utils::traits::RepositorySharedMethod};

use super::{EServiceName, ProcessedBlocks};

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct ProcessedBlocksRepository {
    collection: Collection<ProcessedBlocks>,
}

impl RepositorySharedMethod for ProcessedBlocksRepository {
    type Schema = ProcessedBlocks;

    fn new(database: &Database) -> Self {
        Self {
            collection: database
                .collection::<Self::Schema>(&EDatabase::ProcessedBlocks.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl ProcessedBlocksRepository {
    pub async fn find_one_by_service_name(
        &self,
        service_name: &EServiceName,
    ) -> anyhow::Result<Option<ProcessedBlocks>> {
        let record = self
            .collection
            .find_one(doc! { "serviceName": bson::to_bson(service_name)? }, None)
            .await?;
        Ok(record)
    }

    pub async fn upsert_processed_block(
        &self,
        service_name: &EServiceName,
        block_number: u64,
    ) -> anyhow::Result<()> {
        match self.find_one_by_service_name(service_name).await? {
            Some(_) => {
                let filter = doc! {
                    "serviceName": bson::to_bson(service_name)?
                };
                let update = doc! {
                    "$set": {
                        "blockNumber": bson::to_bson(&block_number)?,
                        "updatedAt": Utc::now(),
                    }
                };
                self.update_one(filter, update, None).await?;
            }
            None => {
                self.insert_one_or_ignore(
                    &ProcessedBlocks {
                        id: None,
                        service_name: service_name.clone(),
                        block_number,
                        updated_at: Utc::now().into(),
                    },
                    None,
                )
                .await?;
            }
        };
        Ok(())
    }
}
