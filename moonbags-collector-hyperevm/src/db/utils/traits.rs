use std::borrow::<PERSON><PERSON>;

use async_trait::async_trait;
use bson::Document;
use futures::TryStreamExt;
use mongodb::{
    error::{ErrorKind, WriteFailure},
    options::{
        CountOptions, DeleteOptions, FindOneAndUpdateOptions, FindOneOptions, FindOptions,
        InsertManyOptions, InsertOneOptions, UpdateModifications, UpdateOptions,
    },
    results::{DeleteResult, InsertManyResult, InsertOneResult, UpdateResult},
    Collection, Database,
};
use serde::{de::DeserializeOwned, Serialize};

#[async_trait]
pub trait RepositorySharedMethod {
    type Schema: Serialize + DeserializeOwned + Sync + Send + Unpin;

    fn new(database: &Database) -> Self;

    fn get_collection(&self) -> &Collection<Self::Schema>;

    async fn find(
        &self,
        filter: impl Into<Option<Document>> + Send,
        options: impl Into<Option<FindOptions>> + Send,
    ) -> Result<Vec<Self::Schema>, mongodb::error::Error> {
        let cursor = self.get_collection().find(filter, options).await?;
        cursor.try_collect().await
    }

    async fn find_one(
        &self,
        filter: impl Into<Option<Document>> + Send,
        options: impl Into<Option<FindOneOptions>> + Send,
    ) -> Result<Option<Self::Schema>, mongodb::error::Error> {
        self.get_collection().find_one(filter, options).await
    }

    async fn find_one_and_update(
        &self,
        filter: Document,
        update: impl Into<UpdateModifications> + Send,
        options: impl Into<Option<FindOneAndUpdateOptions>> + Send,
    ) -> Result<Option<Self::Schema>, mongodb::error::Error> {
        self.get_collection()
            .find_one_and_update(filter, update, options)
            .await
    }

    async fn insert_one(
        &self,
        data: impl Borrow<Self::Schema> + Send,
        options: impl Into<Option<InsertOneOptions>> + Send,
    ) -> Result<InsertOneResult, mongodb::error::Error> {
        self.get_collection().insert_one(data, options).await
    }

    async fn insert_one_or_ignore(
        &self,
        data: impl Borrow<Self::Schema> + Send,
        options: impl Into<Option<InsertOneOptions>> + Send,
    ) -> anyhow::Result<Option<InsertOneResult>> {
        let result = self.insert_one(data, options).await;

        match result {
            Ok(r) => Ok(Some(r)),
            Err(e) => match e.kind.as_ref() {
                ErrorKind::Write(WriteFailure::WriteError(we)) => match we.code {
                    11000 => Ok(None),
                    _ => Err(e.into()),
                },
                _ => Err(e.into()),
            },
        }
    }

    async fn insert_many(
        &self,
        data: &Vec<Self::Schema>,
        options: impl Into<Option<InsertManyOptions>> + Send,
    ) -> Result<InsertManyResult, mongodb::error::Error> {
        self.get_collection().insert_many(data, options).await
    }

    async fn update_one(
        &self,
        query: Document,
        update: impl Into<UpdateModifications> + Send,
        options: impl Into<Option<UpdateOptions>> + Send,
    ) -> Result<UpdateResult, mongodb::error::Error> {
        self.get_collection()
            .update_one(query, update, options)
            .await
    }

    async fn update_many(
        &self,
        query: Document,
        update: impl Into<UpdateModifications> + Send,
        options: impl Into<Option<UpdateOptions>> + Send,
    ) -> Result<UpdateResult, mongodb::error::Error> {
        self.get_collection()
            .update_many(query, update, options)
            .await
    }

    async fn delete_one(
        &self,
        query: Document,
        options: impl Into<Option<DeleteOptions>> + Send,
    ) -> Result<DeleteResult, mongodb::error::Error> {
        self.get_collection().delete_one(query, options).await
    }

    async fn delete_many(
        &self,
        query: Document,
        options: impl Into<Option<DeleteOptions>> + Send,
    ) -> Result<DeleteResult, mongodb::error::Error> {
        self.get_collection().delete_many(query, options).await
    }

    async fn count_documents(
        &self,
        filter: impl Into<Option<Document>> + Send,
        options: impl Into<Option<CountOptions>> + Send,
    ) -> Result<u64, mongodb::error::Error> {
        self.get_collection().count_documents(filter, options).await
    }
}
