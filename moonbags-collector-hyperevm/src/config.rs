use std::sync::LazyLock;

use clap::Parser;

pub static APP_CONFIG: LazyLock<Config> = LazyLock::new(Config::parse);

#[derive(Debug, Parser)]
pub struct Config {
    #[clap(long, env, default_value = "debug")]
    pub log_level: String,

    #[clap(long, env)]
    pub mongodb_uri: String,

    #[clap(long, env)]
    pub redis_host: String,

    #[clap(long, env)]
    pub redis_port: String,

    #[clap(long, env)]
    pub redis_password: String,

    #[clap(long, env)]
    pub redis_db: String,

    #[clap(long, env, value_delimiter = ',')]
    pub kafka_brokers: Vec<String>,

    #[clap(long, env, default_value = "false")]
    pub kafka_ssl_enabled: bool,

    #[clap(long, env)]
    pub kafka_sasl_username: String,

    #[clap(long, env)]
    pub kafka_sasl_password: String,

    #[clap(long, env)]
    pub shro_token_address: String,

    #[clap(long, env)]
    pub wrapped_eth_address: String,

    // EVM Configuration
    #[clap(long, env)]
    pub evm_rpc_url: String,

    #[clap(long, env, default_value = "1000")]
    pub evm_batch_size: u64,

    #[clap(long, env, default_value = "5")]
    pub evm_delayed_blocks: u64,

    #[clap(long, env, default_value = "12000")]
    pub evm_block_time_ms: u64,

    #[clap(long, env)]
    pub evm_start_block: u64,

    // Contract Addresses
    #[clap(long, env)]
    pub moonbags_launchpad_address: String,

    #[clap(long, env)]
    pub moonbags_stake_address: String,

    #[clap(long, env)]
    pub token_lock_address: String,
}
