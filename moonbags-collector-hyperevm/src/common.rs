use std::sync::LazyLock;

use alloy::providers::{ProviderBuilder, RootProvider};

use crate::{
    config::APP_CONFIG,
    db::{
        candles::CandlesRepository,
        coin_lock::CoinLockRepository,
        coins::CoinsRepository,
        common::{get_mongodb_instance, MONGODB},
        event_logs::EventLogsRepository,
        holders::HoldersRepository,
        processed_blocks::ProcessedBlocksRepository,
        staking_events::StakingEventsRepository,
        staking_users::StakingUsersRepository,
        token_prices::TokenPricesRepository,
        trades::TradesRepository,
        utils::traits::RepositorySharedMethod,
    },
};

pub type EthereumProvider = alloy::providers::fillers::FillProvider<
    alloy::providers::fillers::JoinFill<
        alloy::providers::Identity,
        alloy::providers::fillers::JoinFill<
            alloy::providers::fillers::GasFiller,
            alloy::providers::fillers::JoinFill<
                alloy::providers::fillers::BlobGasFiller,
                alloy::providers::fillers::JoinFill<
                    alloy::providers::fillers::NonceFiller,
                    alloy::providers::fillers::ChainIdFiller,
                >,
            >,
        >,
    >,
    RootProvider,
>;

pub static EVM_PROVIDER: LazyLock<EthereumProvider> = LazyLock::new(|| {
    let rpc_url: reqwest::Url = APP_CONFIG
        .evm_rpc_url
        .parse()
        .expect("Failed to parse EVM RPC URL");
    ProviderBuilder::new().connect_http(rpc_url)
});

pub fn get_evm_provider_instance() -> &'static EthereumProvider {
    &EVM_PROVIDER
}

pub static PROCESSED_BLOCKS_REPOSITORY: LazyLock<ProcessedBlocksRepository> =
    LazyLock::new(|| ProcessedBlocksRepository::new(get_mongodb_instance()));
pub static HOLDERS_REPOSITORY: LazyLock<HoldersRepository> =
    LazyLock::new(|| HoldersRepository::new(MONGODB.get().unwrap()));
pub static COINS_REPOSITORY: LazyLock<CoinsRepository> =
    LazyLock::new(|| CoinsRepository::new(MONGODB.get().unwrap()));
pub static TOKEN_PRICES_REPOSITORY: LazyLock<TokenPricesRepository> =
    LazyLock::new(|| TokenPricesRepository::new(MONGODB.get().unwrap()));
pub static CANDLES_REPOSITORY: LazyLock<CandlesRepository> =
    LazyLock::new(|| CandlesRepository::new(MONGODB.get().unwrap()));
pub static TRADES_REPOSITORY: LazyLock<TradesRepository> =
    LazyLock::new(|| TradesRepository::new(MONGODB.get().unwrap()));
pub static STAKING_EVENTS_REPOSITORY: LazyLock<StakingEventsRepository> =
    LazyLock::new(|| StakingEventsRepository::new(MONGODB.get().unwrap()));
pub static STAKING_USERS_REPOSITORY: LazyLock<StakingUsersRepository> =
    LazyLock::new(|| StakingUsersRepository::new(MONGODB.get().unwrap()));
pub static COIN_LOCK_REPOSITORY: LazyLock<CoinLockRepository> =
    LazyLock::new(|| CoinLockRepository::new(MONGODB.get().unwrap()));
pub static EVENT_LOGS_REPOSITORY: LazyLock<EventLogsRepository> =
    LazyLock::new(|| EventLogsRepository::new(MONGODB.get().unwrap()));
