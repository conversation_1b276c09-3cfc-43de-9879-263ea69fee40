use std::str::FromStr;

use alloy::primitives::{
    utils::{format_ether, format_units},
    U256,
};
use bson::Decimal128;
use nanoid::nanoid;
use rust_decimal::Decimal;
use slugify::slugify;

use crate::config::APP_CONFIG;

pub const TOKEN_DECIMALS: u8 = 6;
pub const FEE_DENOMINATOR: u64 = 10000;
pub const COIN_TOTAL_SUPPLY: u128 = 1_000_000_000_000_000;

#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, strum_macros::Display)]
#[strum(serialize_all = "PascalCase")]
pub enum MigrateDex {
    Hyperswap,
}

pub trait ToF64 {
    fn to_f64(&self) -> f64;
}

impl ToF64 for Decimal128 {
    fn to_f64(&self) -> f64 {
        self.to_string().parse::<f64>().unwrap_or_else(|e| {
            tracing::error!("Failed to parse Decimal128 to f64: {}", e);
            0.0
        })
    }
}

impl ToF64 for Decimal {
    fn to_f64(&self) -> f64 {
        self.to_string().parse::<f64>().unwrap_or_else(|e| {
            tracing::error!("Failed to parse decimal number to f64: {}", e);
            0.0
        })
    }
}

pub fn get_market_cap_hype(
    virtual_token_reserves: U256,
    virtual_hype_reserves: U256,
    init_virtual_token_reserves: U256,
) -> String {
    if virtual_token_reserves == U256::from(0) {
        "0.0".into()
    } else {
        let un_decimal_virtual_hype_reserves = format_ether(virtual_hype_reserves);
        let un_decimal_virtual_token_reserves =
            format_units(virtual_token_reserves, TOKEN_DECIMALS).unwrap();
        let price = Decimal::from_str(&un_decimal_virtual_hype_reserves.to_string()).unwrap()
            / Decimal::from_str(&un_decimal_virtual_token_reserves.to_string()).unwrap();
        let total_supply = Decimal::from_str(
            format_units(init_virtual_token_reserves, TOKEN_DECIMALS)
                .unwrap()
                .as_str(),
        )
        .unwrap();

        (price * total_supply).to_string()
    }
}

pub fn calculate_bonding_curve_progress(real_hype_reserves: Decimal, threshold: Decimal) -> f64 {
    let bonding_progress = if real_hype_reserves >= threshold {
        1.0
    } else {
        (real_hype_reserves / threshold)
            .to_string()
            .parse::<f64>()
            .unwrap()
    };
    // round the number off to 4 decimal places
    (bonding_progress * 1e6).floor() / 1e4
}

pub fn get_real_token_decimals(token_address: &str) -> u8 {
    if token_address == APP_CONFIG.shro_token_address {
        return 9;
    }
    if token_address == APP_CONFIG.wrapped_eth_address {
        return 18;
    }
    TOKEN_DECIMALS
}

pub fn generate_coin_slug(token_name: &str) -> String {
    const ALPHANUMERIC: [char; 52] = [
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
        'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
        'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
    ];

    let normalized_name = slugify!(token_name);
    let nano_id = nanoid!(10, &ALPHANUMERIC);

    format!("{normalized_name}-hype-{nano_id}")
}
