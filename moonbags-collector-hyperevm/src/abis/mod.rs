use alloy::sol;

sol!(
    #[allow(missing_docs)]
    #[sol(rpc)]
    #[derive(Debug)]
    MoonbagsLaunchpad,
    "src/abis/MoonbagsLaunchpad.json"
);

sol!(
    #[allow(missing_docs)]
    #[sol(rpc)]
    #[derive(Debug)]
    MoonbagsStake,
    "src/abis/MoonbagsStake.json"
);

sol!(
    #[allow(missing_docs)]
    #[sol(rpc)]
    #[derive(Debug)]
    TokenLock,
    "src/abis/TokenLock.json"
);

sol! {
    #[allow(clippy::too_many_arguments)]
    #[sol(rpc)]
    contract IERC20Metadata {
        function symbol() external view returns (string memory);
        function name() external view returns (string memory);
        function decimals() external view returns (uint8);
    }
}
