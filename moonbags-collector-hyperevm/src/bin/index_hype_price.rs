use anyhow::Context;
use moonbags_collector_hyperevm::{
    common::TOKEN_PRICES_REPOSITORY, config::APP_CONFIG, db::common::setup_mongodb,
    services::redis_service::RedisService, utils::tracing::init_standard_tracing,
};
use std::time::Duration;
use tokio::time::sleep;

async fn get_hype_price_usd() -> Result<f64, anyhow::Error> {
    tracing::info!("Fetching hyperliquid price in usd");
    let url = "https://api.coingecko.com/api/v3/simple/price?ids=hyperliquid&vs_currencies=usd";
    let response = reqwest::get(reqwest::Url::parse(url)?)
        .await
        .with_context(|| format!("Failed to fetch data from {url}"))?;
    let body: serde_json::Value = response
        .json()
        .await
        .context("Failed to parse JSON response")?;

    let price = body["hyperliquid"]["usd"].as_f64();

    match price {
        Some(price) => Ok(price),
        None => Err(anyhow::anyhow!(format!("Failed to get price: {}", body))),
    }
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    let redis_service = RedisService::new().await;
    let sleep_duration = 30;

    loop {
        match get_hype_price_usd().await {
            Ok(price) => {
                redis_service
                    .set_hype_price_usd(price.to_string(), sleep_duration)
                    .await?;
                TOKEN_PRICES_REPOSITORY
                    .upsert_token_price("hype", price)
                    .await?;
            }
            Err(e) => {
                tracing::error!("{}", e);
            }
        };

        sleep(Duration::from_millis(1000 * sleep_duration)).await
    }
}
