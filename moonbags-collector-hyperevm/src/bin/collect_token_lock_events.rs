use anyhow::Result;
use moonbags_collector_hyperevm::{
    config::APP_CONFIG,
    db::common::setup_mongodb,
    services::{
        collect_token_lock_event_evm::collector::collect_token_lock_events,
        redis_service::{RedisEmitter, RedisService},
    },
    utils::tracing::init_standard_tracing,
};
use tracing::info;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    RedisService::new().await;
    RedisEmitter::new().await;

    info!(
        "Starting TokenLock Event Collector - Configuration:\n  Contract: {}\n  Start Block: {}\n  Batch Size: {}\n  Delayed Blocks: {}\n  Block Time (ms): {}",
        APP_CONFIG.token_lock_address,
        APP_CONFIG.evm_start_block,
        APP_CONFIG.evm_batch_size,
        APP_CONFIG.evm_delayed_blocks,
        APP_CONFIG.evm_block_time_ms
    );

    collect_token_lock_events().await?;

    Ok(())
}
