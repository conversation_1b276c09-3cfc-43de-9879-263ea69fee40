MONGODB_URI=mongodb://mongodb-0.mongodb-headless.moonbags-prod.svc.prod.cluster.local:27017,mongodb-1.mongodb-headless.moonbags-prod.svc.prod.cluster.local:27017,mongodb-2.mongodb-headless.moonbags-prod.svc.prod.cluster.local:27017/moonbags?w=2
REDIS_HOST=moonbags-redis
REDIS_PORT=6379
REDIS_PASSWORD=2F2hd8ONmflm
REDIS_DB=0
KAFKA_BROKERS=localhost:9092
KAFKA_SSL_ENABLED=false
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=

SHRO_TOKEN_ADDRESS=******************************************
MOONBAGS_LAUNCHPAD_ADDRESS=******************************************
MOONBAGS_STAKE_ADDRESS=******************************************
TOKEN_LOCK_ADDRESS=******************************************
WRAPPED_ETH_ADDRESS=******************************************

# EVM Configuration
EVM_RPC_URL=https://rpc.hyperliquid-testnet.xyz/evm
EVM_START_BLOCK=1000000
EVM_BATCH_SIZE=1000
EVM_DELAYED_BLOCKS=5
EVM_BLOCK_TIME_MS=12000

