name: deploy
on:
  workflow_dispatch:
    inputs:
      note:
        default: "Deploy by github action"
      env:
        default: "dev"
        type: choice
        options:
          - prod
          - dev
          - gke

jobs:
  dockerize:
    runs-on: sota-labs-self-hosted
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: login docker registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.HARBOR_HOST }}
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}
      - name: build-push-docker-image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ vars.HARBOR_HOST }}/moonbags/moonbags-collector-hyperevm:${{ github.sha }}
          # cache-to: "type=s3,bucket=actions-cache,region=us-east-1,endpoint_url=${{ vars.minio_endpoint }},access_key_id=${{ secrets.minio_access_key_id }},secret_access_key=${{ secrets.minio_secret_access_key }},use_path_style=true,mode=max"
          # cache-from: "type=s3,bucket=actions-cache,region=us-east-1,endpoint_url=${{ vars.minio_endpoint }},access_key_id=${{ secrets.minio_access_key_id }},secret_access_key=${{ secrets.minio_secret_access_key }},use_path_style=true,mode=max"

  deploy_argocd:
    if: ${{ inputs.env != 'gke' }}
    runs-on: sota-labs-self-hosted
    env:
      ENV: ${{ contains(fromJSON('["main", "master"]'), github.ref_name) && 'prod' || 'dev' }}
    needs:
      - dockerize
    steps:
      - name: deploy k8s by argocd
        uses: sota-labs/shared-github-actions/deploy-k8s-by-argocd@v1
        with:
          image_name: sotalab-harbor.sotatek.works/moonbags/moonbags-collector-hyperevm
          image_tag: ${{ github.sha }}
          env: ${{ env.ENV }}
          app_name: moonbags-collector-hyperevm
          argocd_project: moonbags
          github_org: sota-labs
          gitops_repo: moonbags-k8s
          gitops_path: applications/collector/overlays/${{ env.ENV }}
          argocd_host: ${{ vars.ARGOCD_HOST }}
          argocd_token: ${{ secrets.ARGOCD_TOKEN }}
          ssh_private_key: ${{ secrets.SSH_PRIVATE_TOKEN }}

  deploy_gke:
    if: ${{ inputs.env == 'gke' }}
    runs-on: sota-labs-self-hosted
    env:
      CICD_TOKEN: ${{ secrets.CICD_TOKEN }}
      IMAGE_TAG: ${{ github.sha }}
      HARBOR_HOST: ${{ vars.HARBOR_HOST }}
      ENV: gke
    needs:
      - dockerize
    steps:
      - name: deploy k8s by argocd
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitOps"
          git clone --branch master https://GitOps:$<EMAIL>/sota-labs/moonbags-k8s.git
          cd moonbags-k8s
          sed -i "s|newTag:.*|newTag: ${{ env.IMAGE_TAG}}|g" ./applications/collector/overlays/${{ env.ENV }}/kustomization.yaml
          git add  ./applications/collector/overlays/${{ env.ENV }}/kustomization.yaml
          git commit -m "GitOps deploy image ${{ env.HARBOR_HOST }}/moonbags/moonbags-collector-hyperevm:${{ env.IMAGE_TAG }} to moonbags-collector-hyperevm ${{ env.ENV }}"
          git push
