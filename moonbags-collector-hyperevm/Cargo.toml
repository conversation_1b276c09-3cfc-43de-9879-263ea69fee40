[package]
name = "moonbags-collector-hyperevm"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = { version = "1.0" }
clap = { version = "4.5", features = ["derive", "env"] }
dotenv = { version = "0.15" }
tokio = { version = "1.43", features = ["full"] }
tracing = { version = "0.1" }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
serde = { version = "1.0", features = ["derive"] }
strum_macros = { version = "0.27" }
strum = { version = "0.27" }
serde_json = { version = "1.0" }
mongodb = { version = "2.8", features = ["tracing-unstable"] }
bson = { version = "2", features = ["chrono-0_4"] }
chrono = { version = "0.4", features = ["serde"] }
futures = { version = "0.3" }
async-trait = { version = "0.1" }
reqwest = { version = "0.12.4", features = ["json"] }
# redis as pool
bb8 = "0.9"
bb8-redis = "0.18"
redis = "0.27"
cached = { version = "0.54", features = ["async"] }
socketio-rust-emitter = { git = "https://github.com/epli2/socketio-rust-emitter.git" }
rust_decimal = { version = "1.36", features = ["maths"] }
rust_decimal_macros = "1.36"
rdkafka = { version = "0.36.2", features = ["ssl", "sasl"] }
sha2 = "0.10.9"
nanoid = "0.4"
slugify = "0.1.0"
alloy = { version = "1.0.13", features = ["full"] }
alloy-sol-types = "1.2.1"
