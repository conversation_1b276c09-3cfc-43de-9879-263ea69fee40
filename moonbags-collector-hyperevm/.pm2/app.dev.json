{"apps": [{"name": "dev.moonbags.collector.hyperevm.launchpad_events", "script": "./target/release/collect_moonbags_launchpad_events", "namespace": "moonbags.collector", "instances": 1, "env": {"RUST_BACKTRACE": "1", "MONGODB_URI": "mongodb://root:1@localhost:27017/moonbags_dev?authSource=admin&replicaSet=rs0", "REDIS_HOST": "localhost", "REDIS_PORT": "6379", "REDIS_PASSWORD": "", "REDIS_DB": "0", "KAFKA_BROKERS": "localhost:9092", "KAFKA_SSL_ENABLED": "false", "KAFKA_SASL_USERNAME": "", "KAFKA_SASL_PASSWORD": "", "SHRO_TOKEN_ADDRESS": "******************************************", "MOONBAGS_LAUNCHPAD_ADDRESS": "******************************************", "MOONBAGS_STAKE_ADDRESS": "******************************************", "TOKEN_LOCK_ADDRESS": "******************************************", "WRAPPED_ETH_ADDRESS": "******************************************", "EVM_RPC_URL": "https://rpc.hyperliquid.xyz/evm", "EVM_START_BLOCK": "7402710", "EVM_BATCH_SIZE": "10", "EVM_DELAYED_BLOCKS": "3", "EVM_BLOCK_TIME_MS": "4000"}}, {"name": "dev.moonbags.collector.hyperevm.stake_events", "script": "./target/release/collect_moonbags_stake_events", "namespace": "moonbags.collector", "instances": 1, "env": {"RUST_BACKTRACE": "1", "MONGODB_URI": "mongodb://root:1@localhost:27017/moonbags_dev?authSource=admin&replicaSet=rs0", "REDIS_HOST": "localhost", "REDIS_PORT": "6379", "REDIS_PASSWORD": "", "REDIS_DB": "0", "KAFKA_BROKERS": "localhost:9092", "KAFKA_SSL_ENABLED": "false", "KAFKA_SASL_USERNAME": "", "KAFKA_SASL_PASSWORD": "", "SHRO_TOKEN_ADDRESS": "******************************************", "MOONBAGS_LAUNCHPAD_ADDRESS": "******************************************", "MOONBAGS_STAKE_ADDRESS": "******************************************", "TOKEN_LOCK_ADDRESS": "******************************************", "WRAPPED_ETH_ADDRESS": "******************************************", "EVM_RPC_URL": "https://rpc.hyperliquid.xyz/evm", "EVM_START_BLOCK": "7402710", "EVM_BATCH_SIZE": "10", "EVM_DELAYED_BLOCKS": "3", "EVM_BLOCK_TIME_MS": "4000"}}, {"name": "dev.moonbags.collector.hyperevm.token_lock_events", "script": "./target/release/collect_token_lock_events", "namespace": "moonbags.collector", "instances": 1, "env": {"RUST_BACKTRACE": "1", "MONGODB_URI": "mongodb://root:1@localhost:27017/moonbags_dev?authSource=admin&replicaSet=rs0", "REDIS_HOST": "localhost", "REDIS_PORT": "6379", "REDIS_PASSWORD": "", "REDIS_DB": "0", "KAFKA_BROKERS": "localhost:9092", "KAFKA_SSL_ENABLED": "false", "KAFKA_SASL_USERNAME": "", "KAFKA_SASL_PASSWORD": "", "SHRO_TOKEN_ADDRESS": "******************************************", "MOONBAGS_LAUNCHPAD_ADDRESS": "******************************************", "MOONBAGS_STAKE_ADDRESS": "******************************************", "TOKEN_LOCK_ADDRESS": "******************************************", "WRAPPED_ETH_ADDRESS": "******************************************", "EVM_RPC_URL": "https://rpc.hyperliquid.xyz/evm", "EVM_START_BLOCK": "7402710", "EVM_BATCH_SIZE": "10", "EVM_DELAYED_BLOCKS": "3", "EVM_BLOCK_TIME_MS": "4000"}}, {"name": "dev.moonbags.collector.hyperevm.index_hype_price", "script": "./target/release/index_hype_price", "namespace": "moonbags.collector", "instances": 1, "env": {"RUST_BACKTRACE": "1", "MONGODB_URI": "mongodb://root:1@localhost:27017/moonbags_dev?authSource=admin&replicaSet=rs0", "REDIS_HOST": "localhost", "REDIS_PORT": "6379", "REDIS_PASSWORD": "", "REDIS_DB": "0", "KAFKA_BROKERS": "localhost:9092", "KAFKA_SSL_ENABLED": "false", "KAFKA_SASL_USERNAME": "", "KAFKA_SASL_PASSWORD": "", "SHRO_TOKEN_ADDRESS": "******************************************", "MOONBAGS_LAUNCHPAD_ADDRESS": "******************************************", "MOONBAGS_STAKE_ADDRESS": "******************************************", "TOKEN_LOCK_ADDRESS": "******************************************", "WRAPPED_ETH_ADDRESS": "******************************************", "EVM_RPC_URL": "https://rpc.hyperliquid.xyz/evm", "EVM_START_BLOCK": "7402710", "EVM_BATCH_SIZE": "10", "EVM_DELAYED_BLOCKS": "3", "EVM_BLOCK_TIME_MS": "4000"}}]}