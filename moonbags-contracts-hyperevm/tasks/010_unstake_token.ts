import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { IERC20Metadata, MoonbagsStake } from "../types";
import { getStakeAddress } from "./utils/deployments";

//Example: npx hardhat unstake-token --network hyperevm-testnet --token ****************************************** --amount 1000
task("unstake-token", "Unstake tokens from a staking pool")
    .addParam("token", "The token address to unstake")
    .addParam("amount", "Amount of tokens to unstake (in token units)")
    .addFlag("all", "Unstake all available tokens")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [unstaker] = await ethers.getSigners();

        console.log("MoonbagsStake - Unstake Tokens");
        console.log("=================================");
        console.log("Unstaker account:", unstaker.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;
        const unstakeAmount = taskArgs.amount;
        const unstakeAll = taskArgs.all;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);
        if (unstakeAll) {
            console.log("  Unstake Amount: ALL available tokens");
        } else {
            console.log("  Unstake Amount:", unstakeAmount, "tokens");
        }

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        // Validate amount if not unstaking all
        if (!unstakeAll && (!unstakeAmount || parseFloat(unstakeAmount) <= 0)) {
            throw new Error("Invalid unstake amount provided");
        }

        try {
            const STAKE_ADDRESS = getStakeAddress(network.config);

            const moonbagsStake = (await ethers.getContractAt(
                "MoonbagsStake",
                STAKE_ADDRESS
            )) as MoonbagsStake;

            const stakingToken = (await ethers.getContractAt(
                "IERC20Metadata",
                tokenAddress
            )) as IERC20Metadata;

            // Check if staking pool exists
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            if (!stakingPoolExists) {
                throw new Error(
                    "Staking pool does not exist for this token. Please initialize it first."
                );
            }

            // Check if staking account exists
            const accountExists = await moonbagsStake.stakingAccountExists(
                tokenAddress,
                unstaker.address
            );
            if (!accountExists) {
                throw new Error(
                    "No staking account found for this address. Please stake tokens first."
                );
            }

            // Get token decimals
            const decimals = await stakingToken.decimals();

            console.log("\nPre-unstake Status:");

            // Check current token balance
            const tokenBalance = await stakingToken.balanceOf(unstaker.address);
            console.log(
                "  Current Token Balance:",
                ethers.formatUnits(tokenBalance, decimals),
                "tokens"
            );

            // Get current staking account info
            const accountInfo = await moonbagsStake.getStakingAccountInfo(
                tokenAddress,
                unstaker.address
            );
            console.log(
                "  Staked Balance:",
                ethers.formatUnits(accountInfo.balance, decimals),
                "tokens"
            );
            console.log(
                "  Current Earned Rewards:",
                ethers.formatEther(accountInfo.earned),
                "HYPE"
            );
            console.log(
                "  Unstake Deadline:",
                new Date(Number(accountInfo.unstakeDeadline) * 1000).toISOString()
            );

            // Check if unstake deadline has passed
            const currentTime = Math.floor(Date.now() / 1000);
            if (currentTime < Number(accountInfo.unstakeDeadline)) {
                const timeRemaining = Number(accountInfo.unstakeDeadline) - currentTime;
                const hoursRemaining = Math.ceil(timeRemaining / 3600);
                throw new Error(
                    `Cannot unstake yet. Must wait ${hoursRemaining} more hour(s) until ${new Date(Number(accountInfo.unstakeDeadline) * 1000).toISOString()}`
                );
            }

            // Determine actual unstake amount
            let unstakeAmountWei: bigint;
            if (unstakeAll) {
                unstakeAmountWei = accountInfo.balance;
                console.log(
                    "  Unstaking ALL tokens:",
                    ethers.formatUnits(unstakeAmountWei, decimals),
                    "tokens"
                );
            } else {
                unstakeAmountWei = ethers.parseUnits(unstakeAmount, decimals);
                console.log(
                    "  Unstaking Amount:",
                    ethers.formatUnits(unstakeAmountWei, decimals),
                    "tokens"
                );
            }

            // Validate unstake amount
            if (unstakeAmountWei === 0n) {
                throw new Error("No tokens available to unstake");
            }

            if (unstakeAmountWei > accountInfo.balance) {
                throw new Error(
                    `Insufficient staked balance. Available: ${ethers.formatUnits(accountInfo.balance, decimals)} tokens`
                );
            }

            // Calculate total rewards including pending
            const totalEarned = await moonbagsStake.calculateRewardsEarned(tokenAddress);
            console.log(
                "  Total Rewards (including pending):",
                ethers.formatEther(totalEarned),
                "HYPE"
            );

            // Get pool info
            const poolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
            console.log(
                "  Pool Total Supply:",
                ethers.formatUnits(poolInfo.totalSupply, decimals),
                "tokens"
            );
            console.log("  Pool Total Rewards:", ethers.formatEther(poolInfo.totalRewards), "HYPE");

            console.log("\n⏳ Unstaking tokens...");
            const unstakeTx = await moonbagsStake.unstake(tokenAddress, unstakeAmountWei);
            console.log("⏳ Unstake transaction submitted:", unstakeTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const unstakeReceipt = await unstakeTx.wait();
            console.log("✅ Unstake transaction confirmed in block:", unstakeReceipt?.blockNumber);

            // Check for UnstakeEvent
            const unstakeEvent = unstakeReceipt?.logs.find((log) => {
                try {
                    const parsed = moonbagsStake.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "UnstakeEvent";
                } catch {
                    return false;
                }
            });

            let isAccountDeleted = false;

            if (unstakeEvent) {
                const parsed = moonbagsStake.interface.parseLog({
                    topics: unstakeEvent.topics,
                    data: unstakeEvent.data,
                });
                isAccountDeleted = parsed?.args[3];

                console.log("Tokens Unstaked Successfully!");
                console.log("  Token:", parsed?.args[0]);
                console.log("  Unstaker:", parsed?.args[1]);
                console.log("  Amount:", ethers.formatUnits(parsed?.args[2], decimals), "tokens");
                console.log("  Account Deleted:", isAccountDeleted);
                console.log("  Timestamp:", new Date(Number(parsed?.args[4]) * 1000).toISOString());
            }

            // Display post-unstake status
            console.log("\nPost-unstake Status:");

            const newTokenBalance = await stakingToken.balanceOf(unstaker.address);
            console.log(
                "  New Token Balance:",
                ethers.formatUnits(newTokenBalance, decimals),
                "tokens"
            );
            console.log(
                "  Tokens Received:",
                ethers.formatUnits(newTokenBalance - tokenBalance, decimals),
                "tokens"
            );

            // Check if account still exists
            const newAccountExists = await moonbagsStake.stakingAccountExists(
                tokenAddress,
                unstaker.address
            );

            if (newAccountExists) {
                const newAccountInfo = await moonbagsStake.getStakingAccountInfo(
                    tokenAddress,
                    unstaker.address
                );
                console.log(
                    "  Remaining Staked Balance:",
                    ethers.formatUnits(newAccountInfo.balance, decimals),
                    "tokens"
                );
                console.log(
                    "  Remaining Earned Rewards:",
                    ethers.formatEther(newAccountInfo.earned),
                    "HYPE"
                );
                console.log(
                    "  New Unstake Deadline:",
                    new Date(Number(newAccountInfo.unstakeDeadline) * 1000).toISOString()
                );
            } else {
                console.log("  Staking account was deleted (no remaining balance or rewards)");
            }

            const newPoolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
            console.log(
                "  Pool Total Supply:",
                ethers.formatUnits(newPoolInfo.totalSupply, decimals),
                "tokens"
            );
            console.log(
                "  Pool Total Rewards:",
                ethers.formatEther(newPoolInfo.totalRewards),
                "HYPE"
            );

            console.log("\n✅ Token unstaking completed successfully!");
        } catch (error) {
            console.error("❌ Error unstaking tokens:", error);
        }
    });

export {};
