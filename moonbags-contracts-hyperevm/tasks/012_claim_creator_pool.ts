import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsStake } from "../types";
import { getStakeAddress } from "./utils/deployments";

//Example: npx hardhat claim-creator-pool --network hyperevm-testnet --token ******************************************
task("claim-creator-pool", "Claim HYPE rewards from a creator pool")
    .addParam("token", "The token address of the creator pool")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [claimer] = await ethers.getSigners();

        console.log("MoonbagsStake - Claim Creator Pool Rewards");
        console.log("============================================");
        console.log("Claimer account:", claimer.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        try {
            const STAKE_ADDRESS = getStakeAddress(network.config);

            const moonbagsStake = (await ethers.getContractAt(
                "MoonbagsStake",
                STAKE_ADDRESS
            )) as MoonbagsStake;

            // Check if creator pool exists
            const creatorPoolExists = await moonbagsStake.creatorPoolExists(tokenAddress);
            if (!creatorPoolExists) {
                throw new Error(
                    "Creator pool does not exist for this token. Please initialize it first."
                );
            }

            console.log("\nPre-claim Status:");

            // Check current HYPE balance
            const hyeBalance = await ethers.provider.getBalance(claimer.address);
            console.log("  Current HYPE Balance:", ethers.formatEther(hyeBalance), "HYPE");

            // Get creator pool info
            const creatorInfo = await moonbagsStake.getCreatorPoolInfo(tokenAddress);
            console.log("  Pool Creator:", creatorInfo.creator);
            console.log(
                "  Pool Total Rewards:",
                ethers.formatEther(creatorInfo.totalRewards),
                "HYPE"
            );

            // Verify claimer is the creator
            if (creatorInfo.creator.toLowerCase() !== claimer.address.toLowerCase()) {
                throw new Error(
                    `Only the creator can claim from this pool. Creator: ${creatorInfo.creator}, Your address: ${claimer.address}`
                );
            }

            if (creatorInfo.totalRewards === 0n) {
                console.log("⚠️  No rewards available to claim from creator pool");
                return;
            }

            console.log("\n✅ Verification passed - you are the creator of this pool");

            console.log("\n⏳ Claiming creator pool rewards...");
            const claimTx = await moonbagsStake.claimCreatorPool(tokenAddress);
            console.log("⏳ Claim transaction submitted:", claimTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const claimReceipt = await claimTx.wait();
            console.log("✅ Claim transaction confirmed in block:", claimReceipt?.blockNumber);

            // Check for ClaimCreatorPoolEvent
            const claimEvent = claimReceipt?.logs.find((log) => {
                try {
                    const parsed = moonbagsStake.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "ClaimCreatorPoolEvent";
                } catch {
                    return false;
                }
            });

            let claimedAmount = 0n;

            if (claimEvent) {
                const parsed = moonbagsStake.interface.parseLog({
                    topics: claimEvent.topics,
                    data: claimEvent.data,
                });
                claimedAmount = parsed?.args[2];

                console.log("Creator Pool Rewards Claimed Successfully!");
                console.log("  Token:", parsed?.args[0]);
                console.log("  Creator:", parsed?.args[1]);
                console.log("  Claimed Amount:", ethers.formatEther(claimedAmount), "HYPE");
                console.log("  Timestamp:", new Date(Number(parsed?.args[3]) * 1000).toISOString());
            }

            // Display post-claim status
            console.log("\nPost-claim Status:");

            const newHypeBalance = await ethers.provider.getBalance(claimer.address);
            console.log("  New HYPE Balance:", ethers.formatEther(newHypeBalance), "HYPE");
            console.log("  HYPE Gained:", ethers.formatEther(newHypeBalance - hyeBalance), "HYPE");

            const newCreatorInfo = await moonbagsStake.getCreatorPoolInfo(tokenAddress);
            console.log(
                "  Pool Remaining Rewards:",
                ethers.formatEther(newCreatorInfo.totalRewards),
                "HYPE"
            );

            // Show related pool information
            console.log("\nRelated Pool Information:");

            // Check if staking pool exists and show its status
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            if (stakingPoolExists) {
                const poolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
                console.log(
                    "  Staking Pool Total Supply:",
                    ethers.formatEther(poolInfo.totalSupply),
                    "tokens"
                );
                console.log(
                    "  Staking Pool Total Rewards:",
                    ethers.formatEther(poolInfo.totalRewards),
                    "HYPE"
                );
                console.log("  Staking Pool Reward Index:", poolInfo.rewardIndex.toString());
            } else {
                console.log("  Staking Pool: Not initialized");
            }

            // Show gas cost information
            const gasUsed = claimReceipt?.gasUsed || 0n;
            const gasPrice = claimReceipt?.gasPrice || 0n;
            const gasCost = gasUsed * gasPrice;
            console.log("\nTransaction Costs:");
            console.log("  Gas Used:", gasUsed.toString());
            console.log("  Gas Price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");
            console.log("  Gas Cost:", ethers.formatEther(gasCost), "HYPE");

            console.log("\n✅ Creator pool reward claiming completed successfully!");
        } catch (error) {
            console.error("❌ Error claiming creator pool rewards:", error);
            throw error;
        }
    });

export {};
