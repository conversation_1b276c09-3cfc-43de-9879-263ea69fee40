import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress } from "./utils/deployments";

//Example: npx hardhat sell-tokens --network hyperevm-testnet --token ****************************************** --amount 10000
task("sell-tokens", "Sell tokens on the MoonbagsLaunchpad")
    .addParam("token", "The token address to sell")
    .addParam("amount", "The amount of tokens to sell (e.g., 100)")
    .addOptionalParam("slippage", "Slippage percentage (default: 5)", "5")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [seller] = await ethers.getSigners();

        console.log("🚀 MoonbagsLaunchpad - Sell Tokens");
        console.log("=================================");
        console.log("Seller account:", seller.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;
        const tokenAmount = taskArgs.amount;
        const slippagePercent = parseInt(taskArgs.slippage);

        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        let tokenAmountWei: bigint;
        try {
            if (tokenAmount <= 0) {
                throw new Error("Token amount must be greater than 0");
            }
            tokenAmountWei = ethers.parseUnits(tokenAmount, 6);
        } catch (error) {
            throw new Error(
                `Invalid token amount: ${tokenAmount}. Please provide a valid amount (e.g., 100)`
            );
        }

        if (slippagePercent < 0 || slippagePercent > 100) {
            throw new Error("Slippage percent must be between 0 and 100");
        }

        console.log("\nSale Parameters:");
        console.log("  Token Address:", tokenAddress);
        console.log("  Token Amount:", tokenAmount, "tokens");
        console.log("  Slippage:", slippagePercent + "%");

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            const isValidToken = await launchpad.isToken(tokenAddress);
            if (!isValidToken) {
                throw new Error("Token not found in launchpad. Please check the token address.");
            }

            // Get token contract and decimals
            const tokenContract = await ethers.getContractAt("IERC20Extended", tokenAddress);
            const tokenDecimals = Number(await tokenContract.decimals());
            console.log("  Token decimals:", tokenDecimals);

            const pool = await launchpad.pools(tokenAddress);
            if (pool.isCompleted) {
                throw new Error("Pool completed - token graduated to V3");
            }

            const token = await ethers.getContractAt("IERC20Extended", tokenAddress);
            const balance = await token.balanceOf(seller.address);
            console.log("\nYour token balance:", ethers.formatUnits(balance, tokenDecimals));

            if (balance < tokenAmountWei) {
                throw new Error("Insufficient token balance");
            }

            console.log("\nEstimating sale...");
            const estimation = await launchpad.estimateSellTokens(tokenAddress, tokenAmountWei);
            console.log("  Estimated ETH to receive:", ethers.formatEther(estimation));

            const minEthOut = (estimation * BigInt(100 - slippagePercent)) / BigInt(100);
            console.log("  Minimum ETH (with slippage):", ethers.formatEther(minEthOut));

            const allowance = await token.allowance(seller.address, LAUNCHPAD_ADDRESS);
            if (allowance < tokenAmountWei) {
                console.log("\nApproving token spending...");
                const approveTx = await token.approve(LAUNCHPAD_ADDRESS, tokenAmountWei);
                await approveTx.wait();
                console.log("✅ Token spending approved");
            }

            console.log("\nExecuting sell order...");
            const tx = await launchpad.sellExactIn(tokenAddress, tokenAmountWei, minEthOut);

            console.log("⏳ Transaction submitted:", tx.hash);
            console.log("⏳ Waiting for confirmation...");

            const receipt = await tx.wait();
            console.log("✅ Transaction confirmed in block:", receipt?.blockNumber);

            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "Trade" && parsed?.args[2] === false; // isBuy = false
                } catch {
                    return false;
                }
            });

            if (tradeEvent) {
                const parsed = launchpad.interface.parseLog({
                    topics: tradeEvent.topics,
                    data: tradeEvent.data,
                });
                console.log("\nSale Successful!");
                console.log(
                    "  Tokens Sold:",
                    ethers.formatUnits(parsed?.args[4] || 0, tokenDecimals)
                ); // tokenAmount
                console.log("  ETH Received:", ethers.formatEther(parsed?.args[3] || 0)); // hypeAmount
                console.log("  Fee Paid:", ethers.formatEther(parsed?.args[9] || 0)); // fee
                console.log("  Seller:", parsed?.args[1]); // user
            }

            const newTokenBalance = await token.balanceOf(seller.address);
            const ethBalance = await seller.provider.getBalance(seller.address);
            console.log("\nUpdated Balances:");
            console.log("  Token balance:", ethers.formatUnits(newTokenBalance, tokenDecimals));
            console.log("  ETH balance:", ethers.formatEther(ethBalance));

            console.log("\n✅ Sell operation completed successfully!");
        } catch (error) {
            console.error("❌ Error selling tokens:", error);
        }
    });

export {};
