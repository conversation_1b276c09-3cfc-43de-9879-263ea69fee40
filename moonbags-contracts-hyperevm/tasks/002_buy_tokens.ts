import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress } from "./utils/deployments";

//Example: npx hardhat buy-tokens --network hyperevm-testnet --token ****************************************** --amount 0.02
task("buy-tokens", "Buy tokens from the MoonbagsLaunchpad")
    .addParam("token", "The token address to buy")
    .addParam("amount", "The amount of ETH to spend (e.g., 0.01)")
    .addOptionalParam("slippage", "Slippage percentage (default: 1)", "1")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [buyer] = await ethers.getSigners();

        console.log("MoonbagsLaunchpad - Buy Tokens");
        console.log("================================");
        console.log("Buyer account:", buyer.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;
        const ethAmount = taskArgs.amount;
        const slippagePercent = parseInt(taskArgs.slippage);

        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        let ethAmountWei: bigint;
        try {
            ethAmountWei = ethers.parseEther(ethAmount);
            if (ethAmountWei <= 0) {
                throw new Error("ETH amount must be greater than 0");
            }
        } catch (error) {
            throw new Error(
                `Invalid ETH amount: ${ethAmount}. Please provide a valid amount (e.g., 0.01)`
            );
        }

        if (slippagePercent < 0 || slippagePercent > 100) {
            throw new Error("Slippage percent must be between 0 and 100");
        }

        console.log("\nPurchase Parameters:");
        console.log("  Token Address:", tokenAddress);
        console.log("  ETH Amount:", ethAmount, "ETH");
        console.log("  Slippage:", slippagePercent + "%");

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            // Validate token exists
            const isValidToken = await launchpad.isToken(tokenAddress);
            if (!isValidToken) {
                throw new Error("Token not found in launchpad. Please check the token address.");
            }

            // Get token contract and decimals
            const tokenContract = await ethers.getContractAt("IERC20Extended", tokenAddress);
            const tokenDecimals = Number(await tokenContract.decimals());
            console.log("  Token decimals:", tokenDecimals);

            // Check if pool is still active
            const pool = await launchpad.pools(tokenAddress);
            if (pool.isCompleted) {
                throw new Error("Pool completed - token graduated to V3");
            }

            // First, estimate how many tokens we'll get
            console.log("\n🔍 Estimating purchase...");
            const estimationReceive = await launchpad.estimateBuyExactInTokens(
                tokenAddress,
                ethAmountWei
            );
            console.log(
                "  Estimated tokens to receive:",
                ethers.formatUnits(estimationReceive, tokenDecimals)
            );
            let estimationCost = await launchpad.estimateBuyExactInCost(tokenAddress, ethAmountWei);
            console.log("  Estimated ETH cost:", ethers.formatEther(estimationCost));

            // Calculate minimum tokens with slippage
            const minTokensOut = (estimationReceive * BigInt(100 - slippagePercent)) / BigInt(100);
            console.log(
                "  Minimum tokens (with slippage):",
                ethers.formatUnits(minTokensOut, tokenDecimals)
            );

            // Check if we have enough ETH
            const balance = await buyer.provider.getBalance(buyer.address);
            console.log("  Your ETH balance:", ethers.formatEther(balance));

            if (balance < estimationCost) {
                throw new Error("Insufficient ETH balance");
            }

            console.log("\nExecuting buy order...");
            estimationCost = estimationCost < ethAmountWei ? estimationCost : ethAmountWei;

            const tx = await launchpad.buyExactIn(tokenAddress, estimationCost, minTokensOut, {
                value: (estimationCost * BigInt(105)) / BigInt(100),
            });

            console.log("⏳ Transaction submitted:", tx.hash);
            console.log("⏳ Waiting for confirmation...");

            const receipt = await tx.wait();
            console.log("✅ Transaction confirmed in block:", receipt?.blockNumber);

            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "Trade" && parsed?.args[2] === true; // isBuy = true
                } catch {
                    return false;
                }
            });

            const v3PoolCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "V3PoolCreated";
                } catch {
                    return false;
                }
            });

            if (v3PoolCreatedEvent) {
                const parsed = launchpad.interface.parseLog({
                    topics: v3PoolCreatedEvent.topics,
                    data: v3PoolCreatedEvent.data,
                });
                console.log("\nV3 Pool Created!");
                console.log("Token Address:", parsed?.args[0]);
                console.log("Pool Address:", parsed?.args[1]);
                console.log("Timestamp:", parsed?.args[2]);
            }

            // Check if a V3 position was created (when pool completes)
            if (v3PoolCreatedEvent) {
                const positionId = await launchpad.tokenToPositionId(tokenAddress);
                if (positionId > 0) {
                    console.log("\n🎉 V3 Position Created!");
                    console.log("Position NFT ID:", positionId.toString());
                }
            }

            if (tradeEvent) {
                const parsed = launchpad.interface.parseLog({
                    topics: tradeEvent.topics,
                    data: tradeEvent.data,
                });
                console.log("\nPurchase Successful!");
                console.log("  ETH Spent:", ethers.formatEther(parsed?.args[3] || 0)); // hypeAmount
                console.log(
                    "  Tokens Received:",
                    ethers.formatUnits(parsed?.args[4] || 0, tokenDecimals)
                ); // tokenAmount
                console.log("  Fee Paid:", ethers.formatEther(parsed?.args[9] || 0)); // fee
                console.log("  Buyer:", parsed?.args[1]); // user
            }

            const token = await ethers.getContractAt("IERC20Extended", tokenAddress);
            const tokenBalance = await token.balanceOf(buyer.address);
            console.log(
                "\nYour updated token balance:",
                ethers.formatUnits(tokenBalance, tokenDecimals)
            );

            console.log("\n✅ Buy operation completed successfully!");
        } catch (error) {
            console.error("❌ Error buying tokens:", error);
        }
    });

export {};
