import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress, saveTokenToDeployments } from "./utils/deployments";

//Example: npx hardhat create-and-first-buy --name "TestTokenLock99" --symbol "TTL99" --amount 0.01 --amount-out 1000 --network hyperevm-testnet
task("create-and-first-buy", "Create a pool and immediately buy and lock tokens")
    .addParam("name", "The token name")
    .addParam("symbol", "The token symbol")
    .addParam("amount", "The amount of ETH to spend (e.g., 0.01)")
    .addParam("amountOut", "Specific amount of tokens to buy")
    .addOptionalParam("lockDuration", "Duration to lock tokens in hours (minimum 1)", "1")
    .addOptionalParam("description", "Token description", "A token created via MoonbagsLaunchpad")
    .addOptionalParam("metadata", "Token metadata URI", "https://example.com/metadata.json")
    .addOptionalParam("twitter", "Twitter handle", "")
    .addOptionalParam("telegram", "Telegram link", "")
    .addOptionalParam("website", "Website URL", "")
    .addOptionalParam("threshold", "Custom threshold (0 for default)", "0")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [creator] = await ethers.getSigners();

        console.log("MoonbagsLaunchpad - Create Token and First Buy");
        console.log("===============================================");
        console.log("Creator account:", creator.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenName = taskArgs.name;
        const tokenSymbol = taskArgs.symbol;
        const ethAmount = taskArgs.amount;
        const lockDurationHours = parseInt(taskArgs.lockDuration);
        const description = taskArgs.description;
        const tokenMetadata = taskArgs.metadata;
        const twitter = taskArgs.twitter;
        const telegram = taskArgs.telegram;
        const website = taskArgs.website;
        const customThreshold = taskArgs.threshold;
        const amountOut = taskArgs.amountOut;

        if (lockDurationHours < 1) {
            throw new Error("Lock duration must be at least 1 hour");
        }

        let ethAmountWei: bigint;
        try {
            ethAmountWei = ethers.parseEther(ethAmount);
            if (ethAmountWei <= 0) {
                throw new Error("ETH amount must be greater than 0");
            }
        } catch (error) {
            throw new Error(
                `Invalid ETH amount: ${ethAmount}. Please provide a valid amount (e.g., 0.01)`
            );
        }

        const lockDurationSeconds = lockDurationHours * 3600;
        let amountOutWei: bigint = 0n;

        if (amountOut !== "0") {
            try {
                amountOutWei = ethers.parseUnits(amountOut, 6);
                if (amountOutWei <= 0) {
                    throw new Error("Token amount out must be greater than 0");
                }
            } catch (error) {
                throw new Error(
                    `Invalid token amount out: ${amountOut}. Please provide a valid amount`
                );
            }
        }

        console.log("\nToken Parameters:");
        console.log("  Name:", tokenName);
        console.log("  Symbol:", tokenSymbol);
        console.log("  Description:", description);
        console.log("  Metadata URI:", tokenMetadata);
        console.log("  Custom Threshold:", customThreshold);

        console.log("\nPurchase & Lock Parameters:");
        console.log("  ETH Amount:", ethAmount, "ETH");
        console.log(
            "  Amount Out:",
            amountOut === "0" ? "Maximum possible" : amountOut + " tokens"
        );
        console.log("  Lock Duration:", lockDurationHours, "hours");
        console.log("  Lock Duration (seconds):", lockDurationSeconds);

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            const config = await launchpad.config();
            console.log(`\nConfiguration: ${config}`);

            // Check creator's balance
            const creatorBalance = await ethers.provider.getBalance(creator.address);
            console.log("\nCreator Balance:", ethers.formatEther(creatorBalance), "ETH");

            // Get the required pool creation fee
            const POOL_CREATION_FEE = await launchpad.POOL_CREATION_FEE();
            console.log("Pool Creation Fee:", ethers.formatEther(POOL_CREATION_FEE), "HYPE");

            if (creatorBalance < ethAmountWei + POOL_CREATION_FEE) {
                throw new Error("Insufficient ETH balance for the transaction");
            }

            console.log("\n⏳ Creating token and making first buy with lock...");
            const tx = await launchpad.createAndLockFirstBuy(
                tokenName,
                tokenSymbol,
                tokenMetadata,
                description,
                twitter,
                telegram,
                website,
                customThreshold,
                amountOutWei,
                lockDurationSeconds,
                {
                    value: ethAmountWei + POOL_CREATION_FEE,
                }
            );

            console.log("⏳ Transaction submitted:", tx.hash);
            console.log("⏳ Waiting for confirmation...");

            const receipt = await tx.wait();
            console.log("✅ Transaction confirmed in block:", receipt?.blockNumber);

            // Parse events from the transaction
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            // Look for LockCreated event from TokenLock contract
            const lockCreatedEvent = receipt?.logs.find((log) => {
                try {
                    // We need to check if this is a LockCreated event
                    // Since we don't have the TokenLock interface here, we'll check by the event signature
                    return (
                        log.topics[0] ===
                        ethers.id(
                            "LockCreated(uint256,address,address,string,uint256,uint256,uint256,uint256)"
                        )
                    );
                } catch {
                    return false;
                }
            });

            if (tokenCreatedEvent) {
                const parsed = launchpad.interface.parseLog({
                    topics: tokenCreatedEvent.topics,
                    data: tokenCreatedEvent.data,
                });
                const tokenAddress = parsed?.args[0];
                const timestamp = parsed?.args[parsed?.args.length - 1];

                // Get token decimals
                let tokenDecimals = 6; // default fallback
                if (tokenAddress) {
                    try {
                        const tokenContract = await ethers.getContractAt(
                            "IERC20Extended",
                            tokenAddress
                        );
                        tokenDecimals = Number(await tokenContract.decimals());
                        console.log("  Token decimals:", tokenDecimals);
                    } catch (error) {
                        console.log("  Using default decimals (6) - could not fetch from contract");
                    }
                }

                console.log("\n🎉 Token Created Successfully!");
                console.log("  Token Address:", tokenAddress);
                console.log("  Creator:", creator.address);
                console.log("  Name:", tokenName);
                console.log("  Symbol:", tokenSymbol);
                console.log(
                    "  Creation Timestamp:",
                    new Date(Number(timestamp) * 1000).toISOString()
                );

                if (tradeEvent) {
                    const tradeParsed = launchpad.interface.parseLog({
                        topics: tradeEvent.topics,
                        data: tradeEvent.data,
                    });
                    const hypeAmount = tradeParsed?.args[3]; // hypeAmount
                    const tokenAmount = tradeParsed?.args[4]; // tokenAmount
                    const isBuy = tradeParsed?.args[2]; // isBuy

                    console.log("\nFirst Purchase & Lock Completed!");
                    console.log("  ETH Spent:", ethers.formatEther(hypeAmount || 0), "ETH");
                    console.log(
                        "  Tokens Received:",
                        ethers.formatUnits(tokenAmount || 0, tokenDecimals)
                    ); // Using dynamic decimals
                    console.log("  Lock Duration:", lockDurationHours, "hours");
                    console.log("  Is Buy:", isBuy);
                }

                if (lockCreatedEvent) {
                    console.log("Tokens successfully locked!");
                }

                if (tokenAddress) {
                    await saveTokenToDeployments(
                        tokenAddress,
                        tokenName,
                        tokenSymbol,
                        creator.address,
                        LAUNCHPAD_ADDRESS,
                        new Date(Number(timestamp) * 1000),
                        { name: network.name, chainId: network.config.chainId },
                        {
                            ethSpent: ethAmount,
                            lockDurationHours: lockDurationHours,
                            hasInitialPurchase: true,
                        }
                    );
                }

                console.log("\n✅ Token creation and first buy with lock completed successfully!");
            } else {
                console.log("⚠️  Could not find TokenCreated event in transaction logs");
            }
        } catch (error) {
            console.error("❌ Error creating token and making first buy:", error);
        }
    });

export {};
