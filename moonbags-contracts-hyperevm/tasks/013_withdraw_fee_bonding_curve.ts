import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress } from "./utils/deployments";

//Example: npx hardhat withdraw-fee-bonding-curve --network hyperevm-testnet --token ******************************************
task(
    "withdraw-fee-bonding-curve",
    "Withdraw and distribute accumulated bonding curve fees for a token"
)
    .addParam("token", "The token address to distribute bonding curve fees for")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [distributor] = await ethers.getSigners();

        console.log("MoonbagsLaunchpad - Withdraw Bonding Curve Fees");
        console.log("=================================================");
        console.log("Distributor account:", distributor.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            // Check if token exists
            const isToken = await launchpad.isToken(tokenAddress);
            if (!isToken) {
                throw new Error("Token does not exist in the launchpad. Please create it first.");
            }

            console.log("\nPre-distribution Status:");

            // Get pool information
            const poolInfo = await launchpad.pools(tokenAddress);
            console.log("  Pool Completed:", poolInfo.isCompleted);
            console.log("  Accumulated Fees:", ethers.formatEther(poolInfo.feeRecipient), "HYPE");
            console.log(
                "  Launchpad ETH Balance:",
                ethers.formatEther(await ethers.provider.getBalance(LAUNCHPAD_ADDRESS)),
                "HYPE"
            );
            console.log(
                "  Fee Distribution Unlock Time:",
                new Date(Number(poolInfo.feeDistributionUnlockTime) * 1000).toISOString()
            );

            // Check if there are fees to distribute
            if (poolInfo.feeRecipient === 0n) {
                console.log("⚠️  No fees available to distribute for this token");
                return;
            }

            // Check if fee distribution is unlocked
            const currentTime = Math.floor(Date.now() / 1000);
            if (currentTime < Number(poolInfo.feeDistributionUnlockTime)) {
                const timeRemaining = Number(poolInfo.feeDistributionUnlockTime) - currentTime;
                const minutesRemaining = Math.ceil(timeRemaining / 60);
                throw new Error(
                    `Fee distribution is locked. Must wait ${minutesRemaining} more minute(s) until ${new Date(Number(poolInfo.feeDistributionUnlockTime) * 1000).toISOString()}`
                );
            }

            // Get fee distribution percentages
            console.log("\nFee Distribution Breakdown:");
            console.log(
                "  Platform Fee Share:",
                (Number(poolInfo.platformFeeWithdraw) / 100).toFixed(2) + "%"
            );
            console.log(
                "  Creator Fee Share:",
                (Number(poolInfo.creatorFeeWithdraw) / 100).toFixed(2) + "%"
            );
            console.log(
                "  Stake Fee Share:",
                (Number(poolInfo.stakeFeeWithdraw) / 100).toFixed(2) + "%"
            );
            console.log(
                "  Platform Stake Fee Share:",
                (Number(poolInfo.platformStakeFeeWithdraw) / 100).toFixed(2) + "%"
            );

            // Calculate distribution amounts
            const totalFees = poolInfo.feeRecipient;
            const FEE_DENOMINATOR = 10000n;

            const platformShare = (totalFees * poolInfo.platformFeeWithdraw) / FEE_DENOMINATOR;
            const creatorShare = (totalFees * poolInfo.creatorFeeWithdraw) / FEE_DENOMINATOR;
            const stakeShare = (totalFees * poolInfo.stakeFeeWithdraw) / FEE_DENOMINATOR;
            const platformStakeShare =
                (totalFees * poolInfo.platformStakeFeeWithdraw) / FEE_DENOMINATOR;

            console.log("\nDistribution Amounts:");
            console.log("  Platform Share:", ethers.formatEther(platformShare), "HYPE");
            console.log("  Creator Share:", ethers.formatEther(creatorShare), "HYPE");
            console.log("  Stake Share:", ethers.formatEther(stakeShare), "HYPE");
            console.log("  Platform Stake Share:", ethers.formatEther(platformStakeShare), "HYPE");
            console.log(
                "  Total Distribution:",
                ethers.formatEther(platformShare + creatorShare + stakeShare + platformStakeShare),
                "HYPE"
            );

            // Get config for platform token address
            const config = await launchpad.config();
            console.log("  Platform Token Address:", config.platformTokenAddress);

            console.log("\n⏳ Distributing bonding curve fees...");
            const distributeTx = await launchpad.distributeBondingCurveFees(tokenAddress);
            console.log("⏳ Distribution transaction submitted:", distributeTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const distributeReceipt = await distributeTx.wait();
            console.log(
                "✅ Distribution transaction confirmed in block:",
                distributeReceipt?.blockNumber
            );

            // Display post-distribution status
            console.log("\nPost-distribution Status:");
            const newPoolInfo = await launchpad.pools(tokenAddress);
            console.log("  Remaining Fees:", ethers.formatEther(newPoolInfo.feeRecipient), "HYPE");
            console.log(
                "  New Fee Distribution Unlock Time:",
                new Date(Number(newPoolInfo.feeDistributionUnlockTime) * 1000).toISOString()
            );

            // Show gas cost information
            const gasUsed = distributeReceipt?.gasUsed || 0n;
            const gasPrice = distributeReceipt?.gasPrice || 0n;
            const gasCost = gasUsed * gasPrice;
            console.log("\nTransaction Costs:");
            console.log("  Gas Used:", gasUsed.toString());
            console.log("  Gas Price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");
            console.log("  Gas Cost:", ethers.formatEther(gasCost), "HYPE");

            console.log("\n✅ Bonding curve fee distribution completed successfully!");
        } catch (error) {
            console.error("❌ Error distributing bonding curve fees:", error);
            throw error;
        }
    });

export {};
