import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { IERC20Metadata, MoonbagsStake } from "../types";
import { getStakeAddress } from "./utils/deployments";

//Example: npx hardhat stake-token --network hyperevm-testnet --token ****************************************** --amount 1000
task("stake-token", "Stake tokens in a staking pool")
    .addParam("token", "The token address to stake")
    .addParam("amount", "Amount of tokens to stake (in token units)")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [staker] = await ethers.getSigners();

        console.log("MoonbagsStake - Stake Tokens");
        console.log("==============================");
        console.log("Staker account:", staker.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;
        const stakeAmount = taskArgs.amount;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);
        console.log("  Stake Amount:", stakeAmount, "tokens");

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        // Validate amount
        if (!stakeAmount || parseFloat(stakeAmount) <= 0) {
            throw new Error("Invalid stake amount provided");
        }

        try {
            const STAKE_ADDRESS = getStakeAddress(network.config);

            const moonbagsStake = (await ethers.getContractAt(
                "MoonbagsStake",
                STAKE_ADDRESS
            )) as MoonbagsStake;

            const stakingToken = (await ethers.getContractAt(
                "IERC20Metadata",
                tokenAddress
            )) as IERC20Metadata;

            // Check if staking pool exists
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            if (!stakingPoolExists) {
                throw new Error(
                    "Staking pool does not exist for this token. Please initialize it first."
                );
            }

            // Get token decimals and convert amount
            const decimals = await stakingToken.decimals();
            const stakeAmountWei = ethers.parseUnits(stakeAmount, decimals);

            console.log("\nPre-stake Status:");

            // Check token balance
            const tokenBalance = await stakingToken.balanceOf(staker.address);
            console.log("  Token Balance:", ethers.formatUnits(tokenBalance, decimals), "tokens");

            if (tokenBalance < stakeAmountWei) {
                throw new Error("Insufficient token balance for staking");
            }

            // Check allowance
            const allowance = await stakingToken.allowance(staker.address, STAKE_ADDRESS);
            console.log("  Current Allowance:", ethers.formatUnits(allowance, decimals), "tokens");

            // Approve if needed
            if (allowance < stakeAmountWei) {
                console.log("\n⏳ Approving tokens for staking...");
                const approveTx = await stakingToken.approve(STAKE_ADDRESS, stakeAmountWei);
                console.log("⏳ Approval transaction submitted:", approveTx.hash);
                console.log("⏳ Waiting for approval confirmation...");

                // Wait for more confirmations to ensure the transaction is fully processed
                const approveReceipt = await approveTx.wait(2); // Wait for 2 confirmations
                console.log("✅ Approval confirmed in block:", approveReceipt?.blockNumber);

                // Add a small delay to ensure the transaction is fully processed
                await new Promise((resolve) => setTimeout(resolve, 1000));
            }

            // Get current staking account info (if exists)
            const accountExists = await moonbagsStake.stakingAccountExists(
                tokenAddress,
                staker.address
            );
            let currentBalance = 0n;
            let currentEarned = 0n;

            if (accountExists) {
                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    tokenAddress,
                    staker.address
                );
                currentBalance = accountInfo.balance;
                currentEarned = accountInfo.earned;
                console.log(
                    "  Current Staked Balance:",
                    ethers.formatUnits(currentBalance, decimals),
                    "tokens"
                );
                console.log("  Current Earned Rewards:", ethers.formatEther(currentEarned), "HYPE");
            } else {
                console.log("  Current Staked Balance: 0 tokens (new staker)");
                console.log("  Current Earned Rewards: 0 HYPE");
            }

            // Get pool info
            const poolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
            console.log(
                "  Pool Total Supply:",
                ethers.formatUnits(poolInfo.totalSupply, decimals),
                "tokens"
            );
            console.log("  Pool Total Rewards:", ethers.formatEther(poolInfo.totalRewards), "HYPE");

            console.log("\n⏳ Staking tokens...");
            const stakeTx = await moonbagsStake.stake(tokenAddress, stakeAmountWei);
            console.log("⏳ Stake transaction submitted:", stakeTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const stakeReceipt = await stakeTx.wait();
            console.log("✅ Stake transaction confirmed in block:", stakeReceipt?.blockNumber);

            // Check for StakeEvent
            const stakeEvent = stakeReceipt?.logs.find((log) => {
                try {
                    const parsed = moonbagsStake.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "StakeEvent";
                } catch {
                    return false;
                }
            });

            if (stakeEvent) {
                const parsed = moonbagsStake.interface.parseLog({
                    topics: stakeEvent.topics,
                    data: stakeEvent.data,
                });
                console.log("Tokens Staked Successfully!");
                console.log("  Token:", parsed?.args[0]);
                console.log("  Staker:", parsed?.args[1]);
                console.log("  Amount:", ethers.formatUnits(parsed?.args[2], decimals), "tokens");
                console.log("  Timestamp:", new Date(Number(parsed?.args[3]) * 1000).toISOString());
            }

            // Display post-stake status
            console.log("\nPost-stake Status:");
            const newAccountInfo = await moonbagsStake.getStakingAccountInfo(
                tokenAddress,
                staker.address
            );
            const newPoolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);

            console.log(
                "  New Staked Balance:",
                ethers.formatUnits(newAccountInfo.balance, decimals),
                "tokens"
            );
            console.log("  New Earned Rewards:", ethers.formatEther(newAccountInfo.earned), "HYPE");
            console.log(
                "  Unstake Deadline:",
                new Date(Number(newAccountInfo.unstakeDeadline) * 1000).toISOString()
            );
            console.log(
                "  Pool Total Supply:",
                ethers.formatUnits(newPoolInfo.totalSupply, decimals),
                "tokens"
            );
            console.log(
                "  Pool Total Rewards:",
                ethers.formatEther(newPoolInfo.totalRewards),
                "HYPE"
            );

            const newTokenBalance = await stakingToken.balanceOf(staker.address);
            console.log(
                "  Remaining Token Balance:",
                ethers.formatUnits(newTokenBalance, decimals),
                "tokens"
            );

            console.log("\n✅ Token staking completed successfully!");
        } catch (error) {
            console.error("❌ Error staking tokens:", error);
        }
    });

export {};
