{"extends": "solhint:recommended", "rules": {"code-complexity": ["error", 8], "compiler-version": ["error", ">=0.8.23"], "contract-name-camelcase": "off", "const-name-snakecase": "off", "constructor-syntax": "error", "func-name-mixedcase": "off", "func-visibility": ["error", {"ignoreConstructors": true}], "max-line-length": ["error", 120], "not-rely-on-time": "off", "named-parameters-mapping": "warn", "no-console": "off", "var-name-mixedcase": "off"}}