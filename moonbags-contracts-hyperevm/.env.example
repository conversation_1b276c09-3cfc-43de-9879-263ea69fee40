# IMPORTANT: You need a mnemonic or real private key to deploy to testnet or mainnet.

# Either set MNEMONIC or PRIVATE_KEY, but not both, to deploy.
# If PRIVATE_KEY is defined, MNEMONIC will be ignored.

# By default, a test mnemonic is used for testing if MNEMONIC is not defined.

# Your 12-word mnemonic
# here is where your twelve words mnemonic should be put my friend
export MNEMONIC="test test test test test test test test test test test junk"

# Your private keys (without the 0x)
# Uncomment the following to set PRIVATE_KEY.
# export PRIVATE_KEY="****************************************************************"

# To Add more private keys set the following and also update `hardhat.config.ts`.
# export PRIVATE_KEY_2=""
# export PRIVATE_KEY_3=""
# export PRIVATE_KEY_4=""
# export PRIVATE_KEY_5=""

# Your Infura API key for Ethereum (Required)
export INFURA_API_KEY="********************************"

# Block Explorer API keys (Optional)
export ARBISCAN_API_KEY="********************************"
export BSCSCAN_API_KEY="********************************"
export ETHERSCAN_API_KEY="********************************"
export FANTOM_API_KEY="********************************"
export OPTIMISM_API_KEY="********************************"
export POLYGONSCAN_API_KEY="********************************"
export SNOWTRACE_API_KEY="********************************"

# Gas Reporter (Optional)
export GAS_PRICE=20

# Your CoinMarketCap API key (Optional).
# Learn more: https://coinmarketcap.com/api/documentation/v1/#section/Introduction
export COIN_MARKET_CAP_API_KEY=""

# For fork testing (Optional)
export ALCHEMY_API_KEY=""

# Foundry Profile (Optional)
export FOUNDRY_PROFILE="default"
