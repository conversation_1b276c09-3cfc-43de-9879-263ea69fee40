{"name": "moonbags-contracts-hyperevm", "description": "", "version": "0.1.0", "author": {"name": "Sotatek-HungLai", "url": "https://github.com/Sotatek-HungLai"}, "scripts": {"chain": "hardhat node --no-deploy --export-all ./generated/chain.json --network hardhat", "clean": "shx rm -rf ./artifacts ./cache ./cache_hardhat ./coverage ./coverage.json ./types && yarn typechain", "compile": "cross-env TS_NODE_TRANSPILE_ONLY=true hardhat compile", "coverage": "hardhat coverage --solcoverjs ./.solcover.js --temp artifacts --testfiles \"test/**/*.ts\" && yarn typechain", "deploy:hardhat-deploy": "hardhat deploy --export-all ./deployments/hardhat_contracts.json", "deploy": "yarn deploy:hardhat-deploy --network hardhat", "deploy:network": "yarn deploy:hardhat-deploy --network", "fork": "hardhat node --network hardhat --fork https://mainnet.infura.io/v3/********************************", "lint": "yarn lint:sol && yarn lint:ts && yarn prettier:check", "lint:sol": "forge fmt --check && yarn solhint \"{script,contracts,test}/**/*.sol\"", "lint:ts": "eslint --ignore-path ./.eslintignore --ext .js,.ts .", "networks": "hardhat networks", "postinstall": "DOTENV_CONFIG_PATH=./.env.example yarn typechain", "prettier:write": "prettier --write \"**/*.{js,json,md,ts,yml}\"", "prettier:check": "prettier --check \"**/*.{js,json,md,ts,yml}\"", "test": "hardhat test", "test:gas": "REPORT_GAS=true CONTRACT_SIZER=true hardhat test", "test:trace": "hardhat test --trace", "test:fulltrace": "hardhat test --fulltrace", "typechain": "cross-env TS_NODE_TRANSPILE_ONLY=true hardhat typechain", "verify": "hardhat run scripts/verify.ts --network"}, "devDependencies": {"@ethersproject/bignumber": "^5.7.0", "@nomicfoundation/hardhat-chai-matchers": "^2.0.7", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-foundry": "^1.1.2", "@nomicfoundation/hardhat-ignition": "^0.15.5", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.5", "@nomicfoundation/hardhat-network-helpers": "^1.0.11", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.10", "@nomicfoundation/ignition-core": "^0.15.5", "@nomiclabs/hardhat-ethers": "^2.2.3", "@openzeppelin/contracts": "^5.3.0", "@primitivefi/hardhat-dodoc": "^0.2.3", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.19", "@types/fs-extra": "^11.0.4", "@types/mocha": "^10.0.8", "@types/node": "^22.5.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "chai": "4.5.0", "chalk": "4.1.2", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "ds-test": "github:dapphub/ds-test#e282159d5170298eb2455a6c05280ab5a73a4ef0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "ethers": "6.13.2", "evm-bn": "^1.1.2", "forge-std": "github:foundry-rs/forge-std#v1.9.2", "fs-extra": "^11.2.0", "hardhat": "2.22.10", "hardhat-contract-sizer": "2.10.0", "hardhat-deploy": "^0.12.4", "hardhat-deploy-ethers": "^0.4.2", "hardhat-gas-reporter": "2.2.1", "hardhat-preprocessor": "^0.1.5", "hardhat-test-suite-generator": "^2.0.0", "lodash": "^4.17.21", "mocha": "^10.7.3", "prettier": "^3.3.3", "shx": "0.3.4", "solhint": "^5.0.3", "solidity-coverage": "0.8.13", "ts-generator": "^0.1.1", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.6.2"}, "files": ["/contracts"], "keywords": ["blockchain", "ethereum", "hardhat", "forge", "foundry", "smart-contracts", "solidity", "template"], "license": "UNLICENSED", "packageManager": "yarn@4.5.0", "dependencies": {"@openzeppelin/contracts-upgradeable": "^5.3.0", "@uniswap/v3-core": "^1.0.1", "@uniswap/v3-periphery": "^1.4.4", "bignumber.js": "^9.3.0"}}