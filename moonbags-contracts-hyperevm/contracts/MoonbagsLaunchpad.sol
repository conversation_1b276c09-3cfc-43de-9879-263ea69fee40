// SPDX-License-Identifier: MIT
pragma solidity ^0.8.23;

import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol";
import "./interfaces/INonfungiblePositionManager.sol";
import "./MoonbagsStake.sol";
import "./libraries/uniswap/TickMath.sol";
import "./libraries/uniswap/FullMath.sol";
import "./LaunchpadToken.sol";
import "./TokenLock.sol";
import "./libraries/BondingCurveMath.sol";
import "./interfaces/IWETH9.sol";

/**
 * @title Moonbags Launchpad
 */
contract MoonbagsLaunchpad is
    Initializable,
    ReentrancyGuardUpgradeable,
    OwnableUpgradeable,
    IERC721Receiver
{
    using SafeERC20 for IERC20;
    using BondingCurveMath for uint256;

    // Constants
    uint256 public constant VERSION = 1;
    uint256 public constant FEE_DENOMINATOR = 10_000; // 100% = 10000
    uint256 public constant DEFAULT_THRESHOLD = 0.03 ether; // 3 SUI
    uint256 public constant MINIMUM_THRESHOLD = 0.02 ether; // 2 SUI
    uint256 public constant MIN_LOCK_DURATION = 1 hours;
    uint256 public constant ONE_CHECKPOINT_TIMESTAMP = 1 seconds;
    uint256 public constant DISTRIBUTE_FEE_LOCK_DURATION = 5 minutes;

    uint24 public constant FEE_LOW = 500; // 0.05%
    uint24 public constant FEE_MEDIUM = 3000; // 0.3%
    uint24 public constant FEE_HIGH = 10_000; // 1%
    uint24 public constant DEFAULT_FEE_TIER = FEE_MEDIUM; // Use 0.3% as default (most common)
    uint256 public constant PRICE_SCALE_192 = 2 ** 192;
    uint256 public constant POOL_CREATION_FEE = 0.001 ether; // 0.01 SUI

    uint256 public constant DEFAULT_PLATFORM_FEE = 100; // 1%
    uint256 public constant DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES = 8_000_000 * 10 ** 6; // 8M
    // tokens (6 decimals)
    uint256 public constant DEFAULT_REMAIN_TOKEN_RESERVES = 2_000_000 * 10 ** 6; // 2M tokens (6
    // decimals)
    uint8 public constant DEFAULT_TOKEN_DECIMALS = 6;
    uint16 public constant DEFAULT_PLATFORM_FEE_WITHDRAW = 1500; // 15%
    uint16 public constant DEFAULT_CREATOR_FEE_WITHDRAW = 3000; // 30%
    uint16 public constant DEFAULT_STAKE_FEE_WITHDRAW = 2500; // 25%
    uint16 public constant DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW = 2000; // 20%

    // address public constant DEAD_ADDRESS = ******************************************;
    address public constant PLATFORM_TOKEN_BUYER = ******************************************;

    uint256 public constant MAX_URI_LENGTH = 300;
    uint256 public constant MAX_DESCRIPTION_LENGTH = 1000;
    uint256 public constant MAX_SOCIAL_LENGTH = 500;

    struct Configuration {
        address admin;
        address treasury;
        address feePlatformRecipient;
        uint256 platformFee;
        uint256 initialVirtualTokenReserves;
        uint256 remainTokenReserves;
        uint8 tokenDecimals;
        uint16 initPlatformFeeWithdraw; // 15% to platform
        uint16 initCreatorFeeWithdraw; // 30% to creator
        uint16 initStakeFeeWithdraw; // 25% to stakers
        uint16 initPlatformStakeFeeWithdraw; // 20% to platform stakers
        address platformTokenAddress;
    }

    struct Pool {
        uint256 realHypeReserves;
        uint256 realTokenReserves;
        uint256 virtualTokenReserves;
        uint256 virtualHypeReserves;
        uint256 remainTokenReserves;
        uint256 virtualRemainTokenReserves;
        uint256 feeRecipient;
        bool isCompleted;
        uint256 threshold;
        uint16 platformFeeWithdraw;
        uint16 creatorFeeWithdraw;
        uint16 stakeFeeWithdraw;
        uint16 platformStakeFeeWithdraw;
        uint256 creationTimestamp;
        uint256 feeDistributionUnlockTime;
    }

    struct ThresholdConfig {
        uint256 threshold;
    }

    Configuration public config;
    ThresholdConfig public thresholdConfig;
    TokenLock public tokenLock;
    MoonbagsStake public moonbagsStake;
    mapping(address => Pool) public pools;
    mapping(address => uint256) public tokenToPositionId;

    // HyperSwap V3 Integration Variables
    INonfungiblePositionManager public nonfungiblePositionManager;
    address public weth9;
    uint24 public activeFeeTier;

    event ConfigurationUpdated(
        uint256 newPlatformFee,
        uint256 newInitialVirtualTokenReserves,
        uint256 newRemainTokenReserves,
        uint8 newTokenDecimals,
        uint16 newInitPlatformFeeWithdraw,
        uint16 newInitCreatorFeeWithdraw,
        uint16 newInitStakeFeeWithdraw,
        uint16 newInitPlatformStakeFeeWithdraw,
        address newPlatformTokenAddress,
        uint256 timestamp
    );

    event TokenCreated(
        address indexed token,
        address indexed creator,
        string name,
        string symbol,
        string uri,
        string description,
        string twitter,
        string telegram,
        string website,
        uint256 virtualHypeReserves,
        uint256 virtualTokenReserves,
        uint256 realHypeReserves,
        uint256 realTokenReserves,
        uint16 platformFeeWithdraw,
        uint16 creatorFeeWithdraw,
        uint16 stakeFeeWithdraw,
        uint16 platformStakeFeeWithdraw,
        uint256 threshold,
        uint256 timestamp
    );

    event Trade(
        address indexed token,
        address indexed user,
        bool indexed isBuy,
        uint256 hypeAmount,
        uint256 tokenAmount,
        uint256 virtualHypeReserves,
        uint256 virtualTokenReserves,
        uint256 realHypeReserves,
        uint256 realTokenReserves,
        uint256 fee,
        uint256 timestamp
    );

    event TokenLockContractUpdated(address indexed oldTokenLock, address indexed newTokenLock);
    event MoonbagsStakeUpdated(address indexed oldMoonbagsStake, address indexed newMoonbagsStake);

    event PoolCompleted(address indexed token, string lp, uint256 timestamp);

    event V3PoolCreated(address indexed token, address indexed v3Pool, uint256 timestamp);

    // Custom errors
    error InvalidInput();
    error InsufficientAmount();
    error PoolAlreadyCompleted();
    error PoolNotCompleted();
    error TokenNotExists();
    error Unauthorized();
    error InvalidConfiguration();
    error InsufficientTokenReserves();
    error InsufficientHypeReserves();
    error LPValueDecreased();
    error NoPositionFound();
    error InvalidDistributionTime();
    error InvalidWithdrawalAmount();
    error NotEnoughThreshold();

    /**
     * @notice Initialize the contract
     * @param _nonfungiblePositionManager Position Manager address
     * @param _weth9 Weth9 address
     * @param _poolFee Pool fee (e.g., 3000 for 0.3%)
     * @param _platformTokenAddress Platform token address
     * @param _moonbagsStake MoonbagsStake contract address
     */
    function initialize(
        address _nonfungiblePositionManager,
        address _weth9,
        uint24 _poolFee,
        address _platformTokenAddress,
        address _moonbagsStake,
        address _tokenLock
    )
        public
        initializer
    {
        if (
            _nonfungiblePositionManager == address(0) || _weth9 == address(0)
                || _platformTokenAddress == address(0) || _moonbagsStake == address(0)
                || _tokenLock == address(0)
        ) {
            revert InvalidInput();
        }

        __ReentrancyGuard_init();
        __Ownable_init(msg.sender);

        config = Configuration({
            admin: msg.sender,
            treasury: msg.sender,
            feePlatformRecipient: msg.sender,
            platformTokenAddress: _platformTokenAddress,
            platformFee: DEFAULT_PLATFORM_FEE,
            initialVirtualTokenReserves: DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES,
            remainTokenReserves: DEFAULT_REMAIN_TOKEN_RESERVES,
            tokenDecimals: DEFAULT_TOKEN_DECIMALS,
            initPlatformFeeWithdraw: DEFAULT_PLATFORM_FEE_WITHDRAW,
            initCreatorFeeWithdraw: DEFAULT_CREATOR_FEE_WITHDRAW,
            initStakeFeeWithdraw: DEFAULT_STAKE_FEE_WITHDRAW,
            initPlatformStakeFeeWithdraw: DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW
        });

        nonfungiblePositionManager = INonfungiblePositionManager(_nonfungiblePositionManager);
        weth9 = _weth9;
        activeFeeTier = _poolFee;
        moonbagsStake = MoonbagsStake(_moonbagsStake);
        tokenLock = TokenLock(_tokenLock);

        emit ConfigurationUpdated(
            DEFAULT_PLATFORM_FEE,
            DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES,
            DEFAULT_REMAIN_TOKEN_RESERVES,
            DEFAULT_TOKEN_DECIMALS,
            DEFAULT_PLATFORM_FEE_WITHDRAW,
            DEFAULT_CREATOR_FEE_WITHDRAW,
            DEFAULT_STAKE_FEE_WITHDRAW,
            DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW,
            _platformTokenAddress,
            block.timestamp
        );
    }

    /**
     * @notice Create a new pool with bonding curve
     * @param name Token name
     * @param symbol Token symbol
     * @param uri Token metadata URI (max 300 characters)
     * @param description Token description (max 1000 characters)
     * @param twitter Twitter handle (max 500 characters)
     * @param telegram Telegram link (max 500 characters)
     * @param website Website URL (max 500 characters)
     * @param customThreshold Custom threshold (0 for default)
     */
    function createPool(
        string memory name,
        string memory symbol,
        string memory uri,
        string memory description,
        string memory twitter,
        string memory telegram,
        string memory website,
        uint256 customThreshold
    )
        external
        payable
        nonReentrant
        returns (address tokenAddress)
    {
        if (msg.value < POOL_CREATION_FEE) revert InvalidInput();

        tokenAddress = _createPoolInternal(
            name, symbol, uri, description, twitter, telegram, website, customThreshold
        );

        payable(msg.sender).transfer(msg.value - POOL_CREATION_FEE);
        payable(config.feePlatformRecipient).transfer(POOL_CREATION_FEE);

        return tokenAddress;
    }

    /**
     * @notice Create a new pool with bonding curve and lock the first buy
     * @param name Token name
     * @param symbol Token symbol
     * @param uri Token metadata URI (max 300 characters)
     * @param description Token description (max 1000 characters)
     * @param twitter Twitter handle (max 500 characters)
     * @param telegram Telegram link (max 500 characters)
     * @param website Website URL (max 500 characters)
     * @param customThreshold Custom threshold (0 for default)
     * @param amountOut Amount of tokens to buy and lock
     * @param lockDuration Duration to lock the purchased tokens (minimum 1 hour)
     */
    function createAndLockFirstBuy(
        string memory name,
        string memory symbol,
        string memory uri,
        string memory description,
        string memory twitter,
        string memory telegram,
        string memory website,
        uint256 customThreshold,
        uint256 amountOut,
        uint256 lockDuration
    )
        external
        payable
        nonReentrant
        returns (address tokenAddress)
    {
        // Enhanced validation based on Sui implementation
        if (address(tokenLock) == address(0)) revert InvalidConfiguration();
        if (lockDuration < MIN_LOCK_DURATION) revert InvalidInput();
        if (msg.value < POOL_CREATION_FEE) revert InvalidInput();

        tokenAddress = _createPoolInternal(
            name, symbol, uri, description, twitter, telegram, website, customThreshold
        );

        uint256 amountIn = msg.value - POOL_CREATION_FEE;

        if (amountIn > 0) {
            _buyDirectAndLock(tokenAddress, amountIn, amountOut, lockDuration, msg.sender);
        }

        payable(config.feePlatformRecipient).transfer(POOL_CREATION_FEE);

        return tokenAddress;
    }

    function _createPoolInternal(
        string memory name,
        string memory symbol,
        string memory uri,
        string memory description,
        string memory twitter,
        string memory telegram,
        string memory website,
        uint256 customThreshold
    )
        internal
        returns (address tokenAddress)
    {
        if (bytes(name).length == 0 || bytes(symbol).length == 0) revert InvalidInput();
        if (bytes(uri).length > MAX_URI_LENGTH) revert InvalidInput();
        if (bytes(description).length > MAX_DESCRIPTION_LENGTH) revert InvalidInput();
        if (bytes(twitter).length > MAX_SOCIAL_LENGTH) revert InvalidInput();
        if (bytes(telegram).length > MAX_SOCIAL_LENGTH) revert InvalidInput();
        if (bytes(website).length > MAX_SOCIAL_LENGTH) revert InvalidInput();

        uint256 threshold = customThreshold == 0 ? DEFAULT_THRESHOLD : customThreshold;
        if (threshold < MINIMUM_THRESHOLD) revert InvalidInput();

        uint256 initialVirtualHypeReserves = calculateInitialVirtualHypeReserves(threshold);
        uint256 actualVirtualTokenReserves = calculateActualVirtualTokenReserves();
        uint256 virtualRemainTokenReserves = calculateVirtualRemainTokenReserves();

        LaunchpadToken token = new LaunchpadToken(name, symbol, config.tokenDecimals, uri);
        tokenAddress = address(token);

        if (address(tokenLock) != address(0)) {
            token.setTokenLockContract(address(tokenLock));
        }

        pools[tokenAddress] = Pool({
            realHypeReserves: 0,
            realTokenReserves: config.initialVirtualTokenReserves,
            virtualTokenReserves: actualVirtualTokenReserves,
            virtualHypeReserves: initialVirtualHypeReserves,
            remainTokenReserves: config.remainTokenReserves,
            virtualRemainTokenReserves: virtualRemainTokenReserves,
            feeRecipient: 0,
            isCompleted: false,
            threshold: threshold,
            platformFeeWithdraw: config.initPlatformFeeWithdraw,
            creatorFeeWithdraw: config.initCreatorFeeWithdraw,
            stakeFeeWithdraw: config.initStakeFeeWithdraw,
            platformStakeFeeWithdraw: config.initPlatformStakeFeeWithdraw,
            creationTimestamp: block.timestamp,
            feeDistributionUnlockTime: block.timestamp + DISTRIBUTE_FEE_LOCK_DURATION
        });

        Pool storage pool = pools[tokenAddress];
        emit TokenCreated(
            tokenAddress,
            msg.sender,
            name,
            symbol,
            uri,
            description,
            twitter,
            telegram,
            website,
            pool.virtualHypeReserves,
            pool.virtualTokenReserves,
            pool.realHypeReserves,
            pool.realTokenReserves,
            pool.platformFeeWithdraw,
            pool.creatorFeeWithdraw,
            pool.stakeFeeWithdraw,
            pool.platformStakeFeeWithdraw,
            threshold,
            block.timestamp
        );

        _initializeStakingPools(tokenAddress, msg.sender);

        return tokenAddress;
    }

    /**
     * @notice Initialize staking pools for a newly created token
     * @param tokenAddress The address of the newly created token
     * @param creator The address of the token creator
     */
    function _initializeStakingPools(address tokenAddress, address creator) internal {
        if (address(moonbagsStake) == address(0)) revert InvalidInput();

        moonbagsStake.initializeStakingPool(tokenAddress);
        moonbagsStake.initializeCreatorPool(tokenAddress, creator);
    }

    /**
     * @notice Buy exact amount in and lock tokens for specified duration
     * @param token Token address
     * @param amountIn Exact amount of ETH to spend
     * @param amountOutMin Minimum amount of tokens to receive
     */
    function buyExactIn(
        address token,
        uint256 amountIn,
        uint256 amountOutMin
    )
        external
        payable
        nonReentrant
        returns (uint256 tokenAmountOut)
    {
        if (!isToken(token)) revert TokenNotExists();
        if (address(tokenLock) == address(0)) revert InvalidConfiguration();
        if (amountIn == 0) revert InvalidInput();
        if (msg.value < amountIn) revert InsufficientAmount();

        Pool storage pool = pools[token];
        if (pool.isCompleted) revert PoolAlreadyCompleted();

        uint256 tokenReservesInPool = pool.virtualTokenReserves > pool.virtualRemainTokenReserves
            ? pool.virtualTokenReserves - pool.virtualRemainTokenReserves
            : 0;

        (uint256 actualAmountOut, uint256 hypeAmountInSwap, uint256 fee) =
        _calculateBuyExactInAmounts(
            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn, tokenReservesInPool
        );

        if (actualAmountOut < amountOutMin) revert InvalidInput();
        if (msg.value < hypeAmountInSwap + fee) revert InsufficientAmount();

        _executeSwap(pool, 0, hypeAmountInSwap, actualAmountOut, 0);
        pool.virtualTokenReserves = pool.virtualTokenReserves - actualAmountOut;
        pool.feeRecipient += fee;

        emit Trade(
            token,
            msg.sender,
            true,
            hypeAmountInSwap,
            actualAmountOut,
            pool.virtualHypeReserves,
            pool.virtualTokenReserves,
            pool.realHypeReserves,
            pool.realTokenReserves,
            fee,
            block.timestamp
        );

        if (actualAmountOut == tokenReservesInPool) {
            _completePool(token, pool);
        }

        if (block.timestamp < pool.creationTimestamp + ONE_CHECKPOINT_TIMESTAMP) {
            LaunchpadToken(token).mint(address(this), actualAmountOut);
            IERC20(token).safeIncreaseAllowance(address(tokenLock), actualAmountOut);
            uint256 lockEndTime = block.timestamp + 1 hours;
            tokenLock.createLock(token, actualAmountOut, lockEndTime, msg.sender);
        } else {
            LaunchpadToken(token).mint(msg.sender, actualAmountOut);
        }

        uint256 totalUsed = hypeAmountInSwap + fee;
        uint256 refundAmount = msg.value - totalUsed;
        if (refundAmount > 0) {
            payable(msg.sender).transfer(refundAmount);
        }

        return actualAmountOut;
    }

    function _calculateBuyExactInAmounts(
        uint256 virtualHypeReserves,
        uint256 virtualTokenReserves,
        uint256 amountIn,
        uint256 tokenReservesInPool
    )
        internal
        view
        returns (uint256 actualAmountOut, uint256 hypeAmountInSwap, uint256 fee)
    {
        uint256 initialTokenOut = BondingCurveMath.calculateTokensForExactBase(
            virtualHypeReserves, virtualTokenReserves, amountIn
        );
        if (initialTokenOut > tokenReservesInPool) {
            actualAmountOut = tokenReservesInPool;
            hypeAmountInSwap = BondingCurveMath.calculateBaseCostForExactTokens(
                virtualHypeReserves, virtualTokenReserves, actualAmountOut
            ) + 1;
        } else {
            actualAmountOut = initialTokenOut;
            hypeAmountInSwap = amountIn;
        }

        fee = (hypeAmountInSwap * config.platformFee) / FEE_DENOMINATOR;
    }

    // /**
    //  * @notice Buy exact amount out with automatic token locking
    //  * @param token Token address
    //  * @param tokenAmountOut Amount of tokens to buy and lock
    //  * @return actualAmountOut Amount of tokens purchased and locked
    //  */
    // function buyExactOut(
    //     address token,
    //     uint256 tokenAmountOut
    // )
    //     external
    //     payable
    //     nonReentrant
    //     returns (uint256 actualAmountOut)
    // {
    //     if (!isToken(token)) revert TokenNotExists();
    //     if (address(tokenLock) == address(0)) revert InvalidConfiguration();

    //     Pool storage pool = pools[token];
    //     if (pool.isCompleted) revert PoolAlreadyCompleted();
    //     if (tokenAmountOut == 0) revert InvalidInput();

    //     uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;
    //     uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves
    //         ? pool.virtualTokenReserves - virtualRemainTokenReserves
    //         : 0;

    //     uint256 hypeCostBeforeFee;
    //     uint256 fee;
    //     (actualAmountOut, hypeCostBeforeFee, fee) = _calculateBuyExactOutAmounts(
    //         pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut,
    // tokenReservesInPool
    //     );

    //     uint256 totalCost = hypeCostBeforeFee + fee;
    //     if (msg.value < totalCost) revert InsufficientAmount();

    //     _executeSwap(pool, 0, msg.value - fee, actualAmountOut, msg.value - totalCost);

    //     pool.feeRecipient += fee;
    //     pool.virtualTokenReserves = pool.virtualTokenReserves - actualAmountOut;

    //     LaunchpadToken(token).mint(msg.sender, actualAmountOut);

    //     _handleFeesAndRefund(fee, msg.value - totalCost);

    //     emit Trade(
    //         token,
    //         msg.sender,
    //         true,
    //         hypeCostBeforeFee,
    //         actualAmountOut,
    //         pool.virtualHypeReserves,
    //         pool.virtualTokenReserves,
    //         pool.realHypeReserves,
    //         pool.realTokenReserves,
    //         fee,
    //         block.timestamp
    //     );

    //     if (actualAmountOut == tokenReservesInPool) {
    //         _completePool(token, pool);
    //     }

    //     return actualAmountOut;
    // }

    function _calculateBuyExactOutAmounts(
        uint256 virtualHypeReserves,
        uint256 virtualTokenReserves,
        uint256 tokenAmountOut,
        uint256 tokenReservesInPool
    )
        internal
        view
        returns (uint256 actualAmountOut, uint256 hypeCostBeforeFee, uint256 fee)
    {
        actualAmountOut =
            tokenAmountOut > tokenReservesInPool ? tokenReservesInPool : tokenAmountOut;
        hypeCostBeforeFee = BondingCurveMath.calculateBaseCostForExactTokens(
            virtualHypeReserves, virtualTokenReserves, actualAmountOut
        ) + 1;

        fee = (hypeCostBeforeFee * config.platformFee) / FEE_DENOMINATOR;
    }

    function sellExactIn(
        address token,
        uint256 tokenAmountIn,
        uint256 amountOutMin
    )
        external
        nonReentrant
        returns (uint256 hypeAmountOut)
    {
        if (!isToken(token)) revert TokenNotExists();
        Pool storage pool = pools[token];
        if (pool.isCompleted) revert PoolAlreadyCompleted();
        if (tokenAmountIn == 0) revert InvalidInput();

        LaunchpadToken(token).burn(msg.sender, tokenAmountIn);

        uint256 netAmount;
        uint256 fee;
        (hypeAmountOut, netAmount, fee) = _calculateSellExactInAmounts(
            pool.virtualHypeReserves,
            pool.virtualTokenReserves,
            tokenAmountIn,
            pool.realHypeReserves
        );

        if (netAmount < amountOutMin) revert InsufficientAmount();

        _executeSwap(pool, tokenAmountIn, 0, 0, hypeAmountOut);
        pool.virtualHypeReserves = pool.virtualHypeReserves - hypeAmountOut;
        pool.feeRecipient += fee;

        payable(msg.sender).transfer(netAmount);

        emit Trade(
            token,
            msg.sender,
            false,
            hypeAmountOut,
            tokenAmountIn,
            pool.virtualHypeReserves,
            pool.virtualTokenReserves,
            pool.realHypeReserves,
            pool.realTokenReserves,
            fee,
            block.timestamp
        );

        return netAmount;
    }

    function _calculateSellExactInAmounts(
        uint256 virtualHypeReserves,
        uint256 virtualTokenReserves,
        uint256 tokenAmountIn,
        uint256 realHypeReserves
    )
        internal
        view
        returns (uint256 hypeAmountOut, uint256 netAmount, uint256 fee)
    {
        hypeAmountOut = BondingCurveMath.calculateBaseForExactTokens(
            virtualHypeReserves, virtualTokenReserves, tokenAmountIn
        );
        if (hypeAmountOut > realHypeReserves) {
            hypeAmountOut = realHypeReserves;
        }
        fee = (hypeAmountOut * config.platformFee) / FEE_DENOMINATOR;
        netAmount = hypeAmountOut - fee;
    }

    function _executeSwap(
        Pool storage pool,
        uint256 tokenAmountIn,
        uint256 hypeAmountIn,
        uint256 tokenAmountOut,
        uint256 hypeAmountOut
    )
        internal
        returns (uint256 actualTokenOut, uint256 actualHypeOut)
    {
        uint256 beforeVirtualTokenReserves = pool.virtualTokenReserves;
        uint256 beforeVirtualHypeReserves = pool.virtualHypeReserves;

        if (tokenAmountIn == 0 && hypeAmountIn == 0) revert InvalidInput();

        pool.virtualTokenReserves = pool.virtualTokenReserves + tokenAmountIn;
        pool.virtualHypeReserves = pool.virtualHypeReserves + hypeAmountIn;

        if (tokenAmountIn > 0) {
            pool.virtualTokenReserves = pool.virtualTokenReserves - tokenAmountOut;
        }
        if (hypeAmountIn > 0) {
            pool.virtualHypeReserves = pool.virtualHypeReserves - hypeAmountOut;
        }

        if (
            beforeVirtualTokenReserves * beforeVirtualHypeReserves
                > pool.virtualTokenReserves * pool.virtualHypeReserves
        ) revert LPValueDecreased();

        pool.realTokenReserves += tokenAmountIn;
        pool.realHypeReserves += hypeAmountIn;

        if (pool.realTokenReserves < tokenAmountOut) revert InsufficientTokenReserves();
        if (pool.realHypeReserves < hypeAmountOut) revert InsufficientHypeReserves();

        pool.realTokenReserves -= tokenAmountOut;
        pool.realHypeReserves -= hypeAmountOut;

        return (tokenAmountOut, hypeAmountOut);
    }

    /**
     * @notice Complete pool when all tokens are sold
     */
    function _completePool(address token, Pool storage pool) internal {
        pool.isCompleted = true;
        _migrateToHyperSwapV3(token, pool);
    }

    function createThresholdConfig(uint256 _threshold) external onlyOwner {
        thresholdConfig = ThresholdConfig(_threshold);
    }

    function earlyCompletePool(address token) external onlyOwner nonReentrant {
        if (!isToken(token)) revert TokenNotExists();

        Pool storage pool = pools[token];
        if (pool.isCompleted) revert PoolAlreadyCompleted();

        pool.isCompleted = true;

        uint256 realHypeReserves = pool.realHypeReserves;
        uint256 realTokenReserves = pool.realTokenReserves;
        uint256 remainTokenReserves = pool.remainTokenReserves;

        if (realHypeReserves < thresholdConfig.threshold) {
            revert NotEnoughThreshold();
        }

        if (thresholdConfig.threshold > 0) {
            payable(config.admin).transfer(thresholdConfig.threshold);
        }

        if (config.remainTokenReserves > 0) {
            LaunchpadToken(token).mint(config.admin, config.remainTokenReserves);
        }

        uint256 remainingHype = realHypeReserves - thresholdConfig.threshold;
        if (remainingHype > 0) {
            payable(msg.sender).transfer(remainingHype);
        }

        uint256 remainingTokens =
            realTokenReserves + remainTokenReserves - config.remainTokenReserves;
        if (remainingTokens > 0) {
            LaunchpadToken(token).mint(msg.sender, remainingTokens);
        }

        pool.realHypeReserves = 0;
        pool.realTokenReserves = 0;

        emit PoolCompleted(token, "0x0", block.timestamp);
    }

    /**
     * @notice Distribute fees for a token
     * @param token Token address
     */
    function _distributeFees(address token) internal {
        if (!isToken(token)) revert TokenNotExists();
        Pool storage pool = pools[token];

        if (block.timestamp < pool.feeDistributionUnlockTime) {
            revert InvalidDistributionTime();
        }

        uint256 totalFees = pool.feeRecipient;
        if (totalFees == 0) return;

        if (address(this).balance < totalFees) {
            return;
        }

        uint256 platformShare = (totalFees * pool.platformFeeWithdraw) / FEE_DENOMINATOR;
        uint256 creatorShare = (totalFees * pool.creatorFeeWithdraw) / FEE_DENOMINATOR;
        uint256 stakeShare = (totalFees * pool.stakeFeeWithdraw) / FEE_DENOMINATOR;
        uint256 platformStakeShare = (totalFees * pool.platformStakeFeeWithdraw) / FEE_DENOMINATOR;

        uint256 totalDistribution = platformShare + creatorShare + stakeShare + platformStakeShare;
        if (totalDistribution > totalFees) {
            revert InvalidWithdrawalAmount();
        }

        if (platformShare > 0) {
            payable(config.feePlatformRecipient).transfer(platformShare);
        }

        if (stakeShare > 0) {
            moonbagsStake.updateRewardIndex{ value: stakeShare }(token);
        }

        if (creatorShare > 0) {
            moonbagsStake.depositCreatorPool{ value: creatorShare }(token);
        }

        if (platformStakeShare > 0) {
            moonbagsStake.updateRewardIndex{ value: platformStakeShare }(
                config.platformTokenAddress
            );
        }

        pool.feeDistributionUnlockTime = block.timestamp + DISTRIBUTE_FEE_LOCK_DURATION;

        uint256 remainingBalance = totalFees - totalDistribution;
        pool.feeRecipient -= totalFees;

        if (remainingBalance > 0) {
            payable(PLATFORM_TOKEN_BUYER).transfer(remainingBalance);
        }
    }

    /**
     * @notice Distribute accumulated bonding curve fees for a token
     * @param token Token address
     */
    function distributeBondingCurveFees(address token) external nonReentrant {
        _distributeFees(token);
    }

    /**
     * @notice Collect all fees from a HyperSwap V3 position
     * @param token Token address associated with the position
     * @return amount0 The amount of fees collected in token0
     * @return amount1 The amount of fees collected in token1
     */
    function collectHyperSwapFees(address token)
        external
        nonReentrant
        returns (uint256 amount0, uint256 amount1)
    {
        if (!isToken(token)) revert TokenNotExists();

        Pool storage pool = pools[token];
        if (!pool.isCompleted) revert PoolNotCompleted();

        uint256 tokenId = tokenToPositionId[token];
        if (tokenId == 0) revert NoPositionFound();

        INonfungiblePositionManager.CollectParams memory params = INonfungiblePositionManager
            .CollectParams({
            tokenId: tokenId,
            recipient: address(this),
            amount0Max: type(uint128).max,
            amount1Max: type(uint128).max
        });

        (amount0, amount1) = nonfungiblePositionManager.collect(params);
        (,, address token0,,,,,,,,,) = nonfungiblePositionManager.positions(tokenId);
        (uint256 bondedTokenAmount, uint256 whypeTokenAmount) =
            token0 == weth9 ? (amount1, amount0) : (amount0, amount1);

        if (bondedTokenAmount > 0) {
            IERC20(token).safeTransfer(config.treasury, bondedTokenAmount);
        }

        if (whypeTokenAmount > 0) {
            IWETH9(weth9).withdraw(whypeTokenAmount);
            pool.feeRecipient += whypeTokenAmount;
        }

        _distributeFees(token);

        return (amount0, amount1);
    }

    /**
     * @notice Update configuration (only owner)
     */
    function updateConfiguration(
        uint256 _platformFee,
        uint256 _initialVirtualTokenReserves,
        uint256 _remainTokenReserves,
        uint8 _tokenDecimals,
        uint16 _initPlatformFeeWithdraw,
        uint16 _initCreatorFeeWithdraw,
        uint16 _initStakeFeeWithdraw,
        uint16 _initPlatformStakeFeeWithdraw,
        address _platformTokenAddress
    )
        external
        onlyOwner
    {
        if (
            _initPlatformFeeWithdraw + _initCreatorFeeWithdraw + _initStakeFeeWithdraw
                + _initPlatformStakeFeeWithdraw > FEE_DENOMINATOR
        ) revert InvalidInput();

        config.platformFee = _platformFee;
        config.initialVirtualTokenReserves = _initialVirtualTokenReserves;
        config.remainTokenReserves = _remainTokenReserves;
        config.tokenDecimals = _tokenDecimals;
        config.initPlatformFeeWithdraw = _initPlatformFeeWithdraw;
        config.initCreatorFeeWithdraw = _initCreatorFeeWithdraw;
        config.initStakeFeeWithdraw = _initStakeFeeWithdraw;
        config.initPlatformStakeFeeWithdraw = _initPlatformStakeFeeWithdraw;
        config.platformTokenAddress = _platformTokenAddress;

        emit ConfigurationUpdated(
            _platformFee,
            _initialVirtualTokenReserves,
            _remainTokenReserves,
            _tokenDecimals,
            _initPlatformFeeWithdraw,
            _initCreatorFeeWithdraw,
            _initStakeFeeWithdraw,
            _initPlatformStakeFeeWithdraw,
            _platformTokenAddress,
            block.timestamp
        );
    }

    /**
     * @notice skim
     * @param token Token address to skim
     */
    function skim(address token) external onlyOwner {
        if (token == address(0)) revert InvalidInput();
        if (!isToken(token)) revert TokenNotExists();
        Pool storage pool = pools[token];

        if (pool.isCompleted) revert PoolAlreadyCompleted();

        (uint256 realHypeReserves, uint256 realTokenReserves) =
            (pool.realHypeReserves, pool.realTokenReserves);

        if (realHypeReserves > 0) {
            payable(msg.sender).transfer(realHypeReserves);
            pool.realHypeReserves = 0;
        }

        if (realTokenReserves > 0) {
            LaunchpadToken(token).mint(msg.sender, realTokenReserves);
            pool.realTokenReserves = 0;
        }
    }

    function updateThresholdConfig(uint256 newThreshold) external onlyOwner {
        thresholdConfig.threshold = newThreshold;
    }

    /**
     * @notice Update the initial virtual token reserves (only owner)
     * @param _initialVirtualTokenReserves New initial virtual token reserves
     */
    function updateInitialVirtualTokenReserves(uint256 _initialVirtualTokenReserves)
        external
        onlyOwner
    {
        config.initialVirtualTokenReserves = _initialVirtualTokenReserves;
    }

    /**
     * @notice Update fee recipients (only owner)
     */
    function updateFeeRecipients(
        address _treasury,
        address _feePlatformRecipient
    )
        external
        onlyOwner
    {
        config.treasury = _treasury;
        config.feePlatformRecipient = _feePlatformRecipient;
    }

    /**
     * @notice Update the initial withdraw fees (only owner)
     */
    function updateConfigWithdrawFee(
        uint16 _newInitPlatformFeeWithdraw,
        uint16 _newInitCreatorFeeWithdraw,
        uint16 _newInitStakeFeeWithdraw,
        uint16 _newInitPlatformStakeFeeWithdraw
    )
        external
        onlyOwner
    {
        config.initPlatformFeeWithdraw = _newInitPlatformFeeWithdraw;
        config.initCreatorFeeWithdraw = _newInitCreatorFeeWithdraw;
        config.initStakeFeeWithdraw = _newInitStakeFeeWithdraw;
        config.initPlatformStakeFeeWithdraw = _newInitPlatformStakeFeeWithdraw;
    }

    /**
     * @notice Set TokenLock contract (only owner)
     * @param _tokenLock Address of the new TokenLock contract
     */
    function setTokenLock(address _tokenLock) external onlyOwner {
        if (_tokenLock == address(0)) revert InvalidInput();

        address oldTokenLock = address(tokenLock);
        tokenLock = TokenLock(_tokenLock);

        emit TokenLockContractUpdated(oldTokenLock, _tokenLock);
    }

    /**
     * @notice Set the MoonbagsStake contract address (only owner)
     * @param _moonbagsStake Address of the MoonbagsStake contract
     */
    function setMoonbagsStake(address _moonbagsStake) external onlyOwner {
        if (_moonbagsStake == address(0)) revert InvalidInput();

        address oldMoonbagsStake = address(moonbagsStake);
        moonbagsStake = MoonbagsStake(_moonbagsStake);

        emit MoonbagsStakeUpdated(oldMoonbagsStake, _moonbagsStake);
    }

    /**
     * @notice Update the active fee tier for new pools (only owner)
     * @param newFeeTier The new fee tier (500, 3000, or 10000)
     */
    function updateActiveFeeTier(uint24 newFeeTier) external onlyOwner {
        _getTickSpacingInternal(newFeeTier); // Will revert if invalid
        activeFeeTier = newFeeTier;
    }

    function calculateInitialVirtualHypeReserves(uint256 threshold) public view returns (uint256) {
        uint256 remainTokenReserves = config.remainTokenReserves;
        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;

        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();

        return
            (threshold * remainTokenReserves) / (initialVirtualTokenReserves - remainTokenReserves);
    }

    function calculateVirtualRemainTokenReserves() public view returns (uint256) {
        uint256 remainTokenReserves = config.remainTokenReserves;
        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;

        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();

        return (remainTokenReserves * initialVirtualTokenReserves)
            / (initialVirtualTokenReserves - remainTokenReserves);
    }

    function calculateActualVirtualTokenReserves() public view returns (uint256) {
        uint256 remainTokenReserves = config.remainTokenReserves;
        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;

        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();

        return (initialVirtualTokenReserves * initialVirtualTokenReserves)
            / (initialVirtualTokenReserves - remainTokenReserves);
    }

    function getPool(address token) external view returns (Pool memory) {
        return pools[token];
    }

    /**
     * @notice Check if a token exists (has been created through the launchpad)
     * @param token The token address to check
     * @return exists True if the token exists
     */
    function isToken(address token) public view returns (bool exists) {
        return pools[token].creationTimestamp != 0;
    }

    function estimateBuyExactInTokens(
        address token,
        uint256 amountIn
    )
        external
        view
        returns (uint256)
    {
        if (!isToken(token)) return 0;
        Pool memory pool = pools[token];
        if (pool.isCompleted) return 0;

        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;
        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves
            ? pool.virtualTokenReserves - virtualRemainTokenReserves
            : 0;

        uint256 initialTokenOut = BondingCurveMath.calculateTokensForExactBase(
            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn
        );

        return initialTokenOut > tokenReservesInPool ? tokenReservesInPool : initialTokenOut;
    }

    function estimateBuyExactInCost(
        address token,
        uint256 amountIn
    )
        external
        view
        returns (uint256 totalCost)
    {
        if (!isToken(token)) return 0;
        Pool memory pool = pools[token];
        if (pool.isCompleted) return 0;

        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;
        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves
            ? pool.virtualTokenReserves - virtualRemainTokenReserves
            : 0;

        (, uint256 hypeAmountInSwap, uint256 fee) = _calculateBuyExactInAmounts(
            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn, tokenReservesInPool
        );

        return hypeAmountInSwap + fee;
    }

    function estimateBuyExactOutCost(
        address token,
        uint256 tokenAmountOut
    )
        external
        view
        returns (uint256)
    {
        if (!isToken(token)) return 0;
        Pool memory pool = pools[token];
        if (pool.isCompleted) return 0;

        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;
        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves
            ? pool.virtualTokenReserves - virtualRemainTokenReserves
            : 0;

        (, uint256 hypeCostBeforeFee, uint256 fee) = _calculateBuyExactOutAmounts(
            pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut, tokenReservesInPool
        );

        return hypeCostBeforeFee + fee;
    }

    function estimateSellTokens(
        address token,
        uint256 tokenAmountIn
    )
        external
        view
        returns (uint256)
    {
        if (!isToken(token)) return 0;
        Pool memory pool = pools[token];
        if (pool.isCompleted) return 0;

        (, uint256 netAmount,) = _calculateSellExactInAmounts(
            pool.virtualHypeReserves,
            pool.virtualTokenReserves,
            tokenAmountIn,
            pool.realHypeReserves
        );

        return netAmount;
    }

    function _buyDirectAndLock(
        address token,
        uint256 amountIn,
        uint256 amountOut,
        uint256 lockDuration,
        address recipient
    )
        internal
        returns (uint256 tokenAmountOut)
    {
        Pool storage pool = pools[token];
        if (pool.isCompleted) revert PoolAlreadyCompleted();
        if (amountOut == 0) revert InvalidInput();
        if (lockDuration < MIN_LOCK_DURATION) revert InvalidInput();

        uint256 tokenReservesInPool = pool.virtualTokenReserves - pool.virtualRemainTokenReserves;
        tokenAmountOut = amountOut > tokenReservesInPool ? tokenReservesInPool : amountOut;

        uint256 amountInSwap = BondingCurveMath.calculateBaseCostForExactTokens(
            pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut
        ) + 1;

        uint256 swapFee = (amountInSwap * config.platformFee) / FEE_DENOMINATOR;
        uint256 totalSwapCost = amountInSwap + swapFee;

        if (amountIn < totalSwapCost) revert InsufficientAmount();

        _executeSwap(pool, 0, amountInSwap, tokenAmountOut, 0);
        pool.virtualTokenReserves = pool.virtualTokenReserves - tokenAmountOut;
        pool.feeRecipient += swapFee;

        LaunchpadToken(token).mint(address(this), tokenAmountOut);
        IERC20(token).safeIncreaseAllowance(address(tokenLock), tokenAmountOut);
        uint256 endTime = block.timestamp + lockDuration;
        tokenLock.createLock(token, tokenAmountOut, endTime, recipient);

        uint256 refundAmount = amountIn - totalSwapCost;
        if (refundAmount > 0) {
            payable(msg.sender).transfer(refundAmount);
        }

        emit Trade(
            token,
            msg.sender,
            true,
            amountInSwap,
            tokenAmountOut,
            pool.virtualHypeReserves,
            pool.virtualTokenReserves,
            pool.realHypeReserves,
            pool.realTokenReserves,
            swapFee,
            block.timestamp
        );

        // Check if pool should be completed
        if (tokenAmountOut == tokenReservesInPool) {
            _completePool(token, pool);
        }

        return tokenAmountOut;
    }

    /**
     * @notice Migrate liquidity to HyperSwap V3 pool using Uniswap V3 standard parameters
     * @param token The token address
     * @param pool The pool data
     */
    function _migrateToHyperSwapV3(address token, Pool storage pool) internal virtual {
        (uint256 tokenAmount, uint256 hypeAmount) =
            (pool.realTokenReserves + pool.remainTokenReserves, pool.realHypeReserves);
        pool.realHypeReserves = 0;
        pool.realTokenReserves = 0;
        pool.remainTokenReserves = 0;

        (address token0, address token1) = token < weth9 ? (token, weth9) : (weth9, token);
        (uint256 amount0Desired, uint256 amount1Desired) =
            token < weth9 ? (tokenAmount, hypeAmount) : (hypeAmount, tokenAmount);

        uint160 sqrtPriceX96 = _calculateInitialSqrtPrice(amount0Desired, amount1Desired);

        address v3Pool = nonfungiblePositionManager.createAndInitializePoolIfNecessary(
            token0, token1, activeFeeTier, sqrtPriceX96
        );

        emit V3PoolCreated(token, v3Pool, block.timestamp);

        LaunchpadToken(token).setListed(true);
        LaunchpadToken(token).mint(address(this), tokenAmount);

        IWETH9(weth9).deposit{ value: hypeAmount }();
        IERC20(weth9).safeIncreaseAllowance(address(nonfungiblePositionManager), hypeAmount);
        IERC20(token).safeIncreaseAllowance(address(nonfungiblePositionManager), tokenAmount);

        (uint256 tokenId,,,) = _mintV3Position(token0, token1, amount0Desired, amount1Desired);

        tokenToPositionId[token] = tokenId;
    }

    /**
     * @notice Mint V3 liquidity position
     * @dev Uses standard fee tiers and full-range tick calculations based on tick spacing
     */
    function _mintV3Position(
        address token0,
        address token1,
        uint256 amount0Desired,
        uint256 amount1Desired
    )
        internal
        returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1)
    {
        uint24 feeTier = activeFeeTier;
        int24 tickSpacing = _getTickSpacingInternal(feeTier);

        int24 tickLower = (TickMath.MIN_TICK / tickSpacing) * tickSpacing;
        int24 tickUpper = (TickMath.MAX_TICK / tickSpacing) * tickSpacing;

        INonfungiblePositionManager.MintParams memory params = INonfungiblePositionManager
            .MintParams({
            token0: token0,
            token1: token1,
            fee: feeTier,
            tickLower: tickLower,
            tickUpper: tickUpper,
            amount0Desired: amount0Desired,
            amount1Desired: amount1Desired,
            amount0Min: 0,
            amount1Min: 0,
            recipient: address(this),
            deadline: block.timestamp + 300
        });

        return nonfungiblePositionManager.mint(params);
    }

    /**
     * @notice Internal tick spacing calculation
     */
    function _getTickSpacingInternal(uint24 fee) internal pure returns (int24 tickSpacing) {
        if (fee == FEE_LOW) {
            // 500 = 0.05%
            return 10;
        } else if (fee == FEE_MEDIUM) {
            // 3000 = 0.3%
            return 60;
        } else if (fee == FEE_HIGH) {
            // 10000 = 1%
            return 200;
        } else {
            revert InvalidInput();
        }
    }

    /**
     * @notice Calculate the initial sqrt price for a V3 pool
     * @param amount0Desired Amount of token0 to be paired
     * @param amount1Desired Amount of token1 to be paired
     * @return sqrtPriceX96 The sqrt price in Q64.96 format for pool initialization
     */
    function _calculateInitialSqrtPrice(
        uint256 amount0Desired,
        uint256 amount1Desired
    )
        internal
        pure
        returns (uint160 sqrtPriceX96)
    {
        uint256 priceX96 =
            FullMath.mulDivRoundingUp(PRICE_SCALE_192, amount1Desired, amount0Desired);
        return uint160(sqrt(priceX96));
    }

    /**
     * @notice Babylonian sqrt implementation
     * @param y The value to calculate sqrt for
     * @return z The square root of y
     */
    function sqrt(uint256 y) internal pure returns (uint256 z) {
        if (y > 3) {
            z = y;
            uint256 x = y / 2 + 1;
            while (x < z) {
                z = x;
                x = (y / x + x) / 2;
            }
        } else if (y != 0) {
            z = 1;
        }
        // else z = 0 (default value)
    }

    /**
     * @notice Handle NFT transfers (required for IERC721Receiver)
     */
    function onERC721Received(
        address,
        address,
        uint256,
        bytes calldata
    )
        external
        view
        override
        returns (bytes4)
    {
        if (msg.sender != address(nonfungiblePositionManager)) {
            revert Unauthorized();
        }
        return this.onERC721Received.selector;
    }

    /**
     * @notice Receive function to accept ETH
     */
    receive() external payable { }
}
