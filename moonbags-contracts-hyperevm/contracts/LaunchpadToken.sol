// SPDX-License-Identifier: MIT
pragma solidity ^0.8.23;

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title LaunchpadToken
 * @notice ERC20 token deployed through the Moonbags launchpad
 */
contract LaunchpadToken is ERC20, Ownable {
    error TokenTransfersBlocked();

    bool public isListed;
    uint8 private _decimals;
    string private _tokenURI;
    address public tokenLock;

    /**
     * @notice Constructor for LaunchpadToken
     * @param name_ Token name
     * @param symbol_ Token symbol
     * @param decimals_ Token decimals
     * @param uri_ Token metadata URI
     */
    constructor(
        string memory name_,
        string memory symbol_,
        uint8 decimals_,
        string memory uri_
    )
        ERC20(name_, symbol_)
        Ownable(msg.sender)
    {
        _decimals = decimals_;
        _tokenURI = uri_;
        isListed = false; // Initially set to false
    }

    /**
     * @notice Mint new tokens to a specified address
     * @param to Address to receive the minted tokens
     * @param amount Amount of tokens to mint
     */
    function mint(address to, uint256 amount) external onlyOwner {
        _mint(to, amount);
    }

    /**
     * @notice Set the listed status of the token
     * @param isListed_ Boolean indicating if the token is listed
     */
    function setListed(bool isListed_) external onlyOwner {
        isListed = isListed_;
    }

    /**
     * @notice Set the address of the token lock contract
     * @param _tokenLock Address of the token lock contract
     */
    function setTokenLockContract(address _tokenLock) external onlyOwner {
        tokenLock = _tokenLock;
    }

    /**
     * @notice Burn tokens from a specified address
     * @param from Address from which tokens will be burned
     * @param amount Amount of tokens to burn
     */
    function burn(address from, uint256 amount) external onlyOwner {
        _burn(from, amount);
    }

    /**
     * @dev Internal function to handle token transfers with transfer restrictions
     * @param from Sender address
     * @param to Recipient address
     * @param value Amount of tokens to transfer
     */
    function _update(address from, address to, uint256 value) internal override {
        if (
            !isListed && from != address(0) && to != address(0) && from != owner() && to != owner()
                && from != tokenLock
        ) {
            revert TokenTransfersBlocked();
        }
        super._update(from, to, value);
    }

    /**
     * @notice Returns the number of decimals used for the token
     * @return uint8 Number of decimals
     */
    function decimals() public view override returns (uint8) {
        return _decimals;
    }

    /**
     * @notice Returns the token metadata URI
     * @return string Token URI
     */
    function tokenURI() public view returns (string memory) {
        return _tokenURI;
    }

    /**
     * @notice Set the token metadata URI
     * @param uri_ New token URI
     */
    function setTokenURI(string memory uri_) external onlyOwner {
        _tokenURI = uri_;
    }
}
