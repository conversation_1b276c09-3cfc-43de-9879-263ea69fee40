// SPDX-License-Identifier: MIT
pragma solidity ^0.8.23;

import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

/**
 * @title TokenLock
 */
contract TokenLock is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {
    using SafeERC20 for IERC20;

    struct Configuration {
        address admin;
    }

    Configuration public config;

    struct LockContract {
        address token;
        uint256 amount;
        uint256 startTime;
        uint256 endTime;
        address recipient;
        address locker;
        bool closed;
    }

    mapping(uint256 => LockContract) public locks;
    uint256 public nextLockId;

    // Events
    event LockCreated(
        uint256 indexed lockId,
        address indexed locker,
        address indexed recipient,
        address tokenAddress,
        uint256 amount,
        uint256 startTime,
        uint256 endTime
    );

    event TokensWithdrawn(
        uint256 indexed lockId, address indexed sender, address indexed recipient, uint256 amount
    );

    event ConfigUpdated(address oldAdmin, address newAdmin);

    // Errors
    error InvalidParams();
    error Unauthorized();
    error ContractClosed();
    error InvalidLockId();

    /**
     * @notice Initialize the contract
     */
    function initialize() external initializer {
        __ReentrancyGuard_init();
        __Ownable_init(msg.sender);

        config = Configuration({ admin: msg.sender });
        nextLockId = 1;
    }

    /**
     * @notice Create a new time-locked token contract
     * @param token Address of the ERC20 token to lock
     * @param amount Amount of tokens to lock
     * @param endTime End time of the lock in seconds
     * @param recipient Address that will be able to claim the tokens after the lock period
     * @return lockId The ID of the created lock
     */
    function createLock(
        address token,
        uint256 amount,
        uint256 endTime,
        address recipient
    )
        external
        nonReentrant
        returns (uint256 lockId)
    {
        uint256 startTime = block.timestamp;

        if (endTime <= startTime) revert InvalidParams();
        if (token == address(0) || recipient == address(0)) revert InvalidParams();

        lockId = nextLockId++;
        locks[lockId] = LockContract({
            token: token,
            amount: amount,
            startTime: startTime,
            endTime: endTime,
            recipient: recipient,
            locker: msg.sender,
            closed: false
        });

        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);

        emit LockCreated(lockId, msg.sender, recipient, token, amount, startTime, endTime);

        return lockId;
    }

    /**
     * @notice Withdraw tokens from a lock after the lock period has ended
     * @param lockId ID of the lock to withdraw from
     */
    function withdraw(uint256 lockId) external nonReentrant {
        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();

        LockContract storage lockContract = locks[lockId];
        address sender = msg.sender;

        if (
            sender != config.admin && sender != lockContract.recipient
                && sender != lockContract.locker
        ) {
            revert Unauthorized();
        }
        if (lockContract.closed) revert ContractClosed();
        if (block.timestamp < lockContract.endTime) revert Unauthorized();

        lockContract.closed = true;
        IERC20(lockContract.token).safeTransfer(lockContract.recipient, lockContract.amount);

        emit TokensWithdrawn(lockId, sender, lockContract.recipient, lockContract.amount);
    }

    /**
     * @notice Extend the lock duration of an existing time-locked token contract
     * @param lockId ID of the lock to extend
     * @param newEndTime New end time for the lock
     */
    function extendLock(uint256 lockId, uint256 newEndTime) external {
        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();

        LockContract storage lockContract = locks[lockId];
        address sender = msg.sender;

        if (sender != lockContract.locker) revert Unauthorized();
        if (lockContract.closed) revert ContractClosed();

        uint256 currentTime = block.timestamp;
        if (newEndTime <= currentTime || newEndTime <= lockContract.endTime) {
            revert InvalidParams();
        }

        lockContract.endTime = newEndTime;

        emit LockCreated(
            lockId,
            lockContract.locker,
            lockContract.recipient,
            lockContract.token,
            lockContract.amount,
            lockContract.startTime,
            lockContract.endTime
        );
    }

    /**
     * @notice Update configuration
     * @param newAdmin New admin address
     */
    function updateConfig(address newAdmin) external onlyOwner {
        if (newAdmin == address(0)) revert InvalidParams();

        address oldAdmin = config.admin;

        config.admin = newAdmin;

        emit ConfigUpdated(oldAdmin, newAdmin);
    }

    /**
     * @notice Get lock information
     * @param lockId ID of the lock
     * @return lockContract The lock contract details
     */
    function getLock(uint256 lockId) external view returns (LockContract memory lockContract) {
        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();
        return locks[lockId];
    }

    /**
     * @notice Check if a lock is withdrawable
     * @param lockId ID of the lock
     * @return withdrawable True if the lock can be withdrawn
     */
    function isWithdrawable(uint256 lockId) external view returns (bool withdrawable) {
        if (lockId == 0 || lockId >= nextLockId) return false;

        LockContract memory lockContract = locks[lockId];
        return !lockContract.closed && block.timestamp >= lockContract.endTime;
    }

    /**
     * @notice Get the number of locks created
     * @return count Total number of locks
     */
    function getLockCount() external view returns (uint256 count) {
        return nextLockId - 1;
    }

    /**
     * @notice Get current configuration
     * @return admin Current admin address
     */
    function getConfig() external view returns (address admin) {
        return config.admin;
    }

    /**
     * @notice Emergency withdraw function (admin only)
     * @param token Token address to withdraw
     * @param amount Amount to withdraw
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(config.admin, amount);
    }
}
