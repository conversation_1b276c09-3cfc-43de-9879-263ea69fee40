import { DeployFunction } from "hardhat-deploy/types";
import { HardhatRuntimeEnvironment } from "hardhat/types";

const func: DeployFunction = async function (hre: HardhatRuntimeEnvironment) {
    const { deployments, getNamedAccounts } = hre;
    const { deploy, log } = deployments;
    const { deployer } = await getNamedAccounts();

    log(`\nDeploying Platform Token (HYPE)...`);

    const tokenConfig = {
        name: "SHRO Platform Token",
        symbol: "SHRO",
        decimals: 9,
        uri: "https://moonbags.io/metadata/platform-token.json",
        initialSupply: "**********",
    };

    try {
        const platformTokenDeployment = await deploy("PlatformToken", {
            contract: "LaunchpadToken",
            from: deployer,
            args: [tokenConfig.name, tokenConfig.symbol, tokenConfig.decimals, tokenConfig.uri],
            log: true,
        });

        log(`✅ Platform Token deployed at: ${platformTokenDeployment.address}`);
        log(`Platform Token Configuration:`);
        log(`   Name: ${tokenConfig.name}`);
        log(`   Symbol: ${tokenConfig.symbol}`);
        log(`   Decimals: ${tokenConfig.decimals}`);
        log(`   URI: ${tokenConfig.uri}`);

        if (platformTokenDeployment.newlyDeployed) {
            const { ethers } = hre;
            const platformToken = await ethers.getContractAt(
                "LaunchpadToken",
                platformTokenDeployment.address
            );

            // Set token as listed to allow transfers
            log(`\nSetting token as listed...`);
            const setListedTx = await platformToken.setListed(true);
            await setListedTx.wait();

            log(`✅ Platform token set as listed - transfers enabled`);
        }

        log(`\nPlatform Token deployment completed successfully!`);
    } catch (error) {
        log(`❌ Error deploying Platform Token: ${error}`);
        throw error;
    }
};

func.tags = ["PlatformToken"];
func.dependencies = [];

export default func;
