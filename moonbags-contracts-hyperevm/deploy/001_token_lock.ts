import { DeployFunction } from "hardhat-deploy/types";
import { HardhatRuntimeEnvironment } from "hardhat/types";

const func: DeployFunction = async function (hre: HardhatRuntimeEnvironment) {
    const { deployments, getNamedAccounts } = hre;
    const { deploy, log } = deployments;
    const { deployer } = await getNamedAccounts();

    log(`\nDeploying TokenLock contract...`);

    try {
        const tokenLockDeployment = await deploy("TokenLock", {
            from: deployer,
            log: true,
            proxy: {
                proxyContract: "OpenZeppelinTransparentProxy",
                execute: {
                    init: {
                        methodName: "initialize",
                        args: [],
                    },
                },
            },
        });

        log(`✅ TokenLock deployed at: ${tokenLockDeployment.address}`);

        if (tokenLockDeployment.newlyDeployed) {
            const { ethers } = hre;
            const tokenLock = await ethers.getContractAt("TokenLock", tokenLockDeployment.address);

            log(`\nVerifying TokenLock initialization...`);

            // Check configuration
            const admin = await tokenLock.getConfig();
            log(`   Admin: ${admin}`);

            // Check owner
            const owner = await tokenLock.owner();
            log(`   Owner: ${owner}`);

            // Check next lock ID
            const nextLockId = await tokenLock.nextLockId();
            log(`   Next Lock ID: ${nextLockId}`);
        }

        log(`\nTokenLock deployment completed successfully!`);
    } catch (error) {
        log(`❌ Error deploying TokenLock: ${error}`);
        throw error;
    }
};

func.tags = ["TokenLock"];
func.dependencies = [];

export default func;
