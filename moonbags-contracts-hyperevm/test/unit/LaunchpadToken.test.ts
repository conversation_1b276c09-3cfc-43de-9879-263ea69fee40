import { <PERSON><PERSON><PERSON><PERSON>Add<PERSON> } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import { LaunchpadToken } from "../../types";

describe("LaunchpadToken", function () {
    let token: LaunchpadToken;
    let owner: Signer<PERSON>ithAddress;
    let user1: Signer<PERSON>ithAddress;
    let user2: Signer<PERSON>ithAddress;
    let user3: Signer<PERSON>ithAddress;

    // Token parameters
    const TOKEN_NAME = "Test Token";
    const TOKEN_SYMBOL = "TEST";
    const TOKEN_DECIMALS = 6;
    const TOKEN_URI = "https://example.com/token-metadata.json";
    const MINT_AMOUNT = ethers.parseUnits("1000", TOKEN_DECIMALS);

    beforeEach(async function () {
        [owner, user1, user2, user3] = await ethers.getSigners();

        // Deploy LaunchpadToken
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        token = await LaunchpadTokenFactory.deploy(
            TOKEN_NAME,
            TOKEN_SYMBOL,
            TOKEN_DECIMALS,
            TOKEN_URI
        );
        await token.waitForDeployment();
    });

    describe("Deployment", function () {
        it("should set the correct name", async function () {
            expect(await token.name()).to.equal(TOKEN_NAME);
        });

        it("should set the correct symbol", async function () {
            expect(await token.symbol()).to.equal(TOKEN_SYMBOL);
        });

        it("should set the correct decimals", async function () {
            expect(await token.decimals()).to.equal(TOKEN_DECIMALS);
        });

        it("should set the correct token URI", async function () {
            expect(await token.tokenURI()).to.equal(TOKEN_URI);
        });

        it("should set the correct owner", async function () {
            expect(await token.owner()).to.equal(owner.address);
        });

        it("should initialize with isListed as false", async function () {
            expect(await token.isListed()).to.be.false;
        });

        it("should have zero total supply initially", async function () {
            expect(await token.totalSupply()).to.equal(0);
        });
    });

    describe("Minting", function () {
        it("should allow owner to mint tokens", async function () {
            await token.mint(user1.address, MINT_AMOUNT);

            expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT);
            expect(await token.totalSupply()).to.equal(MINT_AMOUNT);
        });

        it("should emit Transfer event when minting", async function () {
            await expect(token.mint(user1.address, MINT_AMOUNT))
                .to.emit(token, "Transfer")
                .withArgs(ethers.ZeroAddress, user1.address, MINT_AMOUNT);
        });

        it("should allow minting to multiple addresses", async function () {
            const amount1 = ethers.parseUnits("500", TOKEN_DECIMALS);
            const amount2 = ethers.parseUnits("300", TOKEN_DECIMALS);

            await token.mint(user1.address, amount1);
            await token.mint(user2.address, amount2);

            expect(await token.balanceOf(user1.address)).to.equal(amount1);
            expect(await token.balanceOf(user2.address)).to.equal(amount2);
            expect(await token.totalSupply()).to.equal(amount1 + amount2);
        });

        it("should revert when non-owner tries to mint", async function () {
            await expect(token.connect(user1).mint(user2.address, MINT_AMOUNT))
                .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount")
                .withArgs(user1.address);
        });

        it("should allow minting zero amount", async function () {
            await token.mint(user1.address, 0);
            expect(await token.balanceOf(user1.address)).to.equal(0);
        });
    });

    describe("Burning", function () {
        beforeEach(async function () {
            // Mint some tokens first
            await token.mint(user1.address, MINT_AMOUNT);
            await token.mint(user2.address, MINT_AMOUNT);
        });

        it("should allow owner to burn tokens", async function () {
            const burnAmount = ethers.parseUnits("300", TOKEN_DECIMALS);
            const initialBalance = await token.balanceOf(user1.address);
            const initialSupply = await token.totalSupply();

            await token.burn(user1.address, burnAmount);

            expect(await token.balanceOf(user1.address)).to.equal(initialBalance - burnAmount);
            expect(await token.totalSupply()).to.equal(initialSupply - burnAmount);
        });

        it("should emit Transfer event when burning", async function () {
            const burnAmount = ethers.parseUnits("300", TOKEN_DECIMALS);

            await expect(token.burn(user1.address, burnAmount))
                .to.emit(token, "Transfer")
                .withArgs(user1.address, ethers.ZeroAddress, burnAmount);
        });

        it("should revert when non-owner tries to burn", async function () {
            const burnAmount = ethers.parseUnits("300", TOKEN_DECIMALS);

            await expect(token.connect(user1).burn(user1.address, burnAmount))
                .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount")
                .withArgs(user1.address);
        });

        it("should revert when burning more than balance", async function () {
            const excessiveAmount = MINT_AMOUNT + ethers.parseUnits("1", TOKEN_DECIMALS);

            await expect(token.burn(user1.address, excessiveAmount))
                .to.be.revertedWithCustomError(token, "ERC20InsufficientBalance")
                .withArgs(user1.address, MINT_AMOUNT, excessiveAmount);
        });

        it("should allow burning entire balance", async function () {
            await token.burn(user1.address, MINT_AMOUNT);

            expect(await token.balanceOf(user1.address)).to.equal(0);
        });
    });

    describe("Listing Status", function () {
        it("should allow owner to set listed status to true", async function () {
            await token.setListed(true);
            expect(await token.isListed()).to.be.true;
        });

        it("should allow owner to set listed status to false", async function () {
            await token.setListed(true);
            await token.setListed(false);
            expect(await token.isListed()).to.be.false;
        });

        it("should revert when non-owner tries to set listed status", async function () {
            await expect(token.connect(user1).setListed(true))
                .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount")
                .withArgs(user1.address);
        });
    });

    describe("Token URI Management", function () {
        it("should allow owner to update token URI", async function () {
            const newURI = "https://newdomain.com/updated-metadata.json";

            await token.setTokenURI(newURI);
            expect(await token.tokenURI()).to.equal(newURI);
        });

        it("should revert when non-owner tries to update token URI", async function () {
            const newURI = "https://newdomain.com/updated-metadata.json";

            await expect(token.connect(user1).setTokenURI(newURI))
                .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount")
                .withArgs(user1.address);
        });

        it("should allow setting empty token URI", async function () {
            await token.setTokenURI("");
            expect(await token.tokenURI()).to.equal("");
        });
    });

    describe("Transfer Restrictions", function () {
        beforeEach(async function () {
            // Mint tokens to users for testing
            await token.mint(owner.address, MINT_AMOUNT);
            await token.mint(user1.address, MINT_AMOUNT);
            await token.mint(user2.address, MINT_AMOUNT);
        });

        describe("When token is not listed", function () {
            it("should allow owner to transfer tokens", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                await token.transfer(user1.address, transferAmount);

                expect(await token.balanceOf(owner.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should allow transfers to owner", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                await token.connect(user1).transfer(owner.address, transferAmount);

                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(owner.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should allow transfers from owner", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                await token.transfer(user2.address, transferAmount);

                expect(await token.balanceOf(owner.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should block transfers between non-owner users", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                await expect(
                    token.connect(user1).transfer(user2.address, transferAmount)
                ).to.be.revertedWithCustomError(token, "TokenTransfersBlocked");
            });

            it("should block transferFrom between non-owner users", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                // User1 approves user3 to spend tokens
                await token.connect(user1).approve(user3.address, transferAmount);

                // User3 tries to transfer from user1 to user2
                await expect(
                    token.connect(user3).transferFrom(user1.address, user2.address, transferAmount)
                ).to.be.revertedWithCustomError(token, "TokenTransfersBlocked");
            });

            it("should allow owner to use transferFrom when owner is involved", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                // User1 approves owner to spend tokens
                await token.connect(user1).approve(owner.address, transferAmount);

                // Owner transfers from user1 to owner (owner is involved as recipient)
                await token.transferFrom(user1.address, owner.address, transferAmount);

                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(owner.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should allow transferFrom to owner", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                // User1 approves user3 to spend tokens
                await token.connect(user1).approve(user3.address, transferAmount);

                // User3 transfers from user1 to owner
                await token
                    .connect(user3)
                    .transferFrom(user1.address, owner.address, transferAmount);

                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(owner.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should allow transferFrom from owner", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                // Owner approves user3 to spend tokens
                await token.approve(user3.address, transferAmount);

                // User3 transfers from owner to user2
                await token
                    .connect(user3)
                    .transferFrom(owner.address, user2.address, transferAmount);

                expect(await token.balanceOf(owner.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT + transferAmount);
            });
        });

        describe("When token is listed", function () {
            beforeEach(async function () {
                await token.setListed(true);
            });

            it("should allow transfers between any users", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                await token.connect(user1).transfer(user2.address, transferAmount);

                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should allow transferFrom between any users", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                // User1 approves user3 to spend tokens
                await token.connect(user1).approve(user3.address, transferAmount);

                // User3 transfers from user1 to user2
                await token
                    .connect(user3)
                    .transferFrom(user1.address, user2.address, transferAmount);

                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should still allow owner transfers", async function () {
                const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

                await token.transfer(user1.address, transferAmount);

                expect(await token.balanceOf(owner.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT + transferAmount);
            });

            it("should allow multiple transfers in sequence", async function () {
                const transferAmount = ethers.parseUnits("50", TOKEN_DECIMALS);

                // user1 -> user2
                await token.connect(user1).transfer(user2.address, transferAmount);
                // user2 -> user3
                await token.connect(user2).transfer(user3.address, transferAmount);

                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
                expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT); // received and sent same amount
                expect(await token.balanceOf(user3.address)).to.equal(transferAmount);
            });
        });

        describe("Special transfer cases", function () {
            it("should allow minting when not listed", async function () {
                // Minting should work regardless of listing status
                const mintAmount = ethers.parseUnits("500", TOKEN_DECIMALS);
                await token.mint(user3.address, mintAmount);

                expect(await token.balanceOf(user3.address)).to.equal(mintAmount);
            });

            it("should allow burning when not listed", async function () {
                // Get current balance (user1 already has MINT_AMOUNT from beforeEach)
                const currentBalance = await token.balanceOf(user1.address);

                // Burning should work regardless of listing status
                const burnAmount = ethers.parseUnits("300", TOKEN_DECIMALS);
                await token.burn(user1.address, burnAmount);

                expect(await token.balanceOf(user1.address)).to.equal(currentBalance - burnAmount);
            });

            it("should handle zero amount transfers when not listed", async function () {
                // Zero amount transfers should be blocked between non-owners when not listed
                await expect(
                    token.connect(user1).transfer(user2.address, 0)
                ).to.be.revertedWithCustomError(token, "TokenTransfersBlocked");
            });

            it("should handle zero amount transfers when listed", async function () {
                await token.setListed(true);

                // Zero amount transfers should be allowed when listed
                await token.connect(user1).transfer(user2.address, 0);

                // Balances should remain unchanged
                expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT);
                expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT);
            });
        });
    });

    describe("ERC20 Standard Compliance", function () {
        beforeEach(async function () {
            await token.mint(owner.address, MINT_AMOUNT);
            await token.mint(user1.address, MINT_AMOUNT);
            await token.setListed(true);
        });

        it("should handle approvals correctly", async function () {
            const approvalAmount = ethers.parseUnits("500", TOKEN_DECIMALS);

            await token.connect(user1).approve(user2.address, approvalAmount);

            expect(await token.allowance(user1.address, user2.address)).to.equal(approvalAmount);
        });

        it("should emit Approval event", async function () {
            const approvalAmount = ethers.parseUnits("500", TOKEN_DECIMALS);

            await expect(token.connect(user1).approve(user2.address, approvalAmount))
                .to.emit(token, "Approval")
                .withArgs(user1.address, user2.address, approvalAmount);
        });

        it("should handle transferFrom with allowance", async function () {
            const approvalAmount = ethers.parseUnits("500", TOKEN_DECIMALS);
            const transferAmount = ethers.parseUnits("300", TOKEN_DECIMALS);

            await token.connect(user1).approve(user2.address, approvalAmount);
            await token.connect(user2).transferFrom(user1.address, user3.address, transferAmount);

            expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
            expect(await token.balanceOf(user3.address)).to.equal(transferAmount);
            expect(await token.allowance(user1.address, user2.address)).to.equal(
                approvalAmount - transferAmount
            );
        });

        it("should revert transferFrom with insufficient allowance", async function () {
            const approvalAmount = ethers.parseUnits("200", TOKEN_DECIMALS);
            const transferAmount = ethers.parseUnits("300", TOKEN_DECIMALS);

            await token.connect(user1).approve(user2.address, approvalAmount);

            await expect(
                token.connect(user2).transferFrom(user1.address, user3.address, transferAmount)
            )
                .to.be.revertedWithCustomError(token, "ERC20InsufficientAllowance")
                .withArgs(user2.address, approvalAmount, transferAmount);
        });

        it("should revert transfer with insufficient balance", async function () {
            const excessiveAmount = MINT_AMOUNT + ethers.parseUnits("1", TOKEN_DECIMALS);

            await expect(token.connect(user1).transfer(user2.address, excessiveAmount))
                .to.be.revertedWithCustomError(token, "ERC20InsufficientBalance")
                .withArgs(user1.address, MINT_AMOUNT, excessiveAmount);
        });
    });

    describe("Edge Cases and Integration", function () {
        it("should handle ownership transfer", async function () {
            // Transfer ownership
            await token.transferOwnership(user1.address);

            expect(await token.owner()).to.equal(user1.address);

            // New owner should be able to mint
            await token.connect(user1).mint(user2.address, MINT_AMOUNT);
            expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT);

            // Old owner should not be able to mint
            await expect(token.mint(user3.address, MINT_AMOUNT))
                .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount")
                .withArgs(owner.address);
        });

        it("should handle complex transfer scenarios after ownership change", async function () {
            // Mint tokens to original owner and user1
            await token.mint(owner.address, MINT_AMOUNT);
            await token.mint(user1.address, MINT_AMOUNT);

            // Transfer ownership to user1
            await token.transferOwnership(user1.address);

            // Now user1 is the owner, so transfers involving user1 should be allowed even when not listed
            const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

            // user1 (new owner) can transfer to user2
            await token.connect(user1).transfer(user2.address, transferAmount);
            expect(await token.balanceOf(user2.address)).to.equal(transferAmount);

            // user2 can transfer to user1 (new owner)
            await token.connect(user2).transfer(user1.address, transferAmount);
            expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT);

            // But user2 cannot transfer to original owner (no longer owner)
            await token.connect(user1).mint(user2.address, MINT_AMOUNT); // New owner mints tokens to user2
            await expect(
                token.connect(user2).transfer(owner.address, transferAmount)
            ).to.be.revertedWithCustomError(token, "TokenTransfersBlocked");
        });

        it("should maintain correct total supply through various operations", async function () {
            const mintAmount1 = ethers.parseUnits("1000", TOKEN_DECIMALS);
            const mintAmount2 = ethers.parseUnits("500", TOKEN_DECIMALS);
            const burnAmount = ethers.parseUnits("300", TOKEN_DECIMALS);

            // Initial supply should be 0
            expect(await token.totalSupply()).to.equal(0);

            // Mint to user1
            await token.mint(user1.address, mintAmount1);
            expect(await token.totalSupply()).to.equal(mintAmount1);

            // Mint to user2
            await token.mint(user2.address, mintAmount2);
            expect(await token.totalSupply()).to.equal(mintAmount1 + mintAmount2);

            // Burn from user1
            await token.burn(user1.address, burnAmount);
            expect(await token.totalSupply()).to.equal(mintAmount1 + mintAmount2 - burnAmount);

            // Verify individual balances
            expect(await token.balanceOf(user1.address)).to.equal(mintAmount1 - burnAmount);
            expect(await token.balanceOf(user2.address)).to.equal(mintAmount2);
        });

        it("should handle listing status changes during transfers", async function () {
            // Mint tokens to users
            await token.mint(user1.address, MINT_AMOUNT);
            await token.mint(user2.address, MINT_AMOUNT);

            const transferAmount = ethers.parseUnits("100", TOKEN_DECIMALS);

            // Initially not listed - transfers between users should fail
            await expect(
                token.connect(user1).transfer(user2.address, transferAmount)
            ).to.be.revertedWithCustomError(token, "TokenTransfersBlocked");

            await token.setListed(true);

            await token.connect(user1).transfer(user2.address, transferAmount);
            expect(await token.balanceOf(user1.address)).to.equal(MINT_AMOUNT - transferAmount);
            expect(await token.balanceOf(user2.address)).to.equal(MINT_AMOUNT + transferAmount);

            await token.setListed(false);

            // Transfers between users should fail again
            await expect(
                token.connect(user2).transfer(user1.address, transferAmount)
            ).to.be.revertedWithCustomError(token, "TokenTransfersBlocked");
        });

        it("should handle maximum values correctly", async function () {
            const maxUint256 = ethers.MaxUint256;

            // Should be able to mint maximum amount (though impractical)
            await token.mint(user1.address, maxUint256);
            expect(await token.balanceOf(user1.address)).to.equal(maxUint256);
            expect(await token.totalSupply()).to.equal(maxUint256);

            // Should be able to burn the entire amount
            await token.burn(user1.address, maxUint256);
            expect(await token.balanceOf(user1.address)).to.equal(0);
            expect(await token.totalSupply()).to.equal(0);
        });

        it("should handle custom decimals correctly", async function () {
            // Deploy token with different decimals
            const customDecimals = 8;
            const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
            const customToken = await LaunchpadTokenFactory.deploy(
                "Custom Token",
                "CUSTOM",
                customDecimals,
                "https://custom.com"
            );
            await customToken.waitForDeployment();

            expect(await customToken.decimals()).to.equal(customDecimals);

            // Mint with custom decimals
            const customAmount = ethers.parseUnits("100", customDecimals);
            await customToken.mint(user1.address, customAmount);
            expect(await customToken.balanceOf(user1.address)).to.equal(customAmount);
        });
    });
});
