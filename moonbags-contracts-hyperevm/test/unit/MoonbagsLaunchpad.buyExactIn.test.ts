import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import {
    INonfungiblePositionManager,
    IWETH9,
    LaunchpadToken,
    MoonbagsLaunchpad,
    MoonbagsStake,
    TokenLock,
} from "../../types";
import { externalFixture } from "./external.fixtures";

describe("MoonbagsLaunchpad - buyExactIn", function () {
    let launchpad: MoonbagsLaunchpad;
    let tokenLock: TokenLock;
    let moonbagsStake: MoonbagsStake;
    let weth9: IWETH9;
    let nonfungiblePositionManager: INonfungiblePositionManager;
    let owner: Signer<PERSON>ithAddress;
    let creator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
    let buyer: Signer<PERSON>ithAddress;
    let treasury: SignerWithAddress;
    let platformToken: LaunchpadToken;
    let testToken: LaunchpadToken;
    let testTokenAddress: string;

    // Token parameters
    const tokenName = "Test Token";
    const tokenSymbol = "TEST";
    const tokenUri = "https://example.com/token";
    const tokenDescription = "A test token for bonding curve";
    const twitter = "https://twitter.com/test";
    const telegram = "https://t.me/test";
    const website = "https://test.com";
    const customThreshold = ethers.parseEther("0.03");

    // Constants from contract
    const DEFAULT_FEE_TIER = 3000;
    const POOL_CREATION_FEE = ethers.parseEther("0.001");
    const ONE_CHECKPOINT_TIMESTAMP = 1; // 1 second

    // Helper function to get sufficient amount for any token
    async function getSufficientAmount(
        tokenAddress: string,
        baseAmount = ethers.parseEther("0.01")
    ) {
        const estimatedCost = await launchpad.estimateBuyExactInCost(tokenAddress, baseAmount);
        return estimatedCost + ethers.parseEther("0.001"); // Add small buffer
    }

    beforeEach(async function () {
        [owner, creator, buyer, treasury] = await ethers.getSigners();

        const { weth9: _weth9, nonfungiblePositionManager: _nonfungiblePositionManager } =
            await externalFixture();
        weth9 = _weth9;
        nonfungiblePositionManager = _nonfungiblePositionManager;

        // Deploy platform token
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        platformToken = await LaunchpadTokenFactory.deploy(
            "Platform Token",
            "PLAT",
            18,
            "https://platform.com"
        );
        await platformToken.waitForDeployment();

        // Deploy MoonbagsStake
        const MoonbagsStakeFactory = await ethers.getContractFactory("MoonbagsStake");
        moonbagsStake = await MoonbagsStakeFactory.deploy();
        await moonbagsStake.waitForDeployment();
        await moonbagsStake.initialize();

        // Deploy TokenLock
        const TokenLockFactory = await ethers.getContractFactory("TokenLock");
        tokenLock = await TokenLockFactory.deploy();
        await tokenLock.waitForDeployment();
        await tokenLock.initialize();

        // Deploy MoonbagsLaunchpad
        const MoonbagsLaunchpadFactory = await ethers.getContractFactory("MoonbagsLaunchpad");
        launchpad = await MoonbagsLaunchpadFactory.deploy();
        await launchpad.waitForDeployment();

        // Initialize the launchpad
        await launchpad.initialize(
            await nonfungiblePositionManager.getAddress(),
            await weth9.getAddress(),
            DEFAULT_FEE_TIER,
            await platformToken.getAddress(),
            await moonbagsStake.getAddress(),
            await tokenLock.getAddress()
        );

        await launchpad.updateFeeRecipients(treasury.address, treasury.address);

        // Create a test token for buying
        const tx = await launchpad
            .connect(creator)
            .createPool(
                tokenName,
                tokenSymbol,
                tokenUri,
                tokenDescription,
                twitter,
                telegram,
                website,
                customThreshold,
                { value: POOL_CREATION_FEE }
            );

        const receipt = await tx.wait();
        const tokenCreatedEvent = receipt?.logs.find((log) => {
            try {
                const parsed = launchpad.interface.parseLog({
                    topics: log.topics as string[],
                    data: log.data,
                });
                return parsed?.name === "TokenCreated";
            } catch {
                return false;
            }
        });

        const parsedEvent = launchpad.interface.parseLog({
            topics: tokenCreatedEvent!.topics as string[],
            data: tokenCreatedEvent!.data,
        });

        testTokenAddress = parsedEvent?.args[0];
        testToken = await ethers.getContractAt("LaunchpadToken", testTokenAddress);
    });

    describe("buyExactIn", function () {
        const amountIn = ethers.parseEther("0.011"); // Sufficient amount based on bonding curve calculation
        const amountOutMin = 0; // No minimum to avoid bonding curve issues

        it("should buy tokens with exact ETH amount", async function () {
            const initialBuyerBalance = await ethers.provider.getBalance(buyer.address);
            const launchpadBalanceBefore = await ethers.provider.getBalance(
                await launchpad.getAddress()
            );

            // Use sufficient amount based on bonding curve
            const sufficientAmount = ethers.parseEther("0.012");
            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(testTokenAddress, amountIn, amountOutMin, { value: sufficientAmount });

            const receipt = await tx.wait();
            const gasUsed = receipt!.gasUsed * receipt!.gasPrice;

            // Check Trade event was emitted
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            expect(tradeEvent).to.not.be.undefined;

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            // Verify trade event parameters
            expect(parsedTradeEvent?.args[0]).to.equal(testTokenAddress); // token
            expect(parsedTradeEvent?.args[1]).to.equal(buyer.address); // user
            expect(parsedTradeEvent?.args[2]).to.be.true; // isBuy
            expect(parsedTradeEvent?.args[3]).to.be.gt(0); // hypeAmount
            expect(parsedTradeEvent?.args[4]).to.be.gt(0); // tokenAmount
            expect(parsedTradeEvent?.args[9]).to.be.gt(0); // fee

            const tokenAmountOut = parsedTradeEvent?.args[4];
            const fee = parsedTradeEvent?.args[9];

            // Verify buyer received tokens
            const buyerTokenBalance = await testToken.balanceOf(buyer.address);
            expect(buyerTokenBalance).to.equal(tokenAmountOut);

            // Verify ETH was spent correctly (allowing for refunds and fees)
            const finalBuyerBalance = await ethers.provider.getBalance(buyer.address);
            const actualSpent = initialBuyerBalance - finalBuyerBalance;
            expect(actualSpent).to.be.lte(sufficientAmount + gasUsed);
            expect(actualSpent).to.be.gte(gasUsed);

            // Verify launchpad balance increased
            const launchpadBalanceAfter = await ethers.provider.getBalance(
                await launchpad.getAddress()
            );
            expect(launchpadBalanceAfter - launchpadBalanceBefore).to.equal(fee + amountIn);
        });

        it("should handle checkpoint timestamp logic correctly", async function () {
            // Create a fresh token
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Checkpoint Token",
                    "CHECK",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            const sufficientAmountIn = await getSufficientAmount(freshTokenAddress);
            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(freshTokenAddress, ethers.parseEther("0.01"), amountOutMin, {
                    value: sufficientAmountIn,
                });

            const receipt = await tx.wait();

            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            const tokenAmountOut = parsedTradeEvent?.args[4];

            // Check if tokens were locked or sent directly
            const buyerTokenBalance = await freshToken.balanceOf(buyer.address);
            const lockBalance = await freshToken.balanceOf(await tokenLock.getAddress());
            const lockCount = await tokenLock.getLockCount();

            // Either tokens are locked OR sent directly, but not both
            if (lockCount > 0) {
                // Tokens were locked (within checkpoint)
                expect(buyerTokenBalance).to.equal(0); // No direct tokens
                expect(lockBalance).to.equal(tokenAmountOut); // Tokens in lock contract

                const lockDetails = await tokenLock.getLock(lockCount);
                expect(lockDetails.token).to.equal(freshTokenAddress);
                expect(lockDetails.recipient).to.equal(buyer.address);
                expect(lockDetails.amount).to.equal(tokenAmountOut);
                expect(lockDetails.closed).to.be.false;
                expect(lockDetails.endTime - lockDetails.startTime).to.equal(3600); // 1 hour lock
            } else {
                // Tokens were sent directly (after checkpoint)
                expect(buyerTokenBalance).to.equal(tokenAmountOut); // Direct tokens
                expect(lockBalance).to.equal(0); // No tokens in lock contract
            }

            expect(tokenAmountOut).to.be.gt(0);
        });

        it("should send tokens directly when bought after checkpoint timestamp", async function () {
            // Wait for checkpoint timestamp to pass
            await ethers.provider.send("evm_increaseTime", [ONE_CHECKPOINT_TIMESTAMP + 1]);
            await ethers.provider.send("evm_mine", []);

            const sufficientAmount = await getSufficientAmount(testTokenAddress);
            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(testTokenAddress, ethers.parseEther("0.01"), amountOutMin, {
                    value: sufficientAmount,
                });

            const receipt = await tx.wait();

            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            const tokenAmountOut = parsedTradeEvent?.args[4];

            // Verify tokens were sent directly to buyer
            const buyerTokenBalance = await testToken.balanceOf(buyer.address);
            expect(buyerTokenBalance).to.equal(tokenAmountOut);

            // Verify no tokens are in the lock contract
            const lockBalance = await testToken.balanceOf(await tokenLock.getAddress());
            expect(lockBalance).to.equal(0);

            // Verify no new locks were created
            const lockCount = await tokenLock.getLockCount();
            expect(lockCount).to.equal(0);
        });

        it("should refund excess ETH", async function () {
            const excessAmount = ethers.parseEther("0.01");
            const sufficientAmount = ethers.parseEther("0.012"); // Sufficient for bonding curve
            const totalSent = sufficientAmount + excessAmount;

            const initialBalance = await ethers.provider.getBalance(buyer.address);

            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(testTokenAddress, amountIn, amountOutMin, { value: totalSent });

            const receipt = await tx.wait();
            const gasUsed = receipt!.gasUsed * receipt!.gasPrice;
            const finalBalance = await ethers.provider.getBalance(buyer.address);

            // Should have received refund minus gas costs (allowing for refunds and fees)
            const actualSpent = initialBalance - finalBalance;
            expect(actualSpent).to.be.lte(sufficientAmount + gasUsed);
            expect(actualSpent).to.be.gte(gasUsed);
        });

        it("should revert with non-existent token", async function () {
            const fakeTokenAddress = ethers.Wallet.createRandom().address;

            await expect(
                launchpad
                    .connect(buyer)
                    .buyExactIn(fakeTokenAddress, amountIn, amountOutMin, { value: amountIn })
            ).to.be.revertedWithCustomError(launchpad, "TokenNotExists");
        });

        it("should revert with zero amountIn", async function () {
            await expect(
                launchpad
                    .connect(buyer)
                    .buyExactIn(testTokenAddress, 0, amountOutMin, { value: amountIn })
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with insufficient ETH sent", async function () {
            const insufficientValue = amountIn - 1n;

            await expect(
                launchpad.connect(buyer).buyExactIn(testTokenAddress, amountIn, amountOutMin, {
                    value: insufficientValue,
                })
            ).to.be.revertedWithCustomError(launchpad, "InsufficientAmount");
        });

        it("should revert when amountOut is less than amountOutMin", async function () {
            const highAmountOutMin = ethers.parseEther("1000000"); // Very high minimum

            await expect(
                launchpad
                    .connect(buyer)
                    .buyExactIn(testTokenAddress, amountIn, highAmountOutMin, { value: amountIn })
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should handle bonding curve math correctly", async function () {
            const testAmount = ethers.parseEther("0.01");
            const sufficientAmount = await getSufficientAmount(testTokenAddress, testAmount);

            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(testTokenAddress, testAmount, amountOutMin, {
                    value: sufficientAmount,
                });

            await tx.wait();

            // Get pool state after the buy
            const pool = await launchpad.getPool(testTokenAddress);
            expect(pool.realHypeReserves).to.be.gt(0); // Should have received ETH
            expect(pool.virtualTokenReserves).to.be.gt(0); // Should have virtual token reserves
            expect(pool.feeRecipient).to.be.gt(0); // Should have accumulated fees

            // Verify pool state changes
            expect(pool.realHypeReserves).to.be.gt(0);
            expect(pool.virtualHypeReserves).to.be.gt(0);
        });

        it("should handle pool state changes correctly", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Pool Test Token",
                    "POOL",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];

            // Use sufficient amount for bonding curve
            const sufficientAmount = await getSufficientAmount(freshTokenAddress);
            const lowAmountOutMin = 0; // No minimum

            const poolBefore = await launchpad.getPool(freshTokenAddress);
            expect(poolBefore.isCompleted).to.be.false;
            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(freshTokenAddress, ethers.parseEther("0.01"), lowAmountOutMin, {
                    value: sufficientAmount,
                });

            await tx.wait();

            // Check pool state after buy
            const poolAfter = await launchpad.getPool(freshTokenAddress);

            // Pool state should have changed (may or may not be completed depending on amount)
            expect(poolAfter.realHypeReserves).to.be.gt(poolBefore.realHypeReserves);
            expect(poolAfter.feeRecipient).to.be.gt(poolBefore.feeRecipient);

            // If pool is completed, that's also a valid outcome
            if (poolAfter.isCompleted) {
                expect(poolAfter.isCompleted).to.be.true;
            }
        });

        it("should handle multiple sequential buys correctly", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Sequential Token",
                    "SEQ",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];

            const sufficientAmountIn = await getSufficientAmount(freshTokenAddress);
            const smallAmountOutMin = 0; // No minimum to avoid bonding curve issues

            // First buy
            const tx1 = await launchpad
                .connect(buyer)
                .buyExactIn(freshTokenAddress, ethers.parseEther("0.01"), smallAmountOutMin, {
                    value: sufficientAmountIn,
                });

            const receipt1 = await tx1.wait();
            const tradeEvent1 = receipt1?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent1 = launchpad.interface.parseLog({
                topics: tradeEvent1!.topics as string[],
                data: tradeEvent1!.data,
            });

            const firstBuyTokens = parsedTradeEvent1?.args[4];

            // Check if pool is still active before second buy
            const poolAfterFirst = await launchpad.getPool(freshTokenAddress);

            if (!poolAfterFirst.isCompleted) {
                // Second buy by different user (only if pool not completed)
                const secondSufficientAmount = await getSufficientAmount(freshTokenAddress);
                const tx2 = await launchpad
                    .connect(creator)
                    .buyExactIn(freshTokenAddress, ethers.parseEther("0.01"), smallAmountOutMin, {
                        value: secondSufficientAmount,
                    });

                const receipt2 = await tx2.wait();
                const tradeEvent2 = receipt2?.logs.find((log) => {
                    try {
                        const parsed = launchpad.interface.parseLog({
                            topics: log.topics as string[],
                            data: log.data,
                        });
                        return parsed?.name === "Trade";
                    } catch {
                        return false;
                    }
                });

                const parsedTradeEvent2 = launchpad.interface.parseLog({
                    topics: tradeEvent2!.topics as string[],
                    data: tradeEvent2!.data,
                });

                const secondBuyTokens = parsedTradeEvent2?.args[4];

                // Verify both users received tokens
                expect(firstBuyTokens).to.be.gt(0);
                expect(secondBuyTokens).to.be.gt(0);

                // Due to bonding curve, second buy should get fewer tokens for same ETH
                expect(secondBuyTokens).to.be.lt(firstBuyTokens);
            } else {
                // Pool was completed by first buy, which is also a valid scenario
                expect(firstBuyTokens).to.be.gt(0);
                expect(poolAfterFirst.isCompleted).to.be.true;
            }
        });

        it("should revert when pool is already completed", async function () {
            // First, try to complete the pool by buying all available tokens
            const largeAmountIn = ethers.parseEther("100");
            const lowAmountOutMin = ethers.parseEther("0");

            try {
                await launchpad
                    .connect(buyer)
                    .buyExactIn(testTokenAddress, largeAmountIn, lowAmountOutMin, {
                        value: largeAmountIn,
                    });
            } catch (error) {
                console.error("Error completing pool:", error);
            }

            const pool = await launchpad.getPool(testTokenAddress);
            console.log("Pool completed status:", pool);

            await expect(
                launchpad
                    .connect(creator)
                    .buyExactIn(testTokenAddress, amountIn, amountOutMin, { value: amountIn })
            ).to.be.revertedWithCustomError(launchpad, "PoolAlreadyCompleted");
        });

        it("should handle exact amount calculations correctly", async function () {
            const exactAmountIn = ethers.parseEther("0.01");
            const sufficientAmount = await getSufficientAmount(testTokenAddress, exactAmountIn);
            const minAmountOut = 0; // No minimum to avoid bonding curve issues

            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(testTokenAddress, exactAmountIn, minAmountOut, {
                    value: sufficientAmount,
                });

            const receipt = await tx.wait();

            // Verify exact amount was used (no refund should occur)
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            const hypeAmountUsed = parsedTradeEvent?.args[3];
            const fee = parsedTradeEvent?.args[9];
            const totalUsed = hypeAmountUsed + fee;

            // Total used should be exactly the amount sent
            expect(totalUsed).to.be.lte(sufficientAmount);
        });

        it("should emit correct event parameters", async function () {
            const sufficientAmount = ethers.parseEther("0.012"); // Sufficient for bonding curve
            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(testTokenAddress, amountIn, amountOutMin, { value: sufficientAmount });

            const receipt = await tx.wait();

            // Check Trade event
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            expect(tradeEvent).to.not.be.undefined;

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            // Verify all event parameters
            expect(parsedTradeEvent?.args[0]).to.equal(testTokenAddress); // token
            expect(parsedTradeEvent?.args[1]).to.equal(buyer.address); // user
            expect(parsedTradeEvent?.args[2]).to.be.true; // isBuy
            expect(parsedTradeEvent?.args[3]).to.be.gt(0); // hypeAmount
            expect(parsedTradeEvent?.args[4]).to.be.gt(0); // tokenAmount
            expect(parsedTradeEvent?.args[5]).to.be.gt(0); // virtualHypeReserves
            expect(parsedTradeEvent?.args[6]).to.be.gt(0); // virtualTokenReserves
            expect(parsedTradeEvent?.args[7]).to.be.gt(0); // realHypeReserves
            expect(parsedTradeEvent?.args[8]).to.be.gte(0); // realTokenReserves
            expect(parsedTradeEvent?.args[9]).to.be.gt(0); // fee
            expect(parsedTradeEvent?.args[10]).to.be.gt(0); // timestamp
        });
    });

    describe("Integration Tests", function () {
        const integrationAmountIn = ethers.parseEther("0.012"); // Sufficient for bonding curve
        const integrationAmountOutMin = 0; // No minimum to avoid bonding curve issues

        it("should work with token lock withdrawal after lock expires", async function () {
            // Get current timestamp and set a specific time for token creation
            const currentBlock = await ethers.provider.getBlock("latest");
            const currentTimestamp = currentBlock!.timestamp;
            const targetTimestamp = currentTimestamp + 200; // 200 seconds in future

            await ethers.provider.send("evm_setNextBlockTimestamp", [targetTimestamp]);

            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Lock Test Token",
                    "LOCK",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens within checkpoint (they get locked) - let timestamp naturally increment
            const sufficientAmount = await getSufficientAmount(
                freshTokenAddress,
                integrationAmountIn
            );

            const tx = await launchpad
                .connect(buyer)
                .buyExactIn(freshTokenAddress, integrationAmountIn, integrationAmountOutMin, {
                    value: sufficientAmount,
                });

            const receipt = await tx.wait();
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            const tokenAmountOut = parsedTradeEvent?.args[4];

            // Check if tokens were locked or sent directly
            const lockCount = await tokenLock.getLockCount();
            const buyerDirectBalance = await freshToken.balanceOf(buyer.address);

            if (lockCount > 0) {
                // Tokens were locked - test withdrawal
                const lockDetails = await tokenLock.getLock(lockCount);
                expect(lockDetails.amount).to.equal(tokenAmountOut);

                // Fast forward time to after lock expiry
                await ethers.provider.send("evm_increaseTime", [3600 + 1]); // 1 hour + 1 second
                await ethers.provider.send("evm_mine", []);

                // Withdraw tokens
                const initialBalance = await freshToken.balanceOf(buyer.address);
                await tokenLock.connect(buyer).withdraw(lockCount);
                const finalBalance = await freshToken.balanceOf(buyer.address);

                // Verify tokens were transferred to buyer
                expect(finalBalance - initialBalance).to.equal(tokenAmountOut);
            } else {
                // Tokens were sent directly (checkpoint passed) - verify buyer has them
                expect(buyerDirectBalance).to.equal(tokenAmountOut);
                expect(buyerDirectBalance).to.be.gt(0);
            }
        });

        it("should handle fee accumulation correctly", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Fee Test Token",
                    "FEE",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];

            // Make a buy to accumulate fees
            const sufficientAmount = await getSufficientAmount(
                freshTokenAddress,
                integrationAmountIn
            );
            await launchpad
                .connect(buyer)
                .buyExactIn(freshTokenAddress, integrationAmountIn, integrationAmountOutMin, {
                    value: sufficientAmount,
                });

            const pool = await launchpad.getPool(freshTokenAddress);
            const accumulatedFees = pool.feeRecipient;
            expect(accumulatedFees).to.be.gt(0);
        });
    });
});
