import { Signer<PERSON>ithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import { LaunchpadToken, MoonbagsStake } from "../../types";

describe("MoonbagsStake", function () {
    let moonbagsStake: MoonbagsStake;
    let stakingToken: LaunchpadToken;
    let anotherToken: LaunchpadToken;
    let owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
    let admin: Signer<PERSON>ithAddress;
    let creator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
    let staker1: Signer<PERSON>ithAddress;
    let staker2: Signer<PERSON>ithAddress;
    let user: Signer<PERSON>ithAddress;

    // Constants
    const DEFAULT_DENY_UNSTAKE_DURATION = 3600; // 1 hour
    const MULTIPLIER = ethers.parseEther("1"); // 1e16
    const STAKE_AMOUNT = ethers.parseEther("100"); // 100 tokens
    const REWARD_AMOUNT = ethers.parseEther("1"); // 1 ETH

    beforeEach(async function () {
        [owner, admin, creator, staker1, staker2, user] = await ethers.getSigners();

        // Deploy MoonbagsStake
        const MoonbagsStakeFactory = await ethers.getContractFactory("MoonbagsStake");
        moonbagsStake = await MoonbagsStakeFactory.deploy();
        await moonbagsStake.waitForDeployment();

        // Initialize the contract
        await moonbagsStake.initialize();

        // Deploy test tokens
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        stakingToken = await LaunchpadTokenFactory.deploy(
            "Staking Token",
            "STAKE",
            18,
            "https://example.com/stake"
        );
        await stakingToken.waitForDeployment();

        anotherToken = await LaunchpadTokenFactory.deploy(
            "Another Token",
            "ANOTHER",
            18,
            "https://example.com/another"
        );
        await anotherToken.waitForDeployment();

        // Enable transfers for tokens (LaunchpadToken has transfer restrictions by default)
        await stakingToken.setListed(true);
        await anotherToken.setListed(true);

        // Mint tokens to stakers
        await stakingToken.mint(staker1.address, ethers.parseEther("1000"));
        await stakingToken.mint(staker2.address, ethers.parseEther("1000"));
        await anotherToken.mint(staker1.address, ethers.parseEther("1000"));

        // Approve staking contract to spend tokens
        await stakingToken
            .connect(staker1)
            .approve(await moonbagsStake.getAddress(), ethers.parseEther("1000"));
        await stakingToken
            .connect(staker2)
            .approve(await moonbagsStake.getAddress(), ethers.parseEther("1000"));
        await anotherToken
            .connect(staker1)
            .approve(await moonbagsStake.getAddress(), ethers.parseEther("1000"));
    });

    describe("Initialization", function () {
        it("should initialize with correct default values", async function () {
            const config = await moonbagsStake.config();
            expect(config.admin).to.equal(owner.address);
            expect(config.denyUnstakeDuration).to.equal(DEFAULT_DENY_UNSTAKE_DURATION);
        });

        it("should set owner correctly", async function () {
            expect(await moonbagsStake.owner()).to.equal(owner.address);
        });

        it("should not allow double initialization", async function () {
            await expect(moonbagsStake.initialize()).to.be.revertedWithCustomError(
                moonbagsStake,
                "InvalidInitialization"
            );
        });
    });

    describe("Staking Pool Management", function () {
        describe("initializeStakingPool", function () {
            it("should initialize a new staking pool", async function () {
                const tx = await moonbagsStake.initializeStakingPool(
                    await stakingToken.getAddress()
                );

                // Check that pool exists
                expect(await moonbagsStake.stakingPoolExists(await stakingToken.getAddress())).to.be
                    .true;

                // Check pool initial state
                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalSupply).to.equal(0);
                expect(poolInfo.rewardIndex).to.equal(0);
                expect(poolInfo.pendingInitialRewards).to.equal(0);
                expect(poolInfo.totalRewards).to.equal(0);

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "InitializeStakingPoolEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        owner.address,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should not revert when initializing existing pool", async function () {
                await moonbagsStake.initializeStakingPool(await stakingToken.getAddress());

                // Should not revert on second initialization
                await expect(moonbagsStake.initializeStakingPool(await stakingToken.getAddress()))
                    .to.not.be.reverted;
            });

            it("should revert with zero address", async function () {
                await expect(
                    moonbagsStake.initializeStakingPool(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(moonbagsStake, "InvalidTokenAddress");
            });
        });

        describe("initializeCreatorPool", function () {
            it("should initialize a new creator pool", async function () {
                const tx = await moonbagsStake.initializeCreatorPool(
                    await stakingToken.getAddress(),
                    creator.address
                );

                // Check that pool exists
                expect(await moonbagsStake.creatorPoolExists(await stakingToken.getAddress())).to.be
                    .true;

                // Check pool initial state
                const poolInfo = await moonbagsStake.getCreatorPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.creator).to.equal(creator.address);
                expect(poolInfo.totalRewards).to.equal(0);

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "InitializeCreatorPoolEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        owner.address,
                        creator.address,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should not revert when initializing existing creator pool", async function () {
                await moonbagsStake.initializeCreatorPool(
                    await stakingToken.getAddress(),
                    creator.address
                );

                // Should not revert on second initialization
                await expect(
                    moonbagsStake.initializeCreatorPool(
                        await stakingToken.getAddress(),
                        creator.address
                    )
                ).to.not.be.reverted;
            });

            it("should revert with zero token address", async function () {
                await expect(
                    moonbagsStake.initializeCreatorPool(ethers.ZeroAddress, creator.address)
                ).to.be.revertedWithCustomError(moonbagsStake, "InvalidTokenAddress");
            });

            it("should revert with zero creator address", async function () {
                await expect(
                    moonbagsStake.initializeCreatorPool(
                        await stakingToken.getAddress(),
                        ethers.ZeroAddress
                    )
                ).to.be.revertedWithCustomError(moonbagsStake, "ZeroAddress");
            });
        });
    });

    describe("Reward Management", function () {
        beforeEach(async function () {
            await moonbagsStake.initializeStakingPool(await stakingToken.getAddress());
        });

        describe("updateRewardIndex", function () {
            it("should add rewards to pending when no stakers", async function () {
                const tx = await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.pendingInitialRewards).to.equal(REWARD_AMOUNT);
                expect(poolInfo.totalRewards).to.equal(REWARD_AMOUNT);
                expect(poolInfo.rewardIndex).to.equal(0);

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "UpdateRewardIndexEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        owner.address,
                        REWARD_AMOUNT,
                        true,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should update reward index when stakers exist", async function () {
                // First stake some tokens
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                const tx = await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                const expectedRewardIndex = (REWARD_AMOUNT * MULTIPLIER) / STAKE_AMOUNT;
                expect(poolInfo.rewardIndex).to.equal(expectedRewardIndex);
                expect(poolInfo.totalRewards).to.equal(REWARD_AMOUNT);

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "UpdateRewardIndexEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        owner.address,
                        REWARD_AMOUNT,
                        false,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should revert with non-existent pool", async function () {
                await expect(
                    moonbagsStake.updateRewardIndex(await anotherToken.getAddress(), {
                        value: REWARD_AMOUNT,
                    })
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingPoolNotExist");
            });

            it("should revert with zero amount", async function () {
                await expect(
                    moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), { value: 0 })
                ).to.be.revertedWithCustomError(moonbagsStake, "InvalidAmount");
            });
        });

        describe("depositCreatorPool", function () {
            beforeEach(async function () {
                await moonbagsStake.initializeCreatorPool(
                    await stakingToken.getAddress(),
                    creator.address
                );
            });

            it("should deposit rewards to creator pool", async function () {
                const tx = await moonbagsStake.depositCreatorPool(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                const poolInfo = await moonbagsStake.getCreatorPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalRewards).to.equal(REWARD_AMOUNT);

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "DepositPoolCreatorEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        owner.address,
                        REWARD_AMOUNT,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should revert with non-existent creator pool", async function () {
                await expect(
                    moonbagsStake.depositCreatorPool(await anotherToken.getAddress(), {
                        value: REWARD_AMOUNT,
                    })
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingCreatorNotExist");
            });

            it("should revert with zero amount", async function () {
                await expect(
                    moonbagsStake.depositCreatorPool(await stakingToken.getAddress(), { value: 0 })
                ).to.be.revertedWithCustomError(moonbagsStake, "InvalidAmount");
            });
        });
    });

    describe("Staking Operations", function () {
        beforeEach(async function () {
            await moonbagsStake.initializeStakingPool(await stakingToken.getAddress());
        });

        describe("stake", function () {
            it("should stake tokens successfully", async function () {
                const tx = await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Check staking account
                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    await stakingToken.getAddress(),
                    staker1.address
                );
                expect(accountInfo.balance).to.equal(STAKE_AMOUNT);
                expect(accountInfo.rewardIndex).to.equal(0);
                expect(accountInfo.earned).to.equal(0);
                expect(accountInfo.unstakeDeadline).to.be.gt(0);

                // Check pool state
                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalSupply).to.equal(STAKE_AMOUNT);

                // Check token transfer
                expect(await stakingToken.balanceOf(staker1.address)).to.equal(
                    ethers.parseEther("900")
                );
                expect(await stakingToken.balanceOf(await moonbagsStake.getAddress())).to.equal(
                    STAKE_AMOUNT
                );

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "StakeEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        staker1.address,
                        STAKE_AMOUNT,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should give first staker pending initial rewards", async function () {
                // Add rewards before any stakers
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // First staker should get pending initial rewards
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    await stakingToken.getAddress(),
                    staker1.address
                );
                expect(accountInfo.earned).to.equal(REWARD_AMOUNT);

                // Pool should have no pending initial rewards after first staker
                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.pendingInitialRewards).to.equal(0);
            });

            it("should update rewards when staking additional tokens", async function () {
                // Initial stake
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Add rewards
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // Stake more tokens - should update rewards
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    await stakingToken.getAddress(),
                    staker1.address
                );
                expect(accountInfo.balance).to.equal(STAKE_AMOUNT * 2n);
                expect(accountInfo.earned).to.equal(REWARD_AMOUNT); // Should have earned the reward
            });

            it("should revert with non-existent pool", async function () {
                await expect(
                    moonbagsStake
                        .connect(staker1)
                        .stake(await anotherToken.getAddress(), STAKE_AMOUNT)
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingPoolNotExist");
            });

            it("should revert with zero amount", async function () {
                await expect(
                    moonbagsStake.connect(staker1).stake(await stakingToken.getAddress(), 0)
                ).to.be.revertedWithCustomError(moonbagsStake, "InvalidAmount");
            });

            it("should revert with insufficient token balance", async function () {
                const excessiveAmount = ethers.parseEther("2000");
                // Increase allowance to test balance check, not allowance check
                await stakingToken
                    .connect(staker1)
                    .approve(await moonbagsStake.getAddress(), excessiveAmount);

                await expect(
                    moonbagsStake
                        .connect(staker1)
                        .stake(await stakingToken.getAddress(), excessiveAmount)
                ).to.be.revertedWithCustomError(stakingToken, "ERC20InsufficientBalance");
            });

            it("should revert with insufficient allowance", async function () {
                // Reset allowance
                await stakingToken.connect(staker1).approve(await moonbagsStake.getAddress(), 0);

                await expect(
                    moonbagsStake
                        .connect(staker1)
                        .stake(await stakingToken.getAddress(), STAKE_AMOUNT)
                ).to.be.revertedWithCustomError(stakingToken, "ERC20InsufficientAllowance");
            });
        });

        describe("unstake", function () {
            beforeEach(async function () {
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);
            });

            it("should unstake tokens successfully after deadline", async function () {
                // Fast forward time past unstake deadline
                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);

                const unstakeAmount = STAKE_AMOUNT / 2n;
                const tx = await moonbagsStake
                    .connect(staker1)
                    .unstake(await stakingToken.getAddress(), unstakeAmount);

                // Check staking account
                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    await stakingToken.getAddress(),
                    staker1.address
                );
                expect(accountInfo.balance).to.equal(STAKE_AMOUNT - unstakeAmount);

                // Check pool state
                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalSupply).to.equal(STAKE_AMOUNT - unstakeAmount);

                // Check token transfer
                expect(await stakingToken.balanceOf(staker1.address)).to.equal(
                    ethers.parseEther("950")
                );

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "UnstakeEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        staker1.address,
                        unstakeAmount,
                        false,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should delete account when unstaking all tokens with no rewards", async function () {
                // Fast forward time past unstake deadline
                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);

                const tx = await moonbagsStake
                    .connect(staker1)
                    .unstake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Account should be deleted
                expect(
                    await moonbagsStake.stakingAccountExists(
                        await stakingToken.getAddress(),
                        staker1.address
                    )
                ).to.be.false;

                // Check event emission with account deletion flag
                await expect(tx)
                    .to.emit(moonbagsStake, "UnstakeEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        staker1.address,
                        STAKE_AMOUNT,
                        true,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should revert when unstaking before deadline", async function () {
                await expect(
                    moonbagsStake
                        .connect(staker1)
                        .unstake(await stakingToken.getAddress(), STAKE_AMOUNT)
                ).to.be.revertedWithCustomError(moonbagsStake, "UnstakeDeadlineNotAllow");
            });

            it("should revert with insufficient balance", async function () {
                // Fast forward time past unstake deadline
                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);

                const excessiveAmount = STAKE_AMOUNT + ethers.parseEther("1");
                await expect(
                    moonbagsStake
                        .connect(staker1)
                        .unstake(await stakingToken.getAddress(), excessiveAmount)
                ).to.be.revertedWithCustomError(moonbagsStake, "AccountBalanceNotEnough");
            });

            it("should revert with zero amount", async function () {
                await expect(
                    moonbagsStake.connect(staker1).unstake(await stakingToken.getAddress(), 0)
                ).to.be.revertedWithCustomError(moonbagsStake, "InvalidAmount");
            });

            it("should revert with non-existent pool", async function () {
                await expect(
                    moonbagsStake
                        .connect(staker1)
                        .unstake(await anotherToken.getAddress(), STAKE_AMOUNT)
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingPoolNotExist");
            });

            it("should revert with non-existent account", async function () {
                await expect(
                    moonbagsStake
                        .connect(staker2)
                        .unstake(await stakingToken.getAddress(), STAKE_AMOUNT)
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingAccountNotExist");
            });
        });
    });

    describe("Claiming Operations", function () {
        beforeEach(async function () {
            await moonbagsStake.initializeStakingPool(await stakingToken.getAddress());
            await moonbagsStake.initializeCreatorPool(
                await stakingToken.getAddress(),
                creator.address
            );
        });

        describe("claimStakingPool", function () {
            beforeEach(async function () {
                // Stake tokens and add rewards
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });
            });

            it("should claim staking rewards successfully", async function () {
                const initialBalance = await ethers.provider.getBalance(staker1.address);

                const tx = await moonbagsStake
                    .connect(staker1)
                    .claimStakingPool(await stakingToken.getAddress());
                const receipt = await tx.wait();
                const gasUsed = receipt!.gasUsed * receipt!.gasPrice;

                // Check account state
                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    await stakingToken.getAddress(),
                    staker1.address
                );
                expect(accountInfo.earned).to.equal(0);

                // Check pool state
                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalRewards).to.equal(0);

                // Check ETH transfer
                const finalBalance = await ethers.provider.getBalance(staker1.address);
                const expectedBalance = initialBalance + REWARD_AMOUNT - gasUsed;
                expect(finalBalance).to.be.closeTo(expectedBalance, ethers.parseEther("0.001"));

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "ClaimStakingPoolEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        staker1.address,
                        REWARD_AMOUNT,
                        false,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should delete account when claiming all rewards with no staked tokens", async function () {
                // Unstake all tokens first
                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);
                await moonbagsStake
                    .connect(staker1)
                    .unstake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Stake again to get rewards, then unstake
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);
                await moonbagsStake
                    .connect(staker1)
                    .unstake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Get the actual reward amount before claiming
                const actualRewardAmount = await moonbagsStake
                    .connect(staker1)
                    .calculateRewardsEarned(await stakingToken.getAddress());

                // Now claim rewards - account should be deleted
                const tx = await moonbagsStake
                    .connect(staker1)
                    .claimStakingPool(await stakingToken.getAddress());

                expect(
                    await moonbagsStake.stakingAccountExists(
                        await stakingToken.getAddress(),
                        staker1.address
                    )
                ).to.be.false;

                // Check event emission with account deletion flag
                await expect(tx)
                    .to.emit(moonbagsStake, "ClaimStakingPoolEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        staker1.address,
                        actualRewardAmount,
                        true,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should revert with no rewards to claim", async function () {
                // Claim once to clear rewards
                await moonbagsStake
                    .connect(staker1)
                    .claimStakingPool(await stakingToken.getAddress());

                // Try to claim again
                await expect(
                    moonbagsStake.connect(staker1).claimStakingPool(await stakingToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "RewardToClaimNotValid");
            });

            it("should revert with non-existent pool", async function () {
                await expect(
                    moonbagsStake.connect(staker1).claimStakingPool(await anotherToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingPoolNotExist");
            });

            it("should revert with non-existent account", async function () {
                await expect(
                    moonbagsStake.connect(staker2).claimStakingPool(await stakingToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingAccountNotExist");
            });
        });

        describe("claimCreatorPool", function () {
            beforeEach(async function () {
                await moonbagsStake.depositCreatorPool(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });
            });

            it("should claim creator rewards successfully", async function () {
                const initialBalance = await ethers.provider.getBalance(creator.address);

                const tx = await moonbagsStake
                    .connect(creator)
                    .claimCreatorPool(await stakingToken.getAddress());
                const receipt = await tx.wait();
                const gasUsed = receipt!.gasUsed * receipt!.gasPrice;

                // Check pool state
                const poolInfo = await moonbagsStake.getCreatorPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalRewards).to.equal(0);

                // Check ETH transfer
                const finalBalance = await ethers.provider.getBalance(creator.address);
                const expectedBalance = initialBalance + REWARD_AMOUNT - gasUsed;
                expect(finalBalance).to.be.closeTo(expectedBalance, ethers.parseEther("0.001"));

                // Check event emission
                await expect(tx)
                    .to.emit(moonbagsStake, "ClaimCreatorPoolEvent")
                    .withArgs(
                        await stakingToken.getAddress(),
                        creator.address,
                        REWARD_AMOUNT,
                        await ethers.provider.getBlock("latest").then((b) => b!.timestamp)
                    );
            });

            it("should revert with invalid creator", async function () {
                await expect(
                    moonbagsStake.connect(staker1).claimCreatorPool(await stakingToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "InvalidCreator");
            });

            it("should revert with no rewards to claim", async function () {
                // Claim once to clear rewards
                await moonbagsStake
                    .connect(creator)
                    .claimCreatorPool(await stakingToken.getAddress());

                // Try to claim again
                await expect(
                    moonbagsStake.connect(creator).claimCreatorPool(await stakingToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "RewardToClaimNotValid");
            });

            it("should revert with non-existent creator pool", async function () {
                await expect(
                    moonbagsStake.connect(creator).claimCreatorPool(await anotherToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingCreatorNotExist");
            });
        });
    });

    describe("View Functions", function () {
        beforeEach(async function () {
            await moonbagsStake.initializeStakingPool(await stakingToken.getAddress());
            await moonbagsStake
                .connect(staker1)
                .stake(await stakingToken.getAddress(), STAKE_AMOUNT);
            await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                value: REWARD_AMOUNT,
            });
        });

        describe("calculateRewardsEarned", function () {
            it("should calculate rewards correctly", async function () {
                const totalEarned = await moonbagsStake
                    .connect(staker1)
                    .calculateRewardsEarned(await stakingToken.getAddress());
                expect(totalEarned).to.equal(REWARD_AMOUNT);
            });

            it("should calculate updated rewards after additional rewards", async function () {
                // Add more rewards
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                const totalEarned = await moonbagsStake
                    .connect(staker1)
                    .calculateRewardsEarned(await stakingToken.getAddress());
                expect(totalEarned).to.equal(REWARD_AMOUNT * 2n);
            });

            it("should revert with non-existent pool", async function () {
                await expect(
                    moonbagsStake
                        .connect(staker1)
                        .calculateRewardsEarned(await anotherToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingPoolNotExist");
            });

            it("should revert with non-existent account", async function () {
                await expect(
                    moonbagsStake
                        .connect(staker2)
                        .calculateRewardsEarned(await stakingToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingAccountNotExist");
            });
        });

        describe("getStakingPoolInfo", function () {
            it("should return correct pool information", async function () {
                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalSupply).to.equal(STAKE_AMOUNT);
                expect(poolInfo.rewardIndex).to.be.gt(0);
                expect(poolInfo.pendingInitialRewards).to.equal(0);
                expect(poolInfo.totalRewards).to.equal(REWARD_AMOUNT);
            });

            it("should revert with non-existent pool", async function () {
                await expect(
                    moonbagsStake.getStakingPoolInfo(await anotherToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingPoolNotExist");
            });
        });

        describe("getCreatorPoolInfo", function () {
            beforeEach(async function () {
                await moonbagsStake.initializeCreatorPool(
                    await stakingToken.getAddress(),
                    creator.address
                );
                await moonbagsStake.depositCreatorPool(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });
            });

            it("should return correct creator pool information", async function () {
                const poolInfo = await moonbagsStake.getCreatorPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.creator).to.equal(creator.address);
                expect(poolInfo.totalRewards).to.equal(REWARD_AMOUNT);
            });

            it("should revert with non-existent creator pool", async function () {
                await expect(
                    moonbagsStake.getCreatorPoolInfo(await anotherToken.getAddress())
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingCreatorNotExist");
            });
        });

        describe("getStakingAccountInfo", function () {
            it("should return correct account information", async function () {
                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    await stakingToken.getAddress(),
                    staker1.address
                );
                expect(accountInfo.balance).to.equal(STAKE_AMOUNT);
                expect(accountInfo.rewardIndex).to.be.gte(0);
                expect(accountInfo.earned).to.be.gte(0);
                expect(accountInfo.unstakeDeadline).to.be.gt(0);
            });

            it("should revert with non-existent account", async function () {
                await expect(
                    moonbagsStake.getStakingAccountInfo(
                        await stakingToken.getAddress(),
                        staker2.address
                    )
                ).to.be.revertedWithCustomError(moonbagsStake, "StakingAccountNotExist");
            });
        });
    });

    describe("Admin Functions", function () {
        describe("updateConfig", function () {
            it("should update configuration successfully", async function () {
                const newAdmin = admin.address;
                const newDuration = 7200; // 2 hours

                await moonbagsStake.connect(owner).updateConfig(newAdmin, newDuration);

                const config = await moonbagsStake.config();
                expect(config.admin).to.equal(newAdmin);
                expect(config.denyUnstakeDuration).to.equal(newDuration);
            });

            it("should revert when called by non-admin", async function () {
                await expect(
                    moonbagsStake.connect(user).updateConfig(admin.address, 7200)
                ).to.be.revertedWithCustomError(moonbagsStake, "OnlyAdmin");
            });
        });
    });

    describe("Complex Integration Scenarios", function () {
        beforeEach(async function () {
            await moonbagsStake.initializeStakingPool(await stakingToken.getAddress());
            await moonbagsStake.initializeCreatorPool(
                await stakingToken.getAddress(),
                creator.address
            );
        });

        describe("Multiple Stakers Scenario", function () {
            it("should handle multiple stakers with proportional rewards", async function () {
                // Staker1 stakes 100 tokens
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Staker2 stakes 200 tokens
                const staker2Amount = STAKE_AMOUNT * 2n;
                await moonbagsStake
                    .connect(staker2)
                    .stake(await stakingToken.getAddress(), staker2Amount);

                // Add rewards - should be distributed proportionally
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // Calculate expected rewards (proportional to stake)
                const totalStaked = STAKE_AMOUNT + staker2Amount; // 300 tokens total
                const staker1ExpectedReward = (REWARD_AMOUNT * STAKE_AMOUNT) / totalStaked; // 1/3
                const staker2ExpectedReward = (REWARD_AMOUNT * staker2Amount) / totalStaked; // 2/3

                const staker1Earned = await moonbagsStake
                    .connect(staker1)
                    .calculateRewardsEarned(await stakingToken.getAddress());
                const staker2Earned = await moonbagsStake
                    .connect(staker2)
                    .calculateRewardsEarned(await stakingToken.getAddress());

                expect(staker1Earned).to.be.closeTo(
                    staker1ExpectedReward,
                    ethers.parseEther("0.001")
                );
                expect(staker2Earned).to.be.closeTo(
                    staker2ExpectedReward,
                    ethers.parseEther("0.001")
                );
            });

            it("should handle stakers joining at different times", async function () {
                // Staker1 stakes first
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Add first round of rewards
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // Staker2 joins later
                await moonbagsStake
                    .connect(staker2)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Add second round of rewards
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // Staker1 should have: full first reward + half of second reward
                const staker1Expected = REWARD_AMOUNT + REWARD_AMOUNT / 2n;
                // Staker2 should have: half of second reward only
                const staker2Expected = REWARD_AMOUNT / 2n;

                const staker1Earned = await moonbagsStake
                    .connect(staker1)
                    .calculateRewardsEarned(await stakingToken.getAddress());
                const staker2Earned = await moonbagsStake
                    .connect(staker2)
                    .calculateRewardsEarned(await stakingToken.getAddress());

                expect(staker1Earned).to.be.closeTo(staker1Expected, ethers.parseEther("0.001"));
                expect(staker2Earned).to.be.closeTo(staker2Expected, ethers.parseEther("0.001"));
            });
        });

        describe("Pending Initial Rewards Scenario", function () {
            it("should handle pending initial rewards correctly", async function () {
                // Add rewards before any stakers
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // Check pending initial rewards
                let poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.pendingInitialRewards).to.equal(REWARD_AMOUNT * 2n);

                // First staker should get all pending initial rewards
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                const accountInfo = await moonbagsStake.getStakingAccountInfo(
                    await stakingToken.getAddress(),
                    staker1.address
                );
                expect(accountInfo.earned).to.equal(REWARD_AMOUNT * 2n);

                // Pending initial rewards should be cleared
                poolInfo = await moonbagsStake.getStakingPoolInfo(await stakingToken.getAddress());
                expect(poolInfo.pendingInitialRewards).to.equal(0);
            });
        });

        describe("Complete Lifecycle Scenario", function () {
            it("should handle complete stake-reward-unstake-claim lifecycle", async function () {
                // 1. Stake tokens
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // 2. Add rewards
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // 3. Verify rewards are calculated correctly
                const earnedBeforeClaim = await moonbagsStake
                    .connect(staker1)
                    .calculateRewardsEarned(await stakingToken.getAddress());
                expect(earnedBeforeClaim).to.equal(REWARD_AMOUNT);

                // 4. Fast forward time and unstake
                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);
                await moonbagsStake
                    .connect(staker1)
                    .unstake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // 5. Claim rewards
                const initialBalance = await ethers.provider.getBalance(staker1.address);
                const claimTx = await moonbagsStake
                    .connect(staker1)
                    .claimStakingPool(await stakingToken.getAddress());
                const receipt = await claimTx.wait();
                const gasUsed = receipt!.gasUsed * receipt!.gasPrice;

                // 6. Verify final state
                const finalBalance = await ethers.provider.getBalance(staker1.address);
                expect(finalBalance).to.be.closeTo(
                    initialBalance + REWARD_AMOUNT - gasUsed,
                    ethers.parseEther("0.001")
                );

                // Account should be deleted
                expect(
                    await moonbagsStake.stakingAccountExists(
                        await stakingToken.getAddress(),
                        staker1.address
                    )
                ).to.be.false;

                // Pool should have no rewards left
                const poolInfo = await moonbagsStake.getStakingPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalRewards).to.equal(0);
                expect(poolInfo.totalSupply).to.equal(0);
            });
        });

        describe("Creator Pool Integration", function () {
            it("should handle creator pool deposits and claims", async function () {
                // Deposit rewards to creator pool
                await moonbagsStake.depositCreatorPool(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });
                await moonbagsStake.depositCreatorPool(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // Check creator pool state
                let poolInfo = await moonbagsStake.getCreatorPoolInfo(
                    await stakingToken.getAddress()
                );
                expect(poolInfo.totalRewards).to.equal(REWARD_AMOUNT * 2n);

                // Creator claims rewards
                const initialBalance = await ethers.provider.getBalance(creator.address);
                const claimTx = await moonbagsStake
                    .connect(creator)
                    .claimCreatorPool(await stakingToken.getAddress());
                const receipt = await claimTx.wait();
                const gasUsed = receipt!.gasUsed * receipt!.gasPrice;

                // Verify final state
                const finalBalance = await ethers.provider.getBalance(creator.address);
                expect(finalBalance).to.be.closeTo(
                    initialBalance + REWARD_AMOUNT * 2n - gasUsed,
                    ethers.parseEther("0.001")
                );

                // Creator pool should be empty
                poolInfo = await moonbagsStake.getCreatorPoolInfo(await stakingToken.getAddress());
                expect(poolInfo.totalRewards).to.equal(0);
            });
        });

        describe("Edge Cases", function () {
            it("should handle zero balance staking account cleanup", async function () {
                // Stake and immediately unstake after deadline
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);

                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);

                await moonbagsStake
                    .connect(staker1)
                    .unstake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Account should be deleted since no rewards and no balance
                expect(
                    await moonbagsStake.stakingAccountExists(
                        await stakingToken.getAddress(),
                        staker1.address
                    )
                ).to.be.false;
            });

            it("should handle reward calculation with zero balance", async function () {
                // Stake, add rewards, then unstake all
                await moonbagsStake
                    .connect(staker1)
                    .stake(await stakingToken.getAddress(), STAKE_AMOUNT);
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                await ethers.provider.send("evm_increaseTime", [DEFAULT_DENY_UNSTAKE_DURATION + 1]);
                await ethers.provider.send("evm_mine", []);
                await moonbagsStake
                    .connect(staker1)
                    .unstake(await stakingToken.getAddress(), STAKE_AMOUNT);

                // Add more rewards - should not affect staker with zero balance
                await moonbagsStake.updateRewardIndex(await stakingToken.getAddress(), {
                    value: REWARD_AMOUNT,
                });

                // Staker should still only have original reward
                const earned = await moonbagsStake
                    .connect(staker1)
                    .calculateRewardsEarned(await stakingToken.getAddress());
                expect(earned).to.equal(REWARD_AMOUNT);
            });
        });
    });
});
