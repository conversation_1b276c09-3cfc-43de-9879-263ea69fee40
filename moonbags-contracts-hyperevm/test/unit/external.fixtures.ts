import {
    abi as UniswapV3Factory<PERSON><PERSON>,
    bytecode as UniswapV3FactoryBytecode,
} from "@uniswap/v3-core/artifacts/contracts/UniswapV3Factory.sol/UniswapV3Factory.json";
import {
    abi as NonfungiblePositionManagerAbi,
    bytecode as NonfungiblePositionManagerBytecode,
} from "@uniswap/v3-periphery/artifacts/contracts/NonfungiblePositionManager.sol/NonfungiblePositionManager.json";
import {
    abi as NonfungibleTokenPositionDescriptorAbi,
    bytecode as NonfungibleTokenPositionDescriptorBytecode,
} from "@uniswap/v3-periphery/artifacts/contracts/NonfungibleTokenPositionDescriptor.sol/NonfungibleTokenPositionDescriptor.json";
import {
    abi as Swap<PERSON>outer<PERSON>bi,
    bytecode as SwapRouterBytecode,
} from "@uniswap/v3-periphery/artifacts/contracts/SwapRouter.sol/SwapRouter.json";
import {
    abi as NFTDescriptor<PERSON>bi,
    bytecode as NFTDescriptorBytecode,
} from "@uniswap/v3-periphery/artifacts/contracts/libraries/NFTDescriptor.sol/NFTDescriptor.json";
import { encodeBytes32String, getAddress } from "ethers";
import { ethers } from "hardhat";

import { INonfungiblePositionManager, ISwapRouter, IWETH9 } from "../../types";
import { abi as WETH9Abi, bytecode as WETH9Bytecode } from "../contracts/WETH9.json";

const linkLibraries = ({ bytecode, linkReferences }: any, libraries: any) => {
    Object.keys(linkReferences).forEach((fileName) => {
        Object.keys(linkReferences[fileName]).forEach((contractName) => {
            if (!libraries.hasOwnProperty(contractName)) {
                throw new Error(`Missing link library name ${contractName}`);
            }
            const address = getAddress(libraries[contractName]).toLowerCase().slice(2);
            linkReferences[fileName][contractName].forEach(
                ({ start, length }: { start: number; length: number }) => {
                    const start2 = 2 + start * 2;
                    const length2 = length * 2;
                    bytecode = bytecode
                        .slice(0, start2)
                        .concat(address)
                        .concat(bytecode.slice(start2 + length2, bytecode.length));
                }
            );
        });
    });
    return bytecode;
};

export async function externalFixture() {
    const [owner] = await ethers.getSigners();

    const WETH9 = new ethers.ContractFactory<[], IWETH9>(WETH9Abi, WETH9Bytecode, owner);
    const weth9 = (await WETH9.deploy()) as IWETH9;
    await weth9.waitForDeployment();

    const FACTORY = new ethers.ContractFactory(
        UniswapV3FactoryAbi,
        UniswapV3FactoryBytecode,
        owner
    );
    const factory = await FACTORY.deploy();
    await factory.waitForDeployment();

    const NFTDescriptor = new ethers.ContractFactory(
        NFTDescriptorAbi,
        NFTDescriptorBytecode,
        owner
    );
    const nftDescriptor = await NFTDescriptor.deploy();
    await nftDescriptor.waitForDeployment();

    const linkedBytecode = linkLibraries(
        {
            bytecode: NonfungibleTokenPositionDescriptorBytecode,
            linkReferences: {
                "NFTDescriptor.sol": {
                    NFTDescriptor: [
                        {
                            length: 20,
                            start: 1681,
                        },
                    ],
                },
            },
        },
        {
            NFTDescriptor: await nftDescriptor.getAddress(),
        }
    );

    const NonfungibleTokenPositionDescriptor = new ethers.ContractFactory(
        NonfungibleTokenPositionDescriptorAbi,
        linkedBytecode,
        owner
    );

    const nativeCurrencyLabelBytes = encodeBytes32String("WETH");
    const nonfungibleTokenPositionDescriptor = await NonfungibleTokenPositionDescriptor.deploy(
        await weth9.getAddress(),
        nativeCurrencyLabelBytes
    );

    await nonfungibleTokenPositionDescriptor.waitForDeployment();

    const NonfungiblePositionManager = new ethers.ContractFactory<
        [string, string, string],
        INonfungiblePositionManager
    >(NonfungiblePositionManagerAbi, NonfungiblePositionManagerBytecode, owner);

    const nonfungiblePositionManager = (await NonfungiblePositionManager.deploy(
        await factory.getAddress(),
        await weth9.getAddress(),
        await nonfungibleTokenPositionDescriptor.getAddress()
    )) as INonfungiblePositionManager;

    await nonfungiblePositionManager.waitForDeployment();

    const SwapRouter = new ethers.ContractFactory(SwapRouterAbi, SwapRouterBytecode, owner);
    const swapRouter = (await SwapRouter.deploy(
        await factory.getAddress(),
        await weth9.getAddress()
    )) as ISwapRouter;

    return {
        weth9,
        nonfungiblePositionManager,
        owner,
        swapRouter,
    };
}
