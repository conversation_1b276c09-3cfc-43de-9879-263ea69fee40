import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import {
    INonfungiblePositionManager,
    IWETH9,
    LaunchpadToken,
    MoonbagsLaunchpad,
    MoonbagsStake,
    TokenLock,
} from "../../types";
import { externalFixture } from "./external.fixtures";

describe("MoonbagsLaunchpad - createPool", function () {
    let launchpad: MoonbagsLaunchpad;
    let tokenLock: TokenLock;
    let moonbagsStake: MoonbagsStake;
    let weth9: IWETH9;
    let nonfungiblePositionManager: INonfungiblePositionManager;
    let owner: <PERSON><PERSON><PERSON><PERSON><PERSON>dd<PERSON>;
    let creator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
    let buyer: Signer<PERSON>ithAddress;
    let treasury: SignerWithAddress;
    let platformToken: LaunchpadToken;

    // Token parameters
    const tokenName = "Test Token";
    const tokenSymbol = "TEST";
    const tokenUri = "https://example.com/token";
    const tokenDescription = "A test token for bonding curve";
    const twitter = "https://twitter.com/test";
    const telegram = "https://t.me/test";
    const website = "https://test.com";
    const customThreshold = ethers.parseEther("0.03");

    // Constants from contract
    const DEFAULT_FEE_TIER = 3000;
    const POOL_CREATION_FEE = ethers.parseEther("0.001");
    const DEFAULT_THRESHOLD = ethers.parseEther("0.03");
    const MINIMUM_THRESHOLD = ethers.parseEther("0.02");

    beforeEach(async function () {
        [owner, creator, buyer, treasury] = await ethers.getSigners();

        const { weth9: _weth9, nonfungiblePositionManager: _nonfungiblePositionManager } =
            await externalFixture();
        weth9 = _weth9;
        nonfungiblePositionManager = _nonfungiblePositionManager;

        // Deploy platform token
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        platformToken = await LaunchpadTokenFactory.deploy(
            "Platform Token",
            "PLAT",
            18,
            "https://platform.com"
        );
        await platformToken.waitForDeployment();

        // Deploy MoonbagsStake
        const MoonbagsStakeFactory = await ethers.getContractFactory("MoonbagsStake");
        moonbagsStake = await MoonbagsStakeFactory.deploy();
        await moonbagsStake.waitForDeployment();
        await moonbagsStake.initialize();

        // Deploy TokenLock
        const TokenLockFactory = await ethers.getContractFactory("TokenLock");
        tokenLock = await TokenLockFactory.deploy();
        await tokenLock.waitForDeployment();
        await tokenLock.initialize();

        // Deploy MoonbagsLaunchpad
        const MoonbagsLaunchpadFactory = await ethers.getContractFactory("MoonbagsLaunchpad");
        launchpad = await MoonbagsLaunchpadFactory.deploy();
        await launchpad.waitForDeployment();

        // Initialize the launchpad
        await launchpad.initialize(
            await nonfungiblePositionManager.getAddress(),
            await weth9.getAddress(),
            DEFAULT_FEE_TIER,
            await platformToken.getAddress(),
            await moonbagsStake.getAddress(),
            await tokenLock.getAddress()
        );

        await launchpad.updateFeeRecipients(treasury.address, treasury.address);
    });

    describe("createPool", function () {
        it("should create a token with valid parameters", async function () {
            const tx = await launchpad
                .connect(creator)
                .createPool(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const receipt = await tx.wait();

            // Check TokenCreated event
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            expect(tokenCreatedEvent).to.not.be.undefined;

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const tokenAddress = parsedEvent?.args[0];
            expect(tokenAddress).to.not.equal(ethers.ZeroAddress);

            // Verify token is registered
            expect(await launchpad.isToken(tokenAddress)).to.be.true;

            // Verify pool is created with correct parameters
            const pool = await launchpad.getPool(tokenAddress);
            console.log("Pool created:", pool);
            expect(pool.threshold).to.equal(customThreshold);
            expect(pool.isCompleted).to.be.false;
            expect(pool.realHypeReserves).to.equal(0);
            expect(pool.realTokenReserves).to.be.gt(0);

            // Verify token contract properties
            const token = await ethers.getContractAt("LaunchpadToken", tokenAddress);
            expect(await token.name()).to.equal(tokenName);
            expect(await token.symbol()).to.equal(tokenSymbol);
            expect(await token.tokenURI()).to.equal(tokenUri);
            expect(await token.decimals()).to.equal(6); // DEFAULT_TOKEN_DECIMALS
            expect(await token.isListed()).to.be.false;
            expect(await token.owner()).to.equal(await launchpad.getAddress());
        });

        it("should use default threshold when customThreshold is 0", async function () {
            const tx = await launchpad.connect(creator).createPool(
                tokenName,
                tokenSymbol,
                tokenUri,
                tokenDescription,
                twitter,
                telegram,
                website,
                0, // Use default threshold
                { value: POOL_CREATION_FEE }
            );

            const receipt = await tx.wait();
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const tokenAddress = parsedEvent?.args[0];
            const pool = await launchpad.getPool(tokenAddress);
            expect(pool.threshold).to.equal(DEFAULT_THRESHOLD);
        });

        it("should transfer pool creation fee to platform recipient", async function () {
            const initialBalance = await ethers.provider.getBalance(treasury.address);

            await launchpad
                .connect(creator)
                .createPool(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const finalBalance = await ethers.provider.getBalance(treasury.address);
            expect(finalBalance - initialBalance).to.equal(POOL_CREATION_FEE);
        });

        it("should refund excess ETH", async function () {
            const excessAmount = ethers.parseEther("0.01");
            const totalSent = POOL_CREATION_FEE + excessAmount;

            const initialBalance = await ethers.provider.getBalance(creator.address);

            const tx = await launchpad
                .connect(creator)
                .createPool(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: totalSent }
                );

            const receipt = await tx.wait();
            const gasUsed = receipt!.gasUsed * receipt!.gasPrice;
            const finalBalance = await ethers.provider.getBalance(creator.address);

            // Should have received refund minus gas costs
            const expectedBalance = initialBalance - POOL_CREATION_FEE - gasUsed;
            expect(finalBalance).to.be.closeTo(expectedBalance, ethers.parseEther("0.0001"));
        });

        it("should revert with insufficient pool creation fee", async function () {
            await expect(
                launchpad
                    .connect(creator)
                    .createPool(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        { value: POOL_CREATION_FEE - 1n }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with empty name", async function () {
            await expect(
                launchpad
                    .connect(creator)
                    .createPool(
                        "",
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        { value: POOL_CREATION_FEE }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with empty symbol", async function () {
            await expect(
                launchpad
                    .connect(creator)
                    .createPool(
                        tokenName,
                        "",
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        { value: POOL_CREATION_FEE }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with threshold below minimum", async function () {
            const belowMinimum = MINIMUM_THRESHOLD - 1n;

            await expect(
                launchpad
                    .connect(creator)
                    .createPool(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        belowMinimum,
                        { value: POOL_CREATION_FEE }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with URI too long", async function () {
            const longUri = "a".repeat(301); // MAX_URI_LENGTH is 300

            await expect(
                launchpad
                    .connect(creator)
                    .createPool(
                        tokenName,
                        tokenSymbol,
                        longUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        { value: POOL_CREATION_FEE }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with description too long", async function () {
            const longDescription = "a".repeat(1001); // MAX_DESCRIPTION_LENGTH is 1000

            await expect(
                launchpad
                    .connect(creator)
                    .createPool(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        longDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        { value: POOL_CREATION_FEE }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with social links too long", async function () {
            const longSocial = "a".repeat(501); // MAX_SOCIAL_LENGTH is 500

            await expect(
                launchpad
                    .connect(creator)
                    .createPool(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        longSocial,
                        telegram,
                        website,
                        customThreshold,
                        { value: POOL_CREATION_FEE }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should initialize staking pools correctly", async function () {
            const tx = await launchpad
                .connect(creator)
                .createPool(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const receipt = await tx.wait();
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const tokenAddress = parsedEvent?.args[0];

            // Verify staking pools were initialized
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            expect(stakingPoolExists).to.be.true;

            const creatorPoolExists = await moonbagsStake.creatorPoolExists(tokenAddress);
            expect(creatorPoolExists).to.be.true;
        });
    });
});
