import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { time } from "@nomicfoundation/hardhat-network-helpers";
import { expect } from "chai";
import { ethers } from "hardhat";

import {
    INonfungiblePositionManager,
    ISwapRouter,
    IWETH9,
    LaunchpadToken,
    MoonbagsLaunchpad,
    MoonbagsStake,
    TokenLock,
} from "../../types";
import { externalFixture } from "./external.fixtures";

describe("MoonbagsLaunchpad - All Functions", function () {
    let launchpad: MoonbagsLaunchpad;
    let tokenLock: TokenLock;
    let moonbagsStake: MoonbagsStake;
    let weth9: IWETH9;
    let nonfungiblePositionManager: INonfungiblePositionManager;
    let swapRouter: ISwapRouter;
    let owner: SignerWithAddress;
    let creator: Signer<PERSON>ithAddress;
    let buyer: SignerWithAddress;
    let treasury: SignerWithAddress;
    let nonOwner: SignerWithAddress;
    let platformToken: LaunchpadToken;

    // Token parameters
    const tokenName = "Test Token";
    const tokenSymbol = "TEST";
    const tokenUri = "https://example.com/token";
    const tokenDescription = "A test token for bonding curve";
    const twitter = "https://twitter.com/test";
    const telegram = "https://t.me/test";
    const website = "https://test.com";
    const customThreshold = ethers.parseEther("0.03");

    // Constants from contract
    const DEFAULT_FEE_TIER = 3000;
    const POOL_CREATION_FEE = ethers.parseEther("0.001");

    let tokenAddress: string;
    let token: LaunchpadToken;

    beforeEach(async function () {
        [owner, creator, buyer, treasury, nonOwner] = await ethers.getSigners();

        const {
            weth9: _weth9,
            nonfungiblePositionManager: _nonfungiblePositionManager,
            swapRouter: _swapRouter,
        } = await externalFixture();
        weth9 = _weth9;
        nonfungiblePositionManager = _nonfungiblePositionManager;
        swapRouter = _swapRouter;

        // Deploy platform token
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        platformToken = await LaunchpadTokenFactory.deploy(
            "Platform Token",
            "PLAT",
            18,
            "https://platform.com"
        );
        await platformToken.waitForDeployment();

        // Deploy MoonbagsStake
        const MoonbagsStakeFactory = await ethers.getContractFactory("MoonbagsStake");
        moonbagsStake = await MoonbagsStakeFactory.deploy();
        await moonbagsStake.waitForDeployment();
        await moonbagsStake.initialize();

        await moonbagsStake.initializeStakingPool(await platformToken.getAddress());

        // Deploy TokenLock
        const TokenLockFactory = await ethers.getContractFactory("TokenLock");
        tokenLock = await TokenLockFactory.deploy();
        await tokenLock.waitForDeployment();
        await tokenLock.initialize();

        // Deploy MoonbagsLaunchpad
        const MoonbagsLaunchpadFactory = await ethers.getContractFactory("MoonbagsLaunchpad");
        launchpad = await MoonbagsLaunchpadFactory.deploy();
        await launchpad.waitForDeployment();

        // Initialize the launchpad
        await launchpad.initialize(
            await nonfungiblePositionManager.getAddress(),
            await weth9.getAddress(),
            DEFAULT_FEE_TIER,
            await platformToken.getAddress(),
            await moonbagsStake.getAddress(),
            await tokenLock.getAddress()
        );

        await launchpad.updateFeeRecipients(treasury.address, treasury.address);

        // Create a test token for testing
        const tx = await launchpad
            .connect(creator)
            .createPool(
                tokenName,
                tokenSymbol,
                tokenUri,
                tokenDescription,
                twitter,
                telegram,
                website,
                customThreshold,
                { value: POOL_CREATION_FEE }
            );

        const receipt = await tx.wait();
        const tokenCreatedEvent = receipt?.logs.find((log) => {
            try {
                const parsed = launchpad.interface.parseLog({
                    topics: log.topics as string[],
                    data: log.data,
                });
                return parsed?.name === "TokenCreated";
            } catch {
                return false;
            }
        });

        const parsedEvent = launchpad.interface.parseLog({
            topics: tokenCreatedEvent!.topics as string[],
            data: tokenCreatedEvent!.data,
        });

        tokenAddress = parsedEvent?.args[0];
        token = await ethers.getContractAt("LaunchpadToken", tokenAddress);
    });

    describe("View Functions", function () {
        it("should return correct pool information", async function () {
            const pool = await launchpad.getPool(tokenAddress);

            // Pool struct fields:
            // 0: realHypeReserves, 1: realTokenReserves, 2: virtualTokenReserves, 3: virtualHypeReserves
            // 4: remainTokenReserves, 5: virtualRemainTokenReserves, 6: feeRecipient, 7: isCompleted
            // 8: threshold, 9: platformFeeWithdraw, 10: creatorFeeWithdraw, 11: stakeFeeWithdraw
            // 12: platformStakeFeeWithdraw, 13: creationTimestamp, 14: feeDistributionUnlockTime

            expect(pool[7]).to.equal(false); // isCompleted
            expect(pool[8]).to.equal(customThreshold); // threshold
            expect(pool[3]).to.be.gt(0); // virtualHypeReserves
            expect(pool[2]).to.be.gt(0); // virtualTokenReserves
        });

        it("should calculate virtual reserves correctly", async function () {
            const initialVirtualHypeReserves =
                await launchpad.calculateInitialVirtualHypeReserves(customThreshold);
            const virtualRemainTokenReserves =
                await launchpad.calculateVirtualRemainTokenReserves();
            const actualVirtualTokenReserves =
                await launchpad.calculateActualVirtualTokenReserves();

            expect(initialVirtualHypeReserves).to.be.gt(0);
            expect(virtualRemainTokenReserves).to.be.gt(0);
            expect(actualVirtualTokenReserves).to.be.gt(0);
        });

        it("should estimate buy costs correctly", async function () {
            const amountIn = ethers.parseEther("0.1");
            const estimatedTokens = await launchpad.estimateBuyExactInTokens(
                tokenAddress,
                amountIn
            );
            const estimatedCost = await launchpad.estimateBuyExactInCost(
                tokenAddress,
                estimatedTokens
            );

            expect(estimatedTokens).to.be.gt(0);
            expect(estimatedCost).to.be.lte(amountIn);
        });

        it("should estimate sell returns correctly", async function () {
            // Create a fresh token for this test to avoid pool completion issues
            const tx = await launchpad
                .connect(creator)
                .createPool(
                    "Sell Test Token",
                    "SELL",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const receipt = await tx.wait();
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const sellTestTokenAddress = parsedEvent?.args[0];
            const sellTestToken = await ethers.getContractAt(
                "LaunchpadToken",
                sellTestTokenAddress
            );

            // Estimate the cost for a small token amount first
            const desiredTokenAmount = ethers.parseUnits("1000", 6); // 1000 tokens with 6 decimals
            const estimatedCost = await launchpad.estimateBuyExactInCost(
                sellTestTokenAddress,
                desiredTokenAmount
            );

            // Buy some tokens with the estimated cost
            await launchpad
                .connect(buyer)
                .buyExactIn(sellTestTokenAddress, desiredTokenAmount, 0, { value: estimatedCost });

            const buyerBalance = await sellTestToken.balanceOf(buyer.address);
            const sellAmount = buyerBalance / 2n;

            // Check pool state after buy
            const pool = await launchpad.getPool(sellTestTokenAddress);

            // Ensure we have tokens to sell
            expect(buyerBalance).to.be.gt(0); // Should have received tokens

            const estimatedHype = await launchpad.estimateSellTokens(
                sellTestTokenAddress,
                sellAmount
            );

            expect(estimatedHype).to.be.gt(0);
        });
    });

    describe("Admin Functions", function () {
        describe("updateConfiguration", function () {
            it("should update configuration successfully", async function () {
                const newPlatformFee = 200; // 2%
                const newInitialVirtualTokenReserves = ethers.parseEther("**********");
                const newRemainTokenReserves = ethers.parseEther("*********");
                const newTokenDecimals = 18;
                const newPlatformFeeWithdraw = 1500; // 15%
                const newCreatorFeeWithdraw = 3000; // 30%
                const newStakeFeeWithdraw = 3500; // 35%
                const newPlatformStakeFeeWithdraw = 2000; // 20%

                await expect(
                    launchpad
                        .connect(owner)
                        .updateConfiguration(
                            newPlatformFee,
                            newInitialVirtualTokenReserves,
                            newRemainTokenReserves,
                            newTokenDecimals,
                            newPlatformFeeWithdraw,
                            newCreatorFeeWithdraw,
                            newStakeFeeWithdraw,
                            newPlatformStakeFeeWithdraw,
                            await platformToken.getAddress()
                        )
                ).to.emit(launchpad, "ConfigurationUpdated");

                const config = await launchpad.config();
                expect(config.platformFee).to.equal(newPlatformFee);
                expect(config.initialVirtualTokenReserves).to.equal(newInitialVirtualTokenReserves);
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad
                        .connect(nonOwner)
                        .updateConfiguration(
                            200,
                            ethers.parseEther("**********"),
                            ethers.parseEther("*********"),
                            18,
                            1500,
                            3000,
                            3500,
                            2000,
                            await platformToken.getAddress()
                        )
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });

            it("should revert with invalid fee distribution", async function () {
                await expect(
                    launchpad.connect(owner).updateConfiguration(
                        200,
                        ethers.parseEther("**********"),
                        ethers.parseEther("*********"),
                        18,
                        5000,
                        5000,
                        5000,
                        5000,
                        await platformToken.getAddress() // Total > 100%
                    )
                ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
            });
        });

        describe("updateFeeRecipients", function () {
            it("should update fee recipients successfully", async function () {
                const newTreasury = nonOwner.address;
                const newFeePlatformRecipient = buyer.address;

                await launchpad
                    .connect(owner)
                    .updateFeeRecipients(newTreasury, newFeePlatformRecipient);

                const config = await launchpad.config();
                expect(config.treasury).to.equal(newTreasury);
                expect(config.feePlatformRecipient).to.equal(newFeePlatformRecipient);
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad.connect(nonOwner).updateFeeRecipients(nonOwner.address, buyer.address)
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });

        describe("setTokenLock", function () {
            it("should update TokenLock contract successfully", async function () {
                const newTokenLock = await ethers.deployContract("TokenLock");
                await newTokenLock.initialize();

                await expect(
                    launchpad.connect(owner).setTokenLock(await newTokenLock.getAddress())
                ).to.emit(launchpad, "TokenLockContractUpdated");

                expect(await launchpad.tokenLock()).to.equal(await newTokenLock.getAddress());
            });

            it("should revert with zero address", async function () {
                await expect(
                    launchpad.connect(owner).setTokenLock(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
            });

            it("should revert when called by non-owner", async function () {
                const newTokenLock = await ethers.deployContract("TokenLock");
                await expect(
                    launchpad.connect(nonOwner).setTokenLock(await newTokenLock.getAddress())
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });

        describe("setMoonbagsStake", function () {
            it("should update MoonbagsStake contract successfully", async function () {
                const newMoonbagsStake = await ethers.deployContract("MoonbagsStake");
                await newMoonbagsStake.initialize();

                await expect(
                    launchpad.connect(owner).setMoonbagsStake(await newMoonbagsStake.getAddress())
                ).to.emit(launchpad, "MoonbagsStakeUpdated");

                expect(await launchpad.moonbagsStake()).to.equal(
                    await newMoonbagsStake.getAddress()
                );
            });

            it("should revert with zero address", async function () {
                await expect(
                    launchpad.connect(owner).setMoonbagsStake(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
            });

            it("should revert when called by non-owner", async function () {
                const newMoonbagsStake = await ethers.deployContract("MoonbagsStake");
                await expect(
                    launchpad
                        .connect(nonOwner)
                        .setMoonbagsStake(await newMoonbagsStake.getAddress())
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });

        describe("updateActiveFeeTier", function () {
            it("should update active fee tier successfully", async function () {
                const newFeeTier = 10000; // 1%

                await launchpad.connect(owner).updateActiveFeeTier(newFeeTier);
                expect(await launchpad.activeFeeTier()).to.equal(newFeeTier);
            });

            it("should revert with invalid fee tier", async function () {
                const invalidFeeTier = 1234; // Invalid fee tier

                await expect(launchpad.connect(owner).updateActiveFeeTier(invalidFeeTier)).to.be
                    .reverted;
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad.connect(nonOwner).updateActiveFeeTier(10000)
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });

        describe("updateInitialVirtualTokenReserves", function () {
            it("should update initial virtual token reserves successfully", async function () {
                const newReserves = ethers.parseEther("*********0");

                await launchpad.connect(owner).updateInitialVirtualTokenReserves(newReserves);

                const config = await launchpad.config();
                expect(config.initialVirtualTokenReserves).to.equal(newReserves);
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad
                        .connect(nonOwner)
                        .updateInitialVirtualTokenReserves(ethers.parseEther("*********0"))
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });

        describe("updateConfigWithdrawFee", function () {
            it("should update withdraw fee configuration successfully", async function () {
                const newPlatformFeeWithdraw = 2000; // 20%
                const newCreatorFeeWithdraw = 2500; // 25%
                const newStakeFeeWithdraw = 3000; // 30%
                const newPlatformStakeFeeWithdraw = 2500; // 25%

                await launchpad
                    .connect(owner)
                    .updateConfigWithdrawFee(
                        newPlatformFeeWithdraw,
                        newCreatorFeeWithdraw,
                        newStakeFeeWithdraw,
                        newPlatformStakeFeeWithdraw
                    );

                const config = await launchpad.config();
                expect(config.initPlatformFeeWithdraw).to.equal(newPlatformFeeWithdraw);
                expect(config.initCreatorFeeWithdraw).to.equal(newCreatorFeeWithdraw);
                expect(config.initStakeFeeWithdraw).to.equal(newStakeFeeWithdraw);
                expect(config.initPlatformStakeFeeWithdraw).to.equal(newPlatformStakeFeeWithdraw);
            });

            it("should update withdraw fees to zero values", async function () {
                await launchpad.connect(owner).updateConfigWithdrawFee(0, 0, 0, 0);

                const config = await launchpad.config();
                expect(config.initPlatformFeeWithdraw).to.equal(0);
                expect(config.initCreatorFeeWithdraw).to.equal(0);
                expect(config.initStakeFeeWithdraw).to.equal(0);
                expect(config.initPlatformStakeFeeWithdraw).to.equal(0);
            });

            it("should update withdraw fees to maximum values", async function () {
                const maxFee = 10000; // 100% (FEE_DENOMINATOR)

                await launchpad.connect(owner).updateConfigWithdrawFee(maxFee, 0, 0, 0);

                const config = await launchpad.config();
                expect(config.initPlatformFeeWithdraw).to.equal(maxFee);
                expect(config.initCreatorFeeWithdraw).to.equal(0);
                expect(config.initStakeFeeWithdraw).to.equal(0);
                expect(config.initPlatformStakeFeeWithdraw).to.equal(0);
            });

            it("should allow fees that sum to more than 100% (no validation)", async function () {
                // Note: Unlike updateConfiguration, updateConfigWithdrawFee doesn't validate total fees
                const highFee = 5000; // 50% each

                await launchpad
                    .connect(owner)
                    .updateConfigWithdrawFee(highFee, highFee, highFee, highFee);

                const config = await launchpad.config();
                expect(config.initPlatformFeeWithdraw).to.equal(highFee);
                expect(config.initCreatorFeeWithdraw).to.equal(highFee);
                expect(config.initStakeFeeWithdraw).to.equal(highFee);
                expect(config.initPlatformStakeFeeWithdraw).to.equal(highFee);
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad.connect(nonOwner).updateConfigWithdrawFee(2000, 2500, 3000, 2500)
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });

            it("should affect new pools created after update", async function () {
                const newPlatformFeeWithdraw = 1000; // 10%
                const newCreatorFeeWithdraw = 4000; // 40%
                const newStakeFeeWithdraw = 3000; // 30%
                const newPlatformStakeFeeWithdraw = 2000; // 20%

                // Update withdraw fees
                await launchpad
                    .connect(owner)
                    .updateConfigWithdrawFee(
                        newPlatformFeeWithdraw,
                        newCreatorFeeWithdraw,
                        newStakeFeeWithdraw,
                        newPlatformStakeFeeWithdraw
                    );

                // Create a new pool
                const newTokenName = "New Test Token";
                const newTokenSymbol = "NEWTEST";
                const newTokenUri = "https://example.com/newtoken";
                const newTokenDescription = "A new test token";
                const newCustomThreshold = ethers.parseEther("0.025");

                const tx = await launchpad
                    .connect(creator)
                    .createPool(
                        newTokenName,
                        newTokenSymbol,
                        newTokenUri,
                        newTokenDescription,
                        twitter,
                        telegram,
                        website,
                        newCustomThreshold,
                        { value: POOL_CREATION_FEE }
                    );

                const receipt = await tx.wait();
                const event = receipt?.logs.find(
                    (log: any) => log.fragment && log.fragment.name === "TokenCreated"
                ) as any;
                const newTokenAddress = event?.args[0];

                // Check that the new pool uses the updated withdraw fees
                const newPool = await launchpad.getPool(newTokenAddress);
                expect(newPool[9]).to.equal(newPlatformFeeWithdraw); // platformFeeWithdraw
                expect(newPool[10]).to.equal(newCreatorFeeWithdraw); // creatorFeeWithdraw
                expect(newPool[11]).to.equal(newStakeFeeWithdraw); // stakeFeeWithdraw
                expect(newPool[12]).to.equal(newPlatformStakeFeeWithdraw); // platformStakeFeeWithdraw
            });
        });
    });

    describe("Threshold Configuration", function () {
        describe("createThresholdConfig", function () {
            it("should create threshold config successfully", async function () {
                const newThreshold = ethers.parseEther("0.05");

                await launchpad.connect(owner).createThresholdConfig(newThreshold);

                const thresholdConfig = await launchpad.thresholdConfig();
                expect(thresholdConfig).to.equal(newThreshold); // ThresholdConfig has only one field
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad.connect(nonOwner).createThresholdConfig(ethers.parseEther("0.05"))
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });

        describe("updateThresholdConfig", function () {
            it("should update threshold config successfully", async function () {
                const newThreshold = ethers.parseEther("0.04");

                await launchpad.connect(owner).updateThresholdConfig(newThreshold);

                const thresholdConfig = await launchpad.thresholdConfig();
                expect(thresholdConfig).to.equal(newThreshold); // ThresholdConfig has only one field
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad.connect(nonOwner).updateThresholdConfig(ethers.parseEther("0.04"))
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });
    });

    describe("Pool Management", function () {
        describe("earlyCompletePool", function () {
            beforeEach(async function () {
                await launchpad.createThresholdConfig(ethers.parseEther("0.011"));
            });

            it("should complete pool early when threshold is met", async function () {
                // Buy enough to meet threshold but not complete the pool
                const buyAmount = ethers.parseEther("0.02"); // Just above threshold of 0.02
                const sufficientAmount = ethers.parseEther("0.05"); // Sufficient for bonding curve + fees
                await launchpad
                    .connect(buyer)
                    .buyExactIn(tokenAddress, buyAmount, 0, { value: sufficientAmount });

                const poolAfterBuy = await launchpad.getPool(tokenAddress);
                // If pool is already completed by the buy, we can't test earlyCompletePool
                // So let's test that the pool has enough reserves to meet threshold
                console.log("Pool state after buy:", poolAfterBuy);
                // Pool not completed, we can test earlyCompletePool
                const thresholdConfig = await launchpad.thresholdConfig();
                expect(poolAfterBuy[0]).to.be.gte(thresholdConfig); // realHypeReserves should meet threshold

                await expect(launchpad.connect(owner).earlyCompletePool(tokenAddress)).to.emit(
                    launchpad,
                    "PoolCompleted"
                );

                const poolAfter = await launchpad.getPool(tokenAddress);
                expect(poolAfter[7]).to.equal(true); // isCompleted
            });

            it("should revert when threshold not met", async function () {
                const buyAmount = ethers.parseEther("0.01"); // Just above threshold of 0.02
                const sufficientAmount = ethers.parseEther("0.05"); // Sufficient for bonding curve + fees
                await launchpad
                    .connect(buyer)
                    .buyExactIn(tokenAddress, buyAmount, 0, { value: sufficientAmount });

                await expect(
                    launchpad.connect(owner).earlyCompletePool(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "NotEnoughThreshold");
            });

            it("should revert with non-existent token", async function () {
                const fakeTokenAddress = ethers.Wallet.createRandom().address;

                await expect(
                    launchpad.connect(owner).earlyCompletePool(fakeTokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "TokenNotExists");
            });

            it("should revert when pool already completed", async function () {
                const pool = await launchpad.getPool(tokenAddress);

                console.log("realHypeReserves:", pool.realHypeReserves);
                console.log("remainTokenReserves:", pool.remainTokenReserves);
                console.log("realTokenReserves:", pool.realTokenReserves);
                console.log("virtualHypeReserves:", pool.virtualHypeReserves);
                console.log("virtualTokenReserves:", pool.virtualTokenReserves);
                // Complete pool first
                const buyAmount = ethers.parseEther("1");
                await launchpad.connect(buyer).buyExactIn(tokenAddress, buyAmount, 0, {
                    value: buyAmount + ethers.parseEther("0.1"),
                });

                // Check if pool is already completed by the buy operation
                // const pool = await launchpad.getPool(tokenAddress);

                // Try to complete again - should revert
                await expect(
                    launchpad.connect(owner).earlyCompletePool(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "PoolAlreadyCompleted");
            });

            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad.connect(nonOwner).earlyCompletePool(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });
        });

        describe("skim", function () {
            it("should revert when called by non-owner", async function () {
                await expect(
                    launchpad.connect(nonOwner).skim(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "OwnableUnauthorizedAccount");
            });

            it("should revert with zero address", async function () {
                await expect(
                    launchpad.connect(owner).skim(ethers.ZeroAddress)
                ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
            });

            it("should revert with non-existent token", async function () {
                const fakeTokenAddress = ethers.Wallet.createRandom().address;

                await expect(
                    launchpad.connect(owner).skim(fakeTokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "TokenNotExists");
            });

            it("should revert when pool is completed", async function () {
                // Complete the pool first
                const buyAmount = ethers.parseEther("1");
                await launchpad.connect(buyer).buyExactIn(tokenAddress, buyAmount, 0, {
                    value: buyAmount + ethers.parseEther("0.1"),
                });

                // Check if pool is already completed by the buy operation
                const pool = await launchpad.getPool(tokenAddress);
                if (!pool[7]) {
                    // Pool not completed yet, complete it manually
                    await launchpad.connect(owner).earlyCompletePool(tokenAddress);
                }

                await expect(
                    launchpad.connect(owner).skim(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "PoolAlreadyCompleted");
            });

            it("should skim HYPE reserves after trading", async function () {
                // Create a fresh token for this test
                const tx = await launchpad
                    .connect(creator)
                    .createPool(
                        "Skim Test Token",
                        "SKIM",
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        { value: POOL_CREATION_FEE }
                    );

                const receipt = await tx.wait();
                const tokenCreatedEvent = receipt?.logs.find((log) => {
                    try {
                        const parsed = launchpad.interface.parseLog({
                            topics: log.topics as string[],
                            data: log.data,
                        });
                        return parsed?.name === "TokenCreated";
                    } catch {
                        return false;
                    }
                });

                const parsedEvent = launchpad.interface.parseLog({
                    topics: tokenCreatedEvent!.topics as string[],
                    data: tokenCreatedEvent!.data,
                });

                const skimTestTokenAddress = parsedEvent?.args[0];

                // Make some trades to create reserves with a smaller amount to avoid pool completion
                const desiredTokenAmount = ethers.parseUnits("1000", 6); // 1000 tokens with 6 decimals
                const estimatedCost = await launchpad.estimateBuyExactInCost(
                    skimTestTokenAddress,
                    desiredTokenAmount
                );

                await launchpad
                    .connect(buyer)
                    .buyExactIn(skimTestTokenAddress, desiredTokenAmount, 0, {
                        value: estimatedCost,
                    });

                const poolAfterBuy = await launchpad.getPool(skimTestTokenAddress);

                // Only test skim if pool is not completed
                if (!poolAfterBuy[7]) {
                    const ownerBalanceBefore = await ethers.provider.getBalance(owner.address);
                    const poolBefore = await launchpad.getPool(skimTestTokenAddress);

                    const skimTx = await launchpad.connect(owner).skim(skimTestTokenAddress);
                    const skimReceipt = await skimTx.wait();
                    const gasUsed = skimReceipt!.gasUsed * skimReceipt!.gasPrice;

                    const ownerBalanceAfter = await ethers.provider.getBalance(owner.address);

                    // Check that HYPE reserves were transferred (if any existed)
                    if (poolBefore[0] > 0) {
                        // realHypeReserves
                        const expectedBalance = ownerBalanceBefore + poolBefore[0] - gasUsed;
                        expect(ownerBalanceAfter).to.be.closeTo(
                            expectedBalance,
                            ethers.parseEther("0.0001")
                        );
                    }

                    // Verify reserves were cleared
                    const poolAfter = await launchpad.getPool(skimTestTokenAddress);
                    expect(poolAfter[0]).to.equal(0); // realHypeReserves
                    expect(poolAfter[1]).to.equal(0); // realTokenReserves
                } else {
                    // Pool was completed by the buy operation, so skim should revert
                    await expect(
                        launchpad.connect(owner).skim(skimTestTokenAddress)
                    ).to.be.revertedWithCustomError(launchpad, "PoolAlreadyCompleted");
                }
            });
        });
    });

    describe("Fee Distribution", function () {
        beforeEach(async function () {
            const buyAmount = ethers.parseEther("0.012");
            const sufficientAmount = ethers.parseEther("0.015");
            await launchpad
                .connect(buyer)
                .buyExactIn(tokenAddress, buyAmount, 0, { value: sufficientAmount });
        });

        describe("distributeBondingCurveFees", function () {
            it("should distribute bonding curve fees successfully", async function () {
                const poolBefore = await launchpad.getPool(tokenAddress);

                // Check contract balance
                const contractBalance = await ethers.provider.getBalance(
                    await launchpad.getAddress()
                );
                console.log("Contract balance:", ethers.formatEther(contractBalance));
                console.log("Fee recipient amount:", ethers.formatEther(poolBefore[6]));

                // Wait for distribution unlock time to pass
                await time.increase(300); // 5 minutes

                await launchpad.connect(owner).distributeBondingCurveFees(tokenAddress);

                const poolAfter = await launchpad.getPool(tokenAddress);
                expect(poolAfter[6]).to.lessThan(poolBefore[6]);
                const stakingPoolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
                const creatorPoolInfo = await moonbagsStake.getCreatorPoolInfo(tokenAddress);
                console.log(
                    "Staking Pool Total Rewards: ",
                    ethers.formatEther(stakingPoolInfo.totalRewards)
                );
                expect(stakingPoolInfo.totalRewards).to.be.gt(0);
                console.log(
                    "Creator Pool Total Rewards: ",
                    ethers.formatEther(creatorPoolInfo.totalRewards)
                );
                expect(creatorPoolInfo.totalRewards).to.be.gt(0);
            });

            it("should revert when distribution time is locked", async function () {
                console.log(
                    "Current block timestamp:",
                    (await ethers.provider.getBlock("latest"))?.timestamp
                );
                console.log(
                    "Distribution unlock time:",
                    (await launchpad.getPool(tokenAddress))[14]
                );
                console.log(
                    "Balance HYPE:",
                    ethers.formatEther(
                        await ethers.provider.getBalance(await launchpad.getAddress())
                    )
                );
                console.log(
                    "Pool fee recipient:",
                    ethers.formatEther((await launchpad.getPool(tokenAddress)).feeRecipient)
                );
                await expect(
                    launchpad.connect(owner).distributeBondingCurveFees(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "InvalidDistributionTime");
            });

            it("should revert with non-existent token", async function () {
                const fakeTokenAddress = ethers.Wallet.createRandom().address;

                await expect(
                    launchpad.connect(owner).distributeBondingCurveFees(fakeTokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "TokenNotExists");
            });
        });
    });

    describe("HyperSwap Integration", function () {
        describe("collectHyperSwapFees", function () {
            it("should revert when token doesn't exist", async function () {
                const fakeTokenAddress = ethers.Wallet.createRandom().address;

                await expect(
                    launchpad.connect(owner).collectHyperSwapFees(fakeTokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "TokenNotExists");
            });

            it("should revert when pool is not completed", async function () {
                await expect(
                    launchpad.connect(owner).collectHyperSwapFees(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "PoolNotCompleted");
            });

            it("should revert when distribution time is locked", async function () {
                const buyAmount = ethers.parseEther("1");
                const sufficientAmount = ethers.parseEther("1.1");
                await launchpad
                    .connect(buyer)
                    .buyExactIn(tokenAddress, buyAmount, 0, { value: sufficientAmount });

                // Check if pool is already completed by the buy operation
                const pool = await launchpad.getPool(tokenAddress);

                expect(pool[7]).to.equal(true); // isCompleted

                // Check if position exists
                const positionId = await launchpad.tokenToPositionId(tokenAddress);
                console.log("Position ID:", positionId.toString());

                console.log(
                    "Current block timestamp:",
                    (await ethers.provider.getBlock("latest"))?.timestamp
                );
                console.log(
                    "Distribution unlock time:",
                    (await launchpad.getPool(tokenAddress))[14]
                );
                console.log(
                    "Balance HYPE:",
                    ethers.formatEther(
                        await ethers.provider.getBalance(await launchpad.getAddress())
                    )
                );
                console.log(
                    "Pool fee recipient:",
                    ethers.formatEther((await launchpad.getPool(tokenAddress)).feeRecipient)
                );

                await expect(
                    launchpad.connect(owner).collectHyperSwapFees(tokenAddress)
                ).to.be.revertedWithCustomError(launchpad, "InvalidDistributionTime");
            });

            it("should collect fees after pool completion and swaps", async function () {
                const beforecontractBalance = await ethers.provider.getBalance(
                    await launchpad.getAddress()
                );
                console.log(
                    "Contract balance before buy:",
                    ethers.formatEther(beforecontractBalance)
                );
                // Complete the pool to create Uniswap V3 position
                const buyAmount = ethers.parseEther("1");
                const sufficientAmount = ethers.parseEther("1.1");
                const buyTx = await launchpad
                    .connect(buyer)
                    .buyExactIn(tokenAddress, buyAmount, 0, { value: sufficientAmount });
                const receipt = await buyTx.wait();
                // Verify pool is completed and V3 position is created
                const pool = await launchpad.getPool(tokenAddress);
                console.log("Buy completed, now checking pool state...: ", pool[6]);
                console.log(
                    "Contract balance after buy:",
                    await ethers.provider.getBalance(await launchpad.getAddress())
                );
                expect(pool[7]).to.equal(true); // isCompleted

                // Get the position ID and verify V3 pool creation via event
                const positionId = await launchpad.tokenToPositionId(tokenAddress);

                // Find V3PoolCreated event to get pool address
                const v3PoolCreatedEvent = receipt?.logs.find((log) => {
                    try {
                        const parsed = launchpad.interface.parseLog({
                            topics: log.topics as string[],
                            data: log.data,
                        });
                        return parsed?.name === "V3PoolCreated";
                    } catch {
                        return false;
                    }
                });

                expect(v3PoolCreatedEvent).to.not.be.undefined;
                const parsedV3Event = launchpad.interface.parseLog({
                    topics: v3PoolCreatedEvent!.topics as string[],
                    data: v3PoolCreatedEvent!.data,
                });
                const v3PoolAddress = parsedV3Event?.args[1];

                console.log("V3 Pool Address:", v3PoolAddress);
                console.log("Position ID:", positionId.toString());

                // Verify that the position was created
                expect(v3PoolAddress).to.not.equal(ethers.ZeroAddress);
                expect(positionId).to.be.gt(0);

                // Check position details using the position manager
                const positionInfo = await nonfungiblePositionManager.positions(positionId);
                console.log("Position Token0:", positionInfo[2]);
                console.log("Position Token1:", positionInfo[3]);
                console.log("Position Fee:", positionInfo[4]);
                console.log("Position Liquidity:", positionInfo[7]);

                // Verify the position has liquidity
                expect(positionInfo[7]).to.be.gt(0);

                // Get initial staking pool info before swaps
                const initialStakingPoolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
                const initialPlatformStakingPoolInfo = await moonbagsStake.getStakingPoolInfo(
                    await platformToken.getAddress()
                );

                console.log(
                    "Initial staking pool rewards:",
                    ethers.formatEther(initialStakingPoolInfo.totalRewards)
                );
                console.log(
                    "Initial platform staking pool rewards:",
                    ethers.formatEther(initialPlatformStakingPoolInfo.totalRewards)
                );

                // Perform multiple swaps using SwapRouter to generate fees
                const swapAmount = ethers.parseEther("0.1");

                // Get the current block timestamp
                const latestBlock = await ethers.provider.getBlock("latest");
                const blockTimestamp = latestBlock!.timestamp;

                // Swap 1: HYPE -> Token
                console.log("Performing swap 1: HYPE -> Token");
                const swapParams1 = {
                    tokenIn: await weth9.getAddress(),
                    tokenOut: tokenAddress,
                    fee: 3000,
                    recipient: buyer.address,
                    deadline: blockTimestamp + 3600, // 1 hour from current block time
                    amountIn: swapAmount,
                    amountOutMinimum: 0,
                    sqrtPriceLimitX96: 0,
                };

                await swapRouter
                    .connect(buyer)
                    .exactInputSingle(swapParams1, { value: swapAmount });

                // Swap 2: Token -> HYPE (need to approve first)
                console.log("Performing swap 2: Token -> HYPE");
                const tokenBalance = await token.balanceOf(buyer.address);
                const swapTokenAmount = tokenBalance / 2n; // Use half of token balance

                await token.connect(buyer).approve(await swapRouter.getAddress(), swapTokenAmount);

                const swapParams2 = {
                    tokenIn: tokenAddress,
                    tokenOut: await weth9.getAddress(),
                    fee: 3000,
                    recipient: buyer.address,
                    deadline: blockTimestamp + 3600,
                    amountIn: swapTokenAmount,
                    amountOutMinimum: 0,
                    sqrtPriceLimitX96: 0,
                };

                await swapRouter.connect(buyer).exactInputSingle(swapParams2);

                // Perform a few more swaps to accumulate more fees
                console.log("Performing additional swaps to accumulate fees...");
                for (let i = 0; i < 3; i++) {
                    // HYPE -> Token
                    await swapRouter.connect(buyer).exactInputSingle(
                        {
                            ...swapParams1,
                            amountIn: ethers.parseEther("0.05"),
                        },
                        { value: ethers.parseEther("0.05") }
                    );

                    // Token -> HYPE
                    const currentTokenBalance = await token.balanceOf(buyer.address);
                    if (currentTokenBalance > 0) {
                        const swapAmount = currentTokenBalance / 4n;
                        await token
                            .connect(buyer)
                            .approve(await swapRouter.getAddress(), swapAmount);
                        await swapRouter.connect(buyer).exactInputSingle({
                            ...swapParams2,
                            amountIn: swapAmount,
                        });
                    }
                }

                console.log("Swaps completed. Now collecting HyperSwap fees...");

                // Wait for fee distribution lock to expire
                await time.increase(300); // 5 minutes

                console.log(
                    "Current block timestamp:",
                    (await ethers.provider.getBlock("latest"))?.timestamp
                );
                console.log(
                    "Distribution unlock time:",
                    (await launchpad.getPool(tokenAddress))[14]
                );
                console.log(
                    "Balance HYPE:",
                    ethers.formatEther(
                        await ethers.provider.getBalance(await launchpad.getAddress())
                    )
                );
                console.log(
                    "Pool fee recipient:",
                    ethers.formatEther((await launchpad.getPool(tokenAddress)).feeRecipient)
                );

                // Collect HyperSwap fees
                let collectReceipt: any;
                try {
                    const collectTx = await launchpad
                        .connect(owner)
                        .collectHyperSwapFees(tokenAddress);
                    collectReceipt = await collectTx.wait();
                } catch (error) {
                    console.error("Error collecting HyperSwap fees:", error);

                    // Let's try to check if the staking pools exist
                    const tokenStakingPoolExists =
                        await moonbagsStake.stakingPoolExists(tokenAddress);
                    const platformStakingPoolExists = await moonbagsStake.stakingPoolExists(
                        await platformToken.getAddress()
                    );
                    const creatorPoolExists = await moonbagsStake.creatorPoolExists(tokenAddress);

                    console.log("Token staking pool exists:", tokenStakingPoolExists);
                    console.log("Platform staking pool exists:", platformStakingPoolExists);
                    console.log("Creator pool exists:", creatorPoolExists);

                    throw error;
                }

                console.log(
                    "Current block timestamp:",
                    (await ethers.provider.getBlock("latest"))?.timestamp
                );
                console.log(
                    "Distribution unlock time:",
                    (await launchpad.getPool(tokenAddress))[14]
                );
                console.log(
                    "Balance HYPE:",
                    ethers.formatEther(
                        await ethers.provider.getBalance(await launchpad.getAddress())
                    )
                );
                console.log(
                    "Pool fee recipient:",
                    ethers.formatEther((await launchpad.getPool(tokenAddress)).feeRecipient)
                );

                console.log("Fee collection transaction completed");

                // Get final staking pool info after fee collection
                const finalStakingPoolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
                const finalPlatformStakingPoolInfo = await moonbagsStake.getStakingPoolInfo(
                    await platformToken.getAddress()
                );

                console.log(
                    "Final staking pool rewards:",
                    ethers.formatEther(finalStakingPoolInfo.totalRewards)
                );
                console.log(
                    "Final platform staking pool rewards:",
                    ethers.formatEther(finalPlatformStakingPoolInfo.totalRewards)
                );

                // Verify that fees were collected and distributed to staking pools
                expect(finalStakingPoolInfo.totalRewards).to.be.gt(
                    initialStakingPoolInfo.totalRewards
                );
                expect(finalPlatformStakingPoolInfo.totalRewards).to.be.gt(
                    initialPlatformStakingPoolInfo.totalRewards
                );

                console.log("✅ Fees successfully collected and distributed to staking pools!");
                console.log(
                    "Token staking pool reward increase:",
                    ethers.formatEther(
                        finalStakingPoolInfo.totalRewards - initialStakingPoolInfo.totalRewards
                    )
                );
                console.log(
                    "Platform staking pool reward increase:",
                    ethers.formatEther(
                        finalPlatformStakingPoolInfo.totalRewards -
                            initialPlatformStakingPoolInfo.totalRewards
                    )
                );

                // Check contract balance after buy
                const contractBalance = await ethers.provider.getBalance(
                    await launchpad.getAddress()
                );
                console.log(
                    "Contract balance after collect fee:",
                    ethers.formatEther(contractBalance)
                );

                // Verify the transaction was successful
                expect(collectReceipt?.status).to.equal(1);
            });
        });
    });

    describe("IERC721Receiver", function () {
        it("should revert when called by unauthorized address", async function () {
            // Test the onERC721Received function - should revert when called by non-position manager
            await expect(
                launchpad.onERC721Received(owner.address, owner.address, 1, "0x")
            ).to.be.revertedWithCustomError(launchpad, "Unauthorized");
        });
    });

    describe("Edge Cases and Error Handling", function () {
        it("should handle zero amounts correctly in calculations", async function () {
            await expect(launchpad.calculateInitialVirtualHypeReserves(0)).to.not.be.reverted;
        });

        it("should revert with invalid configuration in calculations", async function () {
            // This would require modifying the config to create invalid state
            // For now, we test that the current config is valid
            const initialVirtualHypeReserves =
                await launchpad.calculateInitialVirtualHypeReserves(customThreshold);
            expect(initialVirtualHypeReserves).to.be.gt(0);
        });

        it("should handle large numbers correctly", async function () {
            const largeThreshold = ethers.parseEther("1000");
            const result = await launchpad.calculateInitialVirtualHypeReserves(largeThreshold);
            expect(result).to.be.gt(0);
        });
    });

    describe("State Consistency", function () {
        it("should maintain consistent state after multiple operations", async function () {
            // Create a fresh token for this test to avoid pool completion issues
            const tx = await launchpad
                .connect(creator)
                .createPool(
                    "State Test Token",
                    "STATE",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const receipt = await tx.wait();
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const stateTestTokenAddress = parsedEvent?.args[0];
            const stateTestToken = await ethers.getContractAt(
                "LaunchpadToken",
                stateTestTokenAddress
            );

            // Use a smaller amount to avoid pool completion
            const desiredTokenAmount = ethers.parseUnits("1000", 6); // 1000 tokens with 6 decimals
            const estimatedCost = await launchpad.estimateBuyExactInCost(
                stateTestTokenAddress,
                desiredTokenAmount
            );

            // Initial state
            const initialPool = await launchpad.getPool(stateTestTokenAddress);

            // Buy tokens
            await launchpad
                .connect(buyer)
                .buyExactIn(stateTestTokenAddress, desiredTokenAmount, 0, { value: estimatedCost });

            // Check state after buy
            const poolAfterBuy = await launchpad.getPool(stateTestTokenAddress);
            expect(poolAfterBuy[0]).to.be.gte(initialPool[0]); // realHypeReserves

            // Only proceed with sell if pool is not completed
            if (!poolAfterBuy[7]) {
                // Sell some tokens
                const buyerBalance = await stateTestToken.balanceOf(buyer.address);
                const sellAmount = buyerBalance / 2n;

                await stateTestToken
                    .connect(buyer)
                    .approve(await launchpad.getAddress(), sellAmount);
                await launchpad.connect(buyer).sellExactIn(stateTestTokenAddress, sellAmount, 0);

                // Check final state
                const finalPool = await launchpad.getPool(stateTestTokenAddress);
                expect(finalPool[0]).to.be.lte(poolAfterBuy[0]); // realHypeReserves
            } else {
                // Pool was completed, which is also valid behavior
                expect(poolAfterBuy[7]).to.equal(true); // isCompleted
            }
        });

        it("should maintain token supply consistency", async function () {
            // Create a fresh token for this test
            const tx = await launchpad
                .connect(creator)
                .createPool(
                    "Supply Test Token",
                    "SUPPLY",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const receipt = await tx.wait();
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const supplyTestTokenAddress = parsedEvent?.args[0];
            const supplyTestToken = await ethers.getContractAt(
                "LaunchpadToken",
                supplyTestTokenAddress
            );

            // Use a smaller amount to avoid pool completion
            const desiredTokenAmount = ethers.parseUnits("1000", 6); // 1000 tokens with 6 decimals
            const estimatedCost = await launchpad.estimateBuyExactInCost(
                supplyTestTokenAddress,
                desiredTokenAmount
            );

            const totalSupplyBefore = await supplyTestToken.totalSupply();

            await launchpad
                .connect(buyer)
                .buyExactIn(supplyTestTokenAddress, desiredTokenAmount, 0, {
                    value: estimatedCost,
                });

            const totalSupplyAfter = await supplyTestToken.totalSupply();
            const buyerBalance = await supplyTestToken.balanceOf(buyer.address);

            // The total supply should equal the initial supply plus the buyer's balance
            expect(totalSupplyAfter).to.equal(totalSupplyBefore + buyerBalance);
        });
    });
});
