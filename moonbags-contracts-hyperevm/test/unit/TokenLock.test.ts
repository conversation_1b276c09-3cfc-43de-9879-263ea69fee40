import { Sign<PERSON><PERSON><PERSON><PERSON>dd<PERSON> } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import { LaunchpadToken, TokenLock } from "../../types";

describe("TokenLock", function () {
    let tokenLock: TokenLock;
    let testToken: LaunchpadToken;
    let owner: Signer<PERSON><PERSON>Address;
    let admin: <PERSON><PERSON><PERSON><PERSON>Address;
    let locker: Signer<PERSON>ithAddress;
    let recipient: <PERSON><PERSON><PERSON><PERSON>Add<PERSON>;
    let user: Signer<PERSON><PERSON>Address;

    // Constants
    const LOCK_AMOUNT = ethers.parseEther("1000"); // 1000 tokens
    const LOCK_DURATION = 3600; // 1 hour

    beforeEach(async function () {
        [owner, admin, locker, recipient, user] = await ethers.getSigners();

        // Deploy test token
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        testToken = await LaunchpadTokenFactory.deploy(
            "Test Token",
            "TEST",
            18,
            "https://test.com"
        );
        await testToken.waitForDeployment();

        // Deploy TokenLock
        const TokenLockFactory = await ethers.getContractFactory("TokenLock");
        tokenLock = await TokenLockFactory.deploy();
        await tokenLock.waitForDeployment();
        await tokenLock.initialize();

        // Mint tokens to locker for testing
        await testToken.mint(locker.address, ethers.parseEther("10000"));

        // Set token as listed to allow transfers
        await testToken.setListed(true);
    });

    describe("Initialization", function () {
        it("should initialize with correct default values", async function () {
            const config = await tokenLock.getConfig();
            expect(config).to.equal(owner.address);
            expect(await tokenLock.nextLockId()).to.equal(1);
        });

        it("should set owner correctly", async function () {
            expect(await tokenLock.owner()).to.equal(owner.address);
        });

        it("should not allow double initialization", async function () {
            await expect(tokenLock.initialize()).to.be.revertedWithCustomError(
                tokenLock,
                "InvalidInitialization"
            );
        });
    });

    describe("Configuration Management", function () {
        it("should update configuration successfully", async function () {
            const newAdmin = admin.address;

            const tx = await tokenLock.updateConfig(newAdmin);

            await expect(tx).to.emit(tokenLock, "ConfigUpdated").withArgs(owner.address, newAdmin);

            const config = await tokenLock.getConfig();
            expect(config).to.equal(newAdmin);
        });

        it("should revert when called by non-owner", async function () {
            await expect(
                tokenLock.connect(user).updateConfig(admin.address)
            ).to.be.revertedWithCustomError(tokenLock, "OwnableUnauthorizedAccount");
        });

        it("should revert with zero admin address", async function () {
            await expect(tokenLock.updateConfig(ethers.ZeroAddress)).to.be.revertedWithCustomError(
                tokenLock,
                "InvalidParams"
            );
        });
    });

    describe("Lock Creation", function () {
        beforeEach(async function () {
            // Approve tokens for TokenLock contract to transfer
            await testToken.connect(locker).approve(await tokenLock.getAddress(), LOCK_AMOUNT);
        });

        it("should create a lock successfully", async function () {
            const endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;

            const tx = await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), LOCK_AMOUNT, endTime, recipient.address);

            const receipt = await tx.wait();
            const lockId = 1;

            // Check event emission
            await expect(tx)
                .to.emit(tokenLock, "LockCreated")
                .withArgs(
                    lockId,
                    locker.address,
                    recipient.address,
                    await testToken.getAddress(),
                    LOCK_AMOUNT,
                    await ethers.provider.getBlock(receipt!.blockNumber).then((b) => b!.timestamp),
                    endTime
                );

            // Verify lock details
            const lockDetails = await tokenLock.getLock(lockId);
            expect(lockDetails.token).to.equal(await testToken.getAddress());
            expect(lockDetails.amount).to.equal(LOCK_AMOUNT);
            expect(lockDetails.recipient).to.equal(recipient.address);
            expect(lockDetails.locker).to.equal(locker.address);
            expect(lockDetails.closed).to.be.false;
            expect(lockDetails.endTime).to.equal(endTime);

            // Verify lock count
            expect(await tokenLock.getLockCount()).to.equal(1);
        });

        it("should revert with invalid end time", async function () {
            const pastTime = (await ethers.provider.getBlock("latest"))!.timestamp - 1;

            await expect(
                tokenLock
                    .connect(locker)
                    .createLock(
                        await testToken.getAddress(),
                        LOCK_AMOUNT,
                        pastTime,
                        recipient.address
                    )
            ).to.be.revertedWithCustomError(tokenLock, "InvalidParams");
        });

        it("should revert with zero token address", async function () {
            const endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;

            await expect(
                tokenLock
                    .connect(locker)
                    .createLock(ethers.ZeroAddress, LOCK_AMOUNT, endTime, recipient.address)
            ).to.be.revertedWithCustomError(tokenLock, "InvalidParams");
        });

        it("should revert with zero recipient address", async function () {
            const endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;

            await expect(
                tokenLock
                    .connect(locker)
                    .createLock(
                        await testToken.getAddress(),
                        LOCK_AMOUNT,
                        endTime,
                        ethers.ZeroAddress
                    )
            ).to.be.revertedWithCustomError(tokenLock, "InvalidParams");
        });

        it("should revert with insufficient token balance", async function () {
            const endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;
            const excessiveAmount = ethers.parseEther("20000"); // More than available

            // Approve the excessive amount
            await testToken.connect(locker).approve(await tokenLock.getAddress(), excessiveAmount);

            await expect(
                tokenLock
                    .connect(locker)
                    .createLock(
                        await testToken.getAddress(),
                        excessiveAmount,
                        endTime,
                        recipient.address
                    )
            ).to.be.revertedWithCustomError(testToken, "ERC20InsufficientBalance");
        });
    });

    describe("Token Withdrawal", function () {
        let lockId: number;
        let endTime: number;

        beforeEach(async function () {
            // Approve tokens and create a lock
            await testToken.connect(locker).approve(await tokenLock.getAddress(), LOCK_AMOUNT);
            endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;

            await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), LOCK_AMOUNT, endTime, recipient.address);

            lockId = 1;
        });

        it("should allow withdrawal after lock period expires", async function () {
            // Fast forward time to after lock expiry
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);

            const initialBalance = await testToken.balanceOf(recipient.address);

            const tx = await tokenLock.connect(recipient).withdraw(lockId);

            await expect(tx)
                .to.emit(tokenLock, "TokensWithdrawn")
                .withArgs(lockId, recipient.address, recipient.address, LOCK_AMOUNT);

            const finalBalance = await testToken.balanceOf(recipient.address);
            expect(finalBalance - initialBalance).to.equal(LOCK_AMOUNT);

            // Verify lock is closed
            const lockDetails = await tokenLock.getLock(lockId);
            expect(lockDetails.closed).to.be.true;
        });

        it("should allow admin to withdraw", async function () {
            // Fast forward time to after lock expiry
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);

            const initialBalance = await testToken.balanceOf(recipient.address);

            await tokenLock.connect(owner).withdraw(lockId); // owner is admin

            const finalBalance = await testToken.balanceOf(recipient.address);
            expect(finalBalance - initialBalance).to.equal(LOCK_AMOUNT);
        });

        it("should allow locker to withdraw", async function () {
            // Fast forward time to after lock expiry
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);

            const initialBalance = await testToken.balanceOf(recipient.address);

            await tokenLock.connect(locker).withdraw(lockId);

            const finalBalance = await testToken.balanceOf(recipient.address);
            expect(finalBalance - initialBalance).to.equal(LOCK_AMOUNT);
        });

        it("should revert when withdrawing before lock period expires", async function () {
            await expect(
                tokenLock.connect(recipient).withdraw(lockId)
            ).to.be.revertedWithCustomError(tokenLock, "Unauthorized");
        });

        it("should revert when called by unauthorized user", async function () {
            // Fast forward time to after lock expiry
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);

            await expect(tokenLock.connect(user).withdraw(lockId)).to.be.revertedWithCustomError(
                tokenLock,
                "Unauthorized"
            );
        });

        it("should revert with invalid lock ID", async function () {
            await expect(tokenLock.connect(recipient).withdraw(999)).to.be.revertedWithCustomError(
                tokenLock,
                "InvalidLockId"
            );
        });

        it("should revert when lock is already closed", async function () {
            // Fast forward time and withdraw first
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);

            await tokenLock.connect(recipient).withdraw(lockId);

            // Try to withdraw again
            await expect(
                tokenLock.connect(recipient).withdraw(lockId)
            ).to.be.revertedWithCustomError(tokenLock, "ContractClosed");
        });
    });

    describe("Lock Extension", function () {
        let lockId: number;
        let endTime: number;

        beforeEach(async function () {
            // Approve tokens and create a lock
            await testToken.connect(locker).approve(await tokenLock.getAddress(), LOCK_AMOUNT);
            endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;

            await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), LOCK_AMOUNT, endTime, recipient.address);

            lockId = 1;
        });

        it("should extend lock duration successfully", async function () {
            const additionalDuration = 3600; // 1 more hour
            const expectedNewEndTime = endTime + additionalDuration;
            const originalLockDetails = await tokenLock.getLock(lockId);

            const tx = await tokenLock.connect(locker).extendLock(lockId, expectedNewEndTime);

            const lockDetails = await tokenLock.getLock(lockId);
            expect(lockDetails.endTime.toString()).to.equal(expectedNewEndTime.toString());

            await expect(tx)
                .to.emit(tokenLock, "LockCreated")
                .withArgs(
                    lockId,
                    locker.address,
                    recipient.address,
                    await testToken.getAddress(),
                    LOCK_AMOUNT,
                    originalLockDetails.startTime,
                    expectedNewEndTime
                );
        });

        it("should extend expired lock from current time", async function () {
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 100]);
            await ethers.provider.send("evm_mine", []);

            const additionalDuration = 3600;
            const currentTime = (await ethers.provider.getBlock("latest"))!.timestamp;
            const expectedNewEndTime = currentTime + additionalDuration;

            await tokenLock.connect(locker).extendLock(lockId, expectedNewEndTime);

            const lockDetails = await tokenLock.getLock(lockId);
            expect(lockDetails.endTime).to.be.closeTo(expectedNewEndTime, 5);
        });

        it("should revert when called by non-locker", async function () {
            const newEndTime = endTime + 3600;
            await expect(
                tokenLock.connect(recipient).extendLock(lockId, newEndTime)
            ).to.be.revertedWithCustomError(tokenLock, "Unauthorized");
        });

        it("should revert with invalid new end time", async function () {
            const currentTime = (await ethers.provider.getBlock("latest"))!.timestamp;

            // Test with past time
            await expect(
                tokenLock.connect(locker).extendLock(lockId, currentTime - 100)
            ).to.be.revertedWithCustomError(tokenLock, "InvalidParams");

            // Test with current time
            await expect(
                tokenLock.connect(locker).extendLock(lockId, currentTime)
            ).to.be.revertedWithCustomError(tokenLock, "InvalidParams");

            // Test with time before current endTime
            await expect(
                tokenLock.connect(locker).extendLock(lockId, endTime - 100)
            ).to.be.revertedWithCustomError(tokenLock, "InvalidParams");
        });

        it("should revert with invalid lock ID", async function () {
            const newEndTime = endTime + 3600;
            await expect(
                tokenLock.connect(locker).extendLock(999, newEndTime)
            ).to.be.revertedWithCustomError(tokenLock, "InvalidLockId");
        });

        it("should revert when lock is closed", async function () {
            // Fast forward and withdraw
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);
            await tokenLock.connect(recipient).withdraw(lockId);

            const newEndTime = endTime + 3600;
            await expect(
                tokenLock.connect(locker).extendLock(lockId, newEndTime)
            ).to.be.revertedWithCustomError(tokenLock, "ContractClosed");
        });
    });

    describe("View Functions", function () {
        let lockId: number;

        beforeEach(async function () {
            // Approve tokens and create a lock
            await testToken.connect(locker).approve(await tokenLock.getAddress(), LOCK_AMOUNT);
            const endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;

            await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), LOCK_AMOUNT, endTime, recipient.address);

            lockId = 1;
        });

        it("should return correct lock details", async function () {
            const lockDetails = await tokenLock.getLock(lockId);

            expect(lockDetails.token).to.equal(await testToken.getAddress());
            expect(lockDetails.amount).to.equal(LOCK_AMOUNT);
            expect(lockDetails.recipient).to.equal(recipient.address);
            expect(lockDetails.locker).to.equal(locker.address);
            expect(lockDetails.closed).to.be.false;
            expect(lockDetails.endTime).to.be.gt(lockDetails.startTime);
        });

        it("should return correct withdrawable status before expiry", async function () {
            const isWithdrawable = await tokenLock.isWithdrawable(lockId);
            expect(isWithdrawable).to.be.false;
        });

        it("should return correct withdrawable status after expiry", async function () {
            // Fast forward time to after lock expiry
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);

            const isWithdrawable = await tokenLock.isWithdrawable(lockId);
            expect(isWithdrawable).to.be.true;
        });

        it("should return false for closed lock", async function () {
            // Fast forward and withdraw
            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION + 1]);
            await ethers.provider.send("evm_mine", []);
            await tokenLock.connect(recipient).withdraw(lockId);

            const isWithdrawable = await tokenLock.isWithdrawable(lockId);
            expect(isWithdrawable).to.be.false;
        });

        it("should return false for invalid lock ID", async function () {
            const isWithdrawable = await tokenLock.isWithdrawable(999);
            expect(isWithdrawable).to.be.false;
        });

        it("should return correct lock count", async function () {
            expect(await tokenLock.getLockCount()).to.equal(1);

            // Create another lock
            await testToken.connect(locker).approve(await tokenLock.getAddress(), LOCK_AMOUNT);
            const endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;
            await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), LOCK_AMOUNT, endTime, recipient.address);

            expect(await tokenLock.getLockCount()).to.equal(2);
        });

        it("should revert getLock with invalid ID", async function () {
            await expect(tokenLock.getLock(999)).to.be.revertedWithCustomError(
                tokenLock,
                "InvalidLockId"
            );
        });
    });

    describe("Emergency Functions", function () {
        beforeEach(async function () {
            // Transfer some tokens to the contract
            await testToken.connect(locker).transfer(await tokenLock.getAddress(), LOCK_AMOUNT);
        });

        it("should allow owner to emergency withdraw", async function () {
            const withdrawAmount = ethers.parseEther("500");
            const initialAdminBalance = await testToken.balanceOf(owner.address);

            await tokenLock.emergencyWithdraw(await testToken.getAddress(), withdrawAmount);

            const finalAdminBalance = await testToken.balanceOf(owner.address);
            expect(finalAdminBalance - initialAdminBalance).to.equal(withdrawAmount);
        });

        it("should revert emergency withdraw when called by non-owner", async function () {
            await expect(
                tokenLock.connect(user).emergencyWithdraw(await testToken.getAddress(), LOCK_AMOUNT)
            ).to.be.revertedWithCustomError(tokenLock, "OwnableUnauthorizedAccount");
        });
    });

    describe("Integration Tests", function () {
        it("should handle multiple locks correctly", async function () {
            const lockAmount1 = ethers.parseEther("1000");
            const lockAmount2 = ethers.parseEther("2000");

            const endTime1 = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;
            const endTime2 =
                (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION * 2;

            // Approve tokens and create first lock
            await testToken.connect(locker).approve(await tokenLock.getAddress(), lockAmount1);
            await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), lockAmount1, endTime1, recipient.address);

            // Approve tokens and create second lock
            await testToken.connect(locker).approve(await tokenLock.getAddress(), lockAmount2);
            await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), lockAmount2, endTime2, user.address);

            expect(await tokenLock.getLockCount()).to.equal(2);

            // Verify both locks
            const lock1 = await tokenLock.getLock(1);
            const lock2 = await tokenLock.getLock(2);

            expect(lock1.amount).to.equal(lockAmount1);
            expect(lock1.recipient).to.equal(recipient.address);
            expect(lock2.amount).to.equal(lockAmount2);
            expect(lock2.recipient).to.equal(user.address);
        });

        it("should handle complete lock lifecycle", async function () {
            await testToken.connect(locker).approve(await tokenLock.getAddress(), LOCK_AMOUNT);
            const endTime = (await ethers.provider.getBlock("latest"))!.timestamp + LOCK_DURATION;

            await tokenLock
                .connect(locker)
                .createLock(await testToken.getAddress(), LOCK_AMOUNT, endTime, recipient.address);

            const lockId = 1;

            const newEndTime = endTime + LOCK_DURATION;
            await tokenLock.connect(locker).extendLock(lockId, newEndTime);

            await ethers.provider.send("evm_increaseTime", [LOCK_DURATION * 2 + 100]);
            await ethers.provider.send("evm_mine", []);

            expect(await tokenLock.isWithdrawable(lockId)).to.be.true;

            const initialBalance = await testToken.balanceOf(recipient.address);
            await tokenLock.connect(recipient).withdraw(lockId);
            const finalBalance = await testToken.balanceOf(recipient.address);

            expect(finalBalance - initialBalance).to.equal(LOCK_AMOUNT);

            const lockDetails = await tokenLock.getLock(lockId);
            expect(lockDetails.closed).to.be.true;
        });
    });
});
