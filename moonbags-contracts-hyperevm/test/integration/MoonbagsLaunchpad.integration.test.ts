import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import {
    INonfungiblePositionManager,
    ISwapRouter,
    IWETH9,
    LaunchpadToken,
    MoonbagsLaunchpad,
    MoonbagsStake,
    TokenLock,
} from "../../types";
import { externalFixture } from "../unit/external.fixtures";

describe("MoonbagsLaunchpad - Integration Test", function () {
    let launchpad: MoonbagsLaunchpad;
    let tokenLock: TokenLock;
    let moonbagsStake: MoonbagsStake;
    let weth9: IWETH9;
    let nonfungiblePositionManager: INonfungiblePositionManager;
    let swapRouter: ISwapRouter;
    let owner: Signer<PERSON><PERSON><PERSON>ddress;
    let creator: Signer<PERSON>ithAddress;
    let buyer1: SignerWithAddress;
    let buyer2: SignerWithAddress;
    let seller: SignerWithAddress;
    let treasury: SignerWithAddress;
    let platformToken: LaunchpadToken;

    // Token parameters
    const tokenName = "Integration Test Token";
    const tokenSymbol = "ITT";
    const tokenUri = "https://example.com/integration-token";
    const tokenDescription = "A token for integration testing complete flow";
    const twitter = "https://twitter.com/integration_test";
    const telegram = "https://t.me/integration_test";
    const website = "https://integration-test.com";
    const customThreshold = ethers.parseEther("0.1"); // Higher threshold for testing

    // Constants from contract
    const DEFAULT_FEE_TIER = 3000;
    const POOL_CREATION_FEE = ethers.parseEther("0.001");
    const MIN_LOCK_DURATION = 3600; // 1 hour in seconds

    // Helper function to get sufficient amount for any token
    async function getSufficientAmount(
        tokenAddress: string,
        baseAmount = ethers.parseEther("0.01")
    ) {
        const estimatedCost = await launchpad.estimateBuyExactInCost(tokenAddress, baseAmount);
        return estimatedCost + ethers.parseEther("0.001"); // Add small buffer
    }

    beforeEach(async function () {
        [owner, creator, buyer1, buyer2, seller, treasury] = await ethers.getSigners();

        const {
            weth9: _weth9,
            nonfungiblePositionManager: _nonfungiblePositionManager,
            swapRouter: _swapRouter,
        } = await externalFixture();
        weth9 = _weth9;
        nonfungiblePositionManager = _nonfungiblePositionManager;
        swapRouter = _swapRouter;

        // Deploy platform token
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        platformToken = await LaunchpadTokenFactory.deploy(
            "Platform Token",
            "PLAT",
            18,
            "https://platform.com"
        );
        await platformToken.waitForDeployment();

        // Deploy MoonbagsStake
        const MoonbagsStakeFactory = await ethers.getContractFactory("MoonbagsStake");
        moonbagsStake = await MoonbagsStakeFactory.deploy();
        await moonbagsStake.waitForDeployment();
        await moonbagsStake.initialize();

        // Deploy TokenLock
        const TokenLockFactory = await ethers.getContractFactory("TokenLock");
        tokenLock = await TokenLockFactory.deploy();
        await tokenLock.waitForDeployment();
        await tokenLock.initialize();

        // Deploy MoonbagsLaunchpad
        const MoonbagsLaunchpadFactory = await ethers.getContractFactory("MoonbagsLaunchpad");
        launchpad = await MoonbagsLaunchpadFactory.deploy();
        await launchpad.waitForDeployment();

        // Initialize the launchpad
        await launchpad.initialize(
            await nonfungiblePositionManager.getAddress(),
            await weth9.getAddress(),
            DEFAULT_FEE_TIER,
            await platformToken.getAddress(),
            await moonbagsStake.getAddress(),
            await tokenLock.getAddress()
        );

        await launchpad.updateFeeRecipients(treasury.address, treasury.address);
    });

    describe("Complete Flow: createAndLockFirstBuy → buy → sell → buy (pool completion)", function () {
        it("should execute the complete token lifecycle successfully", async function () {
            console.log("\n🚀 Starting Integration Test: Complete Token Lifecycle");

            // Step 1: createAndLockFirstBuy
            console.log("\n📝 Step 1: Create token and lock first buy");

            const lockDuration = MIN_LOCK_DURATION * 2; // 2 hours
            const firstBuyAmount = ethers.parseEther("0.01"); // ETH to spend on first buy
            const firstBuyTokenAmount = ethers.parseUnits("1000000", 6); // 1M tokens to buy and lock
            const totalCreationValue = POOL_CREATION_FEE + firstBuyAmount;

            // Record initial balances
            const creatorInitialBalance = await ethers.provider.getBalance(creator.address);
            console.log(
                "Creator initial balance:",
                ethers.formatEther(creatorInitialBalance),
                "ETH"
            );

            const createAndLockTx = await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    firstBuyTokenAmount,
                    lockDuration,
                    { value: totalCreationValue }
                );

            const createReceipt = await createAndLockTx.wait();
            console.log(
                "✅ Token created and first buy locked, gas used:",
                createReceipt?.gasUsed.toString()
            );

            // Extract token address from TokenCreated event
            const tokenCreatedEvent = createReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            console.log("event log: ", tokenCreatedEvent);

            expect(tokenCreatedEvent).to.not.be.undefined;
            const parsedTokenEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });
            const tokenAddress = parsedTokenEvent?.args[0];
            console.log("Token created at address:", tokenAddress);

            // Verify trade event was emitted
            const tradeEvent = createReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });
            expect(tradeEvent).to.not.be.undefined;

            // Verify lock was created
            const lockCount = await tokenLock.getLockCount();
            expect(lockCount).to.be.gt(0);
            console.log("Lock created with ID:", lockCount.toString());

            // Get token contract instance
            const token = await ethers.getContractAt("LaunchpadToken", tokenAddress);

            // Verify pool state after creation and first buy
            let pool = await launchpad.getPool(tokenAddress);
            console.log("Pool state after creation:");
            console.log("  Real HYPE reserves:", ethers.formatEther(pool.realHypeReserves));
            console.log(
                "  Virtual token reserves:",
                ethers.formatUnits(pool.virtualTokenReserves, 6)
            );
            console.log("  Virtual hype reserves:", ethers.formatEther(pool.virtualHypeReserves));
            console.log("  Is completed:", pool.isCompleted);
            console.log("  Fee recipient balance:", ethers.formatEther(pool.feeRecipient));

            expect(pool.isCompleted).to.be.false;
            expect(pool.realHypeReserves).to.be.gt(0);

            console.log("\n💰 Step 2: Additional buy transactions by different users");

            // First additional buy
            const buyAmount1 = ethers.parseEther("0.015");
            const estimateCost1 = await getSufficientAmount(tokenAddress, buyAmount1);
            const buy1Tx = await launchpad
                .connect(buyer1)
                .buyExactIn(tokenAddress, buyAmount1, 0, { value: estimateCost1 });

            const buy1Receipt = await buy1Tx.wait();
            console.log("✅ Buyer1 purchase completed, gas used:", buy1Receipt?.gasUsed.toString());

            const buyer1Balance = await token.balanceOf(buyer1.address);
            console.log("Buyer1 token balance:", ethers.formatUnits(buyer1Balance, 6));
            // Check pool state after additional buys
            pool = await launchpad.getPool(tokenAddress);
            console.log("Pool state after additional buys:");
            console.log("  Real HYPE reserves:", ethers.formatEther(pool.realHypeReserves));
            console.log(
                "  Virtual token reserves:",
                ethers.formatUnits(pool.virtualTokenReserves, 6)
            );
            console.log("  Virtual hype reserves:", ethers.formatEther(pool.virtualHypeReserves));
            console.log("  Is completed:", pool.isCompleted);

            // Sell
            console.log("\nSell transaction");

            const sellAmount = buyer1Balance / 3n;

            const sellTx = await launchpad.connect(buyer1).sellExactIn(tokenAddress, sellAmount, 0);

            const sellReceipt = await sellTx.wait();
            console.log(
                "✅ Sell transaction completed, gas used:",
                sellReceipt?.gasUsed.toString()
            );

            const buyer1BalanceAfterSell = await token.balanceOf(buyer1.address);
            console.log(
                "Buyer1 balance after sell:",
                ethers.formatUnits(buyer1BalanceAfterSell, 6)
            );

            // Verify sell trade event
            const sellTradeEvent = sellReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });
            expect(sellTradeEvent).to.not.be.undefined;

            const parsedSellEvent = launchpad.interface.parseLog({
                topics: sellTradeEvent!.topics as string[],
                data: sellTradeEvent!.data,
            });
            expect(parsedSellEvent?.args[2]).to.be.false; // isBuy should be false for sell
            console.log("Tokens sold:", ethers.formatUnits(parsedSellEvent?.args[4], 6));
            console.log("ETH received:", ethers.formatEther(parsedSellEvent?.args[3]));

            // Check pool state after sell
            pool = await launchpad.getPool(tokenAddress);
            console.log("Pool state after sell:");
            console.log("  Real HYPE reserves:", ethers.formatEther(pool.realHypeReserves));
            console.log(
                "  Virtual token reserves:",
                ethers.formatUnits(pool.virtualTokenReserves, 6)
            );
            console.log("  Virtual hype reserves:", ethers.formatEther(pool.virtualHypeReserves));
            console.log("  Is completed:", pool.isCompleted);

            // Second additional buy
            const buyAmount2 = ethers.parseEther("0.02");
            const estimateCost2 = await getSufficientAmount(tokenAddress, buyAmount2);

            const buy2Tx = await launchpad
                .connect(buyer2)
                .buyExactIn(tokenAddress, buyAmount2, 0, { value: estimateCost2 });

            await buy2Tx.wait();

            const buyer2Balance = await token.balanceOf(buyer2.address);
            console.log("Buyer2 token balance:", ethers.formatUnits(buyer2Balance, 6));

            // Check pool state after additional buys
            pool = await launchpad.getPool(tokenAddress);
            console.log("Pool state after additional buys:");
            console.log("  Real HYPE reserves:", ethers.formatEther(pool.realHypeReserves));
            console.log(
                "  Virtual token reserves:",
                ethers.formatUnits(pool.virtualTokenReserves, 6)
            );
            console.log("  Virtual hype reserves:", ethers.formatEther(pool.virtualHypeReserves));
            console.log("  Is completed:", pool.isCompleted);

            // Step 4: Large buy to complete the pool and trigger migration
            console.log("\n🎯 Step 4: Large buy to complete pool and migrate to UniswapV3");

            // Calculate remaining amount needed to reach threshold
            const threshold = customThreshold;
            const currentReserves = pool.realHypeReserves;
            const remainingNeeded = threshold - currentReserves;
            const largeBuyAmount = remainingNeeded + ethers.parseEther("0.01"); // Add extra to ensure completion

            console.log("Current reserves:", ethers.formatEther(currentReserves));
            console.log("Threshold:", ethers.formatEther(threshold));
            console.log("Remaining needed:", ethers.formatEther(remainingNeeded));
            console.log("Large buy amount:", ethers.formatEther(largeBuyAmount));

            const largeBuyTx = await launchpad
                .connect(buyer2)
                .buyExactIn(tokenAddress, largeBuyAmount, 0, { value: largeBuyAmount });

            const largeBuyReceipt = await largeBuyTx.wait();
            console.log("✅ Large buy completed, gas used:", largeBuyReceipt?.gasUsed.toString());

            const v3PoolCreatedEvent = largeBuyReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "V3PoolCreated";
                } catch {
                    return false;
                }
            });

            const v3PositionMintedEvent = largeBuyReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "V3PositionMinted";
                } catch {
                    return false;
                }
            });

            // Verify pool completion
            pool = await launchpad.getPool(tokenAddress);
            console.log("Final pool state:");
            console.log("  Is completed:", pool.isCompleted);
            console.log("  Real HYPE reserves:", ethers.formatEther(pool.realHypeReserves));

            if (pool.isCompleted) {
                console.log("🎉 Pool completed and migrated to UniswapV3!");
                expect(v3PoolCreatedEvent).to.not.be.undefined;

                // Verify UniswapV3 pool creation (check via V3PoolCreated event)
                const parsedV3Event = launchpad.interface.parseLog({
                    topics: v3PoolCreatedEvent!.topics as string[],
                    data: v3PoolCreatedEvent!.data,
                });
                const v3PoolAddress = parsedV3Event?.args[1];
                expect(v3PoolAddress).to.not.equal(ethers.ZeroAddress);
                console.log("UniswapV3 pool created at:", v3PoolAddress);

                // Verify position NFT was minted
                const positionId = await launchpad.tokenToPositionId(tokenAddress);
                expect(positionId).to.be.gt(0);
                console.log("Position NFT ID:", positionId.toString());
            } else {
                console.log(
                    "ℹ️  Pool not yet completed, may need more purchases to reach threshold"
                );
            }

            // Step 5: Verify final balances and states
            console.log("\n📊 Step 5: Final verification");

            const finalBuyer1Balance = await token.balanceOf(buyer1.address);
            const finalBuyer2Balance = await token.balanceOf(buyer2.address);
            const creatorDirectBalance = await token.balanceOf(creator.address);
            const lockContractBalance = await token.balanceOf(await tokenLock.getAddress());

            console.log("Final token balances:");
            console.log("  Buyer1:", ethers.formatUnits(finalBuyer1Balance, 6));
            console.log("  Buyer2:", ethers.formatUnits(finalBuyer2Balance, 6));
            console.log("  Creator (direct):", ethers.formatUnits(creatorDirectBalance, 6));
            console.log("  Lock contract:", ethers.formatUnits(lockContractBalance, 6));

            // Verify creator's tokens are still locked
            expect(creatorDirectBalance).to.equal(0);
            expect(lockContractBalance).to.be.gt(0);

            // Verify lock details
            const lockDetails = await tokenLock.getLock(1);
            expect(lockDetails.token).to.equal(tokenAddress);
            expect(lockDetails.recipient).to.equal(creator.address);
            expect(lockDetails.closed).to.be.false;
            console.log(
                "Lock end time:",
                new Date(Number(lockDetails.endTime) * 1000).toISOString()
            );

            // Verify token is registered in launchpad
            expect(await launchpad.isToken(tokenAddress)).to.be.true;

            console.log("\n✅ Integration test completed successfully!");
        });

        it("should handle edge case where pool doesn't complete", async function () {
            console.log("\n🧪 Edge Case Test: Pool doesn't reach completion threshold");

            // Use smaller amounts to ensure pool doesn't complete
            const lockDuration = MIN_LOCK_DURATION;
            const smallBuyAmount = ethers.parseEther("0.005"); // Very small amount
            const smallTokenAmount = ethers.parseUnits("100000", 6); // 100k tokens
            const totalCreationValue = POOL_CREATION_FEE + smallBuyAmount;

            const createAndLockTx = await launchpad.connect(creator).createAndLockFirstBuy(
                "Small Test Token",
                "STT",
                tokenUri,
                tokenDescription,
                twitter,
                telegram,
                website,
                customThreshold, // Use high threshold to prevent completion
                smallTokenAmount,
                lockDuration,
                { value: totalCreationValue }
            );

            const createReceipt = await createAndLockTx.wait();

            // Extract token address
            const tokenCreatedEvent = createReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedTokenEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });
            const tokenAddress = parsedTokenEvent?.args[0];

            // Make small additional purchases
            const smallPurchase = ethers.parseEther("0.002");
            await launchpad.connect(buyer1).buyExactIn(tokenAddress, smallPurchase, 0, {
                value: smallPurchase + POOL_CREATION_FEE,
            });

            await launchpad.connect(buyer2).buyExactIn(tokenAddress, smallPurchase, 0, {
                value: smallPurchase + POOL_CREATION_FEE,
            });

            // Verify pool is still not completed
            const pool = await launchpad.getPool(tokenAddress);
            expect(pool.isCompleted).to.be.false;
            expect(pool.realHypeReserves).to.be.lt(customThreshold);

            console.log("✅ Edge case verified: Pool remains incomplete with small transactions");
            console.log("  Current reserves:", ethers.formatEther(pool.realHypeReserves));
            console.log("  Threshold:", ethers.formatEther(customThreshold));
        });

        it("should handle immediate pool completion with large first buy", async function () {
            console.log("\n⚡ Speed Test: Immediate pool completion with large first buy");

            const lockDuration = MIN_LOCK_DURATION;
            // Use amount larger than threshold to immediately complete pool
            const largeBuyAmount = customThreshold + ethers.parseEther("0.02");
            const largeTokenAmount = ethers.parseUnits("5000000", 6); // 5M tokens
            const totalCreationValue = POOL_CREATION_FEE + largeBuyAmount;

            const createAndLockTx = await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    "Instant Complete Token",
                    "ICT",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    largeTokenAmount,
                    lockDuration,
                    { value: totalCreationValue }
                );

            const createReceipt = await createAndLockTx.wait();

            // Check for immediate completion events
            const poolCompletedEvent = createReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "PoolCompleted";
                } catch {
                    return false;
                }
            });

            // Extract token address
            const tokenCreatedEvent = createReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedTokenEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });
            const tokenAddress = parsedTokenEvent?.args[0];

            // Verify immediate completion
            const pool = await launchpad.getPool(tokenAddress);

            if (pool.isCompleted) {
                expect(poolCompletedEvent).to.not.be.undefined;
                console.log("✅ Pool completed immediately upon creation");

                // Verify UniswapV3 integration (check via V3PoolCreated event)
                const v3PoolCreatedEvent = createReceipt?.logs.find((log) => {
                    try {
                        const parsed = launchpad.interface.parseLog({
                            topics: log.topics as string[],
                            data: log.data,
                        });
                        return parsed?.name === "V3PoolCreated";
                    } catch {
                        return false;
                    }
                });

                if (v3PoolCreatedEvent) {
                    const parsedV3Event = launchpad.interface.parseLog({
                        topics: v3PoolCreatedEvent.topics as string[],
                        data: v3PoolCreatedEvent.data,
                    });
                    const v3PoolAddress = parsedV3Event?.args[1];
                    expect(v3PoolAddress).to.not.equal(ethers.ZeroAddress);
                    console.log("  UniswapV3 pool:", v3PoolAddress);
                }
            } else {
                console.log("ℹ️  Pool created but not completed immediately");
            }
        });
    });

    describe("TokenLock Integration Tests", function () {
        describe("Complete Lock-to-Withdraw Flow", function () {
            it("should execute complete flow: createAndLockFirstBuy → wait → withdraw", async function () {
                console.log("\nTokenLock Integration Test: createAndLockFirstBuy → withdraw");

                const lockDuration = MIN_LOCK_DURATION; // 1 hour
                const buyAmount = ethers.parseEther("0.05"); // ETH to spend
                const amountOut = ethers.parseUnits("5000000", 6); // 5M tokens to buy and lock
                const totalValue = POOL_CREATION_FEE + buyAmount;

                console.log("Step 1: Create token and lock first buy");
                console.log(
                    `  Lock duration: ${lockDuration} seconds (${lockDuration / 3600} hour)`
                );
                console.log(`  Buy amount: ${ethers.formatEther(buyAmount)} ETH`);
                console.log(`  Tokens to lock: ${ethers.formatUnits(amountOut, 6)} tokens`);

                // Create token and lock first buy
                const createTx = await launchpad
                    .connect(creator)
                    .createAndLockFirstBuy(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        amountOut,
                        lockDuration,
                        { value: totalValue }
                    );

                const createReceipt = await createTx.wait();
                console.log(
                    `✅ Token created and first buy locked, gas used: ${createReceipt?.gasUsed}`
                );

                // Get token address from event
                const tokenCreatedEvent = createReceipt?.logs.find((log) => {
                    try {
                        const parsed = launchpad.interface.parseLog({
                            topics: log.topics as string[],
                            data: log.data,
                        });
                        return parsed?.name === "TokenCreated";
                    } catch {
                        return false;
                    }
                });

                const parsedEvent = launchpad.interface.parseLog({
                    topics: tokenCreatedEvent!.topics as string[],
                    data: tokenCreatedEvent!.data,
                });

                const tokenAddress = parsedEvent?.args[0];
                const token = await ethers.getContractAt("LaunchpadToken", tokenAddress);

                console.log(`Token created at address: ${tokenAddress}`);

                // Verify lock was created
                const lockCount = await tokenLock.getLockCount();
                expect(lockCount).to.equal(1);
                console.log(`Lock created with ID: ${lockCount}`);

                // Get lock details
                const lockDetails = await tokenLock.getLock(lockCount);
                expect(lockDetails.token).to.equal(tokenAddress);
                expect(lockDetails.recipient).to.equal(creator.address);
                expect(lockDetails.locker).to.equal(await launchpad.getAddress());
                expect(lockDetails.closed).to.be.false;
                expect(lockDetails.amount).to.be.gt(0);

                const lockedAmount = lockDetails.amount;
                console.log(`Locked amount: ${ethers.formatUnits(lockedAmount, 6)} tokens`);
                console.log(
                    `Lock end time: ${new Date(Number(lockDetails.endTime) * 1000).toISOString()}`
                );

                // Verify tokens are in the lock contract
                const lockContractBalance = await token.balanceOf(await tokenLock.getAddress());
                expect(lockContractBalance).to.equal(lockedAmount);
                console.log(
                    `✅ Tokens successfully locked in contract: ${ethers.formatUnits(lockContractBalance, 6)}`
                );

                // Verify creator doesn't have direct access to tokens
                const creatorDirectBalance = await token.balanceOf(creator.address);
                expect(creatorDirectBalance).to.equal(0);
                console.log(
                    `Creator direct balance: ${ethers.formatUnits(creatorDirectBalance, 6)} (should be 0)`
                );

                console.log("\nStep 2: Verify lock is not withdrawable before expiry");
                const isWithdrawableBeforeExpiry = await tokenLock.isWithdrawable(lockCount);
                expect(isWithdrawableBeforeExpiry).to.be.false;
                console.log(`Lock withdrawable before expiry: ${isWithdrawableBeforeExpiry}`);

                // Try to withdraw before expiry (should fail)
                await expect(
                    tokenLock.connect(creator).withdraw(lockCount)
                ).to.be.revertedWithCustomError(tokenLock, "Unauthorized");
                console.log("✅ Withdrawal correctly blocked before lock expiry");

                console.log("\nStep 3: Fast forward time to after lock expiry");
                const timeToAdvance = lockDuration + 60; // Add 1 minute buffer
                await ethers.provider.send("evm_increaseTime", [timeToAdvance]);
                await ethers.provider.send("evm_mine", []);
                console.log(`Time advanced by ${timeToAdvance} seconds`);

                // Verify lock is now withdrawable
                const isWithdrawableAfterExpiry = await tokenLock.isWithdrawable(lockCount);
                expect(isWithdrawableAfterExpiry).to.be.true;
                console.log(`Lock withdrawable after expiry: ${isWithdrawableAfterExpiry}`);

                console.log("\nStep 4: Withdraw locked tokens");
                const creatorBalanceBeforeWithdraw = await token.balanceOf(creator.address);

                const withdrawTx = await tokenLock.connect(creator).withdraw(lockCount);
                const withdrawReceipt = await withdrawTx.wait();
                console.log(`✅ Withdrawal completed, gas used: ${withdrawReceipt?.gasUsed}`);

                // Verify withdrawal event
                await expect(withdrawTx)
                    .to.emit(tokenLock, "TokensWithdrawn")
                    .withArgs(lockCount, creator.address, creator.address, lockedAmount);

                // Verify tokens were transferred to creator
                const creatorBalanceAfterWithdraw = await token.balanceOf(creator.address);
                const tokensReceived = creatorBalanceAfterWithdraw - creatorBalanceBeforeWithdraw;
                expect(tokensReceived).to.equal(lockedAmount);
                console.log(`Tokens received by creator: ${ethers.formatUnits(tokensReceived, 6)}`);

                // Verify lock contract balance is now zero
                const lockContractBalanceAfter = await token.balanceOf(
                    await tokenLock.getAddress()
                );
                expect(lockContractBalanceAfter).to.equal(0);
                console.log(
                    `Lock contract balance after withdrawal: ${ethers.formatUnits(lockContractBalanceAfter, 6)}`
                );

                // Verify lock is marked as closed
                const lockDetailsAfter = await tokenLock.getLock(lockCount);
                expect(lockDetailsAfter.closed).to.be.true;
                console.log(`Lock closed status: ${lockDetailsAfter.closed}`);

                // Try to withdraw again (should fail)
                await expect(
                    tokenLock.connect(creator).withdraw(lockCount)
                ).to.be.revertedWithCustomError(tokenLock, "ContractClosed");
                console.log("✅ Double withdrawal correctly blocked");

                console.log("\nTokenLock integration test completed successfully!");
                console.log("Summary:");
                console.log(`  - Token created: ${tokenAddress}`);
                console.log(`  - Tokens locked: ${ethers.formatUnits(lockedAmount, 6)}`);
                console.log(`  - Lock duration: ${lockDuration} seconds`);
                console.log(`  - Tokens successfully withdrawn after expiry`);
            });

            it("should handle admin withdrawal of expired locks", async function () {
                console.log("\nTokenLock Integration Test: Admin withdrawal");

                const lockDuration = MIN_LOCK_DURATION;
                const buyAmount = ethers.parseEther("0.03");
                const amountOut = ethers.parseUnits("3000000", 6);
                const totalValue = POOL_CREATION_FEE + buyAmount;

                // Create token and lock
                const createTx = await launchpad
                    .connect(creator)
                    .createAndLockFirstBuy(
                        tokenName + " Admin",
                        tokenSymbol + "ADM",
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        amountOut,
                        lockDuration,
                        { value: totalValue }
                    );

                await createTx.wait();
                console.log("✅ Token created and locked");

                const lockCount = await tokenLock.getLockCount();
                const lockDetails = await tokenLock.getLock(lockCount);
                const lockedAmount = lockDetails.amount;

                // Fast forward time
                await ethers.provider.send("evm_increaseTime", [lockDuration + 60]);
                await ethers.provider.send("evm_mine", []);

                console.log("\nAdmin withdrawing on behalf of user");
                const creatorBalanceBefore = await ethers
                    .getContractAt("LaunchpadToken", lockDetails.token)
                    .then((token) => token.balanceOf(creator.address));

                // Admin (owner) withdraws the lock
                const withdrawTx = await tokenLock.connect(owner).withdraw(lockCount);
                await withdrawTx.wait();
                console.log("✅ Admin withdrawal completed");

                // Verify tokens went to the recipient (creator)
                const token = await ethers.getContractAt("LaunchpadToken", lockDetails.token);
                const creatorBalanceAfter = await token.balanceOf(creator.address);
                const tokensReceived = creatorBalanceAfter - creatorBalanceBefore;
                expect(tokensReceived).to.equal(lockedAmount);
                console.log(
                    `Tokens transferred to recipient: ${ethers.formatUnits(tokensReceived, 6)}`
                );

                console.log("Admin withdrawal test completed successfully!");
            });

            it("should handle multiple locks and selective withdrawal", async function () {
                console.log("\nTokenLock Integration Test: Multiple locks");

                // Create first lock
                const lockDuration1 = MIN_LOCK_DURATION;
                const buyAmount1 = ethers.parseEther("0.02");
                const amountOut1 = ethers.parseUnits("2000000", 6);
                const totalValue1 = POOL_CREATION_FEE + buyAmount1;

                const createTx1 = await launchpad
                    .connect(creator)
                    .createAndLockFirstBuy(
                        tokenName + " Multi1",
                        tokenSymbol + "M1",
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        amountOut1,
                        lockDuration1,
                        { value: totalValue1 }
                    );

                await createTx1.wait();
                console.log("✅ First lock created");

                // Create second lock with longer duration
                const lockDuration2 = MIN_LOCK_DURATION * 2; // 2 hours
                const buyAmount2 = ethers.parseEther("0.025");
                const amountOut2 = ethers.parseUnits("2500000", 6);
                const totalValue2 = POOL_CREATION_FEE + buyAmount2;

                const createTx2 = await launchpad
                    .connect(buyer1)
                    .createAndLockFirstBuy(
                        tokenName + " Multi2",
                        tokenSymbol + "M2",
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        amountOut2,
                        lockDuration2,
                        { value: totalValue2 }
                    );

                await createTx2.wait();
                console.log("✅ Second lock created");

                const totalLocks = await tokenLock.getLockCount();
                expect(totalLocks).to.equal(2);
                console.log(`Total locks created: ${totalLocks}`);

                // Fast forward to expire first lock only
                await ethers.provider.send("evm_increaseTime", [lockDuration1 + 60]);
                await ethers.provider.send("evm_mine", []);

                // Check withdrawable status
                const firstLockId = totalLocks - 1n; // First lock
                const secondLockId = totalLocks; // Second lock
                const lock1Withdrawable = await tokenLock.isWithdrawable(firstLockId);
                const lock2Withdrawable = await tokenLock.isWithdrawable(secondLockId);

                expect(lock1Withdrawable).to.be.true;
                expect(lock2Withdrawable).to.be.false;
                console.log(`Lock 1 withdrawable: ${lock1Withdrawable}`);
                console.log(`Lock 2 withdrawable: ${lock2Withdrawable}`);

                // Withdraw first lock
                const withdrawTx = await tokenLock.connect(creator).withdraw(firstLockId);
                await withdrawTx.wait();
                console.log("✅ First lock withdrawn");

                // Verify first lock is closed, second is still active
                const lock1DetailsAfter = await tokenLock.getLock(firstLockId);
                const lock2Details = await tokenLock.getLock(secondLockId);

                expect(lock1DetailsAfter.closed).to.be.true;
                expect(lock2Details.closed).to.be.false;
                console.log(`Lock 1 closed: ${lock1DetailsAfter.closed}`);
                console.log(`Lock 2 still active: ${!lock2Details.closed}`);

                // Try to withdraw second lock (should fail)
                await expect(
                    tokenLock.connect(buyer1).withdraw(secondLockId)
                ).to.be.revertedWithCustomError(tokenLock, "Unauthorized");
                console.log("✅ Second lock withdrawal correctly blocked (not expired)");

                console.log("Multiple locks test completed successfully!");
            });
        });
    });
});
