env:
    node: true
    es2021: true
parser: "@typescript-eslint/parser"
parserOptions:
    project: "tsconfig.json"
    ecmaVersion: 12
    sourceType: "module"
ignorePatterns: ["node_modules/**/*"]
plugins:
    - "@typescript-eslint"
root: true
extends:
    - "eslint:recommended"
    - "plugin:@typescript-eslint/eslint-recommended"
    - "plugin:@typescript-eslint/recommended"
    - "prettier"
rules:
    "@typescript-eslint/no-floating-promises":
        - "warn"
        - "ignoreIIFE": true
          "ignoreVoid": true
    "@typescript-eslint/no-inferrable-types": "off"
    "@typescript-eslint/no-unused-vars":
        - "warn"
        - "argsIgnorePattern": "_"
          "varsIgnorePattern": "_"
    no-unused-vars: "warn"
    no-extra-boolean-cast: "off"
    no-constant-condition: "off"
    no-async-promise-executor: "off"
    no-useless-escape: "warn"
