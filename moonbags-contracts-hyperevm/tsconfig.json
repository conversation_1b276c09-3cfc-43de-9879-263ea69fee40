{"compilerOptions": {"declaration": true, "declarationMap": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "lib": ["es2020"], "module": "commonjs", "moduleResolution": "node", "noImplicitAny": true, "removeComments": true, "resolveJsonModule": true, "sourceMap": true, "strict": true, "target": "es2020"}, "exclude": ["node_modules"], "files": ["./hardhat.config.ts"], "include": ["deploy/**/*", "scripts/**/*", "tasks/**/*", "test/**/*", "types/**/*", "utils/**/*", "config/**/*"]}