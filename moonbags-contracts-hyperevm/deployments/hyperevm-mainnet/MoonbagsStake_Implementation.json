{"address": "0x971d144C01158308a4dB7cd83Bf063259E2331f8", "abi": [{"inputs": [], "name": "AccountBalanceNotEnough", "type": "error"}, {"inputs": [], "name": "CreatorPoolAlreadyExists", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidCreator", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "InvalidTokenAddress", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "NotUpgrade", "type": "error"}, {"inputs": [], "name": "Only<PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "RewardToClaimNotValid", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "StakingAccountNotExist", "type": "error"}, {"inputs": [], "name": "StakingCreatorNotExist", "type": "error"}, {"inputs": [], "name": "StakingPoolAlreadyExists", "type": "error"}, {"inputs": [], "name": "StakingPoolNotExist", "type": "error"}, {"inputs": [], "name": "UnstakeDeadlineNotAllow", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "claimer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ClaimCreatorPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "claimer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isStakingAccountDeleted", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ClaimStakingPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "depositor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "DepositPoolCreatorEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "initializer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "InitializeCreatorPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "initializer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "InitializeStakingPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "staker", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "StakeEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "unstaker", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isStakingAccountDeleted", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "UnstakeEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "rewardUpdater", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isInitialRewards", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "UpdateRewardIndexEvent", "type": "event"}, {"inputs": [], "name": "DEFAULT_DENY_UNSTAKE_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "calculateRewardsEarned", "outputs": [{"internalType": "uint256", "name": "totalEarned", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "claimCreatorPool", "outputs": [{"internalType": "uint256", "name": "rewardAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "claimStakingPool", "outputs": [{"internalType": "uint256", "name": "rewardAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "config", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "uint256", "name": "denyUnstakeDuration", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "creatorPoolExists", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "creatorPools", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "depositCreatorPool", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "getCreatorPoolInfo", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "address", "name": "staker", "type": "address"}], "name": "getStakingAccountInfo", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "earned", "type": "uint256"}, {"internalType": "uint256", "name": "unstakeDeadline", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "getStakingPoolInfo", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "pendingInitialRewards", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "address", "name": "creator", "type": "address"}], "name": "initializeCreatorPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "initializeStakingPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "stakingAccountExists", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "stakingAccounts", "outputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "earned", "type": "uint256"}, {"internalType": "uint256", "name": "unstakeDeadline", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "stakingPoolExists", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "stakingPools", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "pendingInitialRewards", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "uint256", "name": "unstakeAmount", "type": "uint256"}], "name": "unstake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}, {"internalType": "uint256", "name": "newDenyUnstakeDuration", "type": "uint256"}], "name": "updateConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "updateRewardIndex", "outputs": [], "stateMutability": "payable", "type": "function"}], "transactionHash": "0xef0a9cd068eee0046bc1a82324f4020d2e88ea1c800c27904f99f957648e2389", "receipt": {"to": null, "from": "0xb6981f22cB9A48B12483B7Fcf512Abd0C8a2B3de", "contractAddress": "0x971d144C01158308a4dB7cd83Bf063259E2331f8", "transactionIndex": 0, "gasUsed": "1304041", "logsBloom": "0x************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************00000000", "blockHash": "0xa5bfe39c7a8fe6a2208a5574f2082c6be7579efce5a88ca02b43ff798e2924de", "transactionHash": "0xef0a9cd068eee0046bc1a82324f4020d2e88ea1c800c27904f99f957648e2389", "logs": [], "blockNumber": 7385988, "cumulativeGasUsed": "1304041", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "2d791615bdbd29bde3ba98a0e9c3b3ee", "metadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AccountBalanceNotEnough\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CreatorPoolAlreadyExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidCreator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidTokenAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotUpgrade\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyAdmin\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardToClaimNotValid\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StakingAccountNotExist\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StakingCreatorNotExist\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StakingPoolAlreadyExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StakingPoolNotExist\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnstakeDeadlineNotAllow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"claimer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"ClaimCreatorPoolEvent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"claimer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isStakingAccountDeleted\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"ClaimStakingPoolEvent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"depositor\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"DepositPoolCreatorEvent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"initializer\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"InitializeCreatorPoolEvent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"initializer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"InitializeStakingPoolEvent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"StakeEvent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"unstaker\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isStakingAccountDeleted\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"UnstakeEvent\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"rewardUpdater\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reward\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isInitialRewards\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"UpdateRewardIndexEvent\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DEFAULT_DENY_UNSTAKE_DURATION\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"calculateRewardsEarned\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalEarned\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"claimCreatorPool\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"rewardAmount\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"claimStakingPool\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"rewardAmount\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"config\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"denyUnstakeDuration\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"creatorPoolExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"exists\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"creatorPools\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"initializer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"totalRewards\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"depositCreatorPool\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"getCreatorPoolInfo\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"initializer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"totalRewards\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getStakingAccountInfo\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"rewardIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"earned\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"unstakeDeadline\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"getStakingPoolInfo\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"initializer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"totalSupply\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"rewardIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"pendingInitialRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"totalRewards\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"}],\"name\":\"initializeCreatorPool\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"initializeStakingPool\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"stake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"stakingAccountExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"exists\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"stakingAccounts\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"rewardIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"earned\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"unstakeDeadline\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"stakingPoolExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"exists\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"stakingPools\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"initializer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"totalSupply\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"rewardIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"pendingInitialRewards\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"totalRewards\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"unstakeAmount\",\"type\":\"uint256\"}],\"name\":\"unstake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newAdmin\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"newDenyUnstakeDuration\",\"type\":\"uint256\"}],\"name\":\"updateConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"stakingToken\",\"type\":\"address\"}],\"name\":\"updateRewardIndex\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"calculateRewardsEarned(address)\":{\"params\":{\"stakingToken\":\"The token associated with the staking pool\"},\"returns\":{\"totalEarned\":\"The total amount of rewards earned\"}},\"claimCreatorPool(address)\":{\"params\":{\"stakingToken\":\"The token associated with the creator pool\"},\"returns\":{\"rewardAmount\":\"The amount of HYPE claimed from the creator pool\"}},\"claimStakingPool(address)\":{\"params\":{\"stakingToken\":\"The token associated with the staking pool\"},\"returns\":{\"rewardAmount\":\"The amount of HYPE claimed as rewards\"}},\"creatorPoolExists(address)\":{\"params\":{\"token\":\"The token address\"},\"returns\":{\"exists\":\"True if the creator pool exists\"}},\"depositCreatorPool(address)\":{\"params\":{\"stakingToken\":\"The token associated with the creator pool\"}},\"getCreatorPoolInfo(address)\":{\"params\":{\"stakingToken\":\"The token associated with the creator pool\"},\"returns\":{\"creator\":\"Address of the creator\",\"initializer\":\"Address that initialized the pool\",\"totalRewards\":\"Total HYPE rewards for creator\"}},\"getStakingAccountInfo(address,address)\":{\"params\":{\"staker\":\"Address of the staker\",\"stakingToken\":\"The token associated with the staking pool\"},\"returns\":{\"balance\":\"Staked token balance\",\"earned\":\"Earned rewards ready to claim\",\"rewardIndex\":\"Last reward index when rewards were updated\",\"unstakeDeadline\":\"Timestamp when unstaking is allowed\"}},\"getStakingPoolInfo(address)\":{\"params\":{\"stakingToken\":\"The token associated with the staking pool\"},\"returns\":{\"initializer\":\"Address that initialized the pool\",\"pendingInitialRewards\":\"Pending initial rewards\",\"rewardIndex\":\"Current reward index\",\"totalRewards\":\"Total HYPE rewards in pool\",\"totalSupply\":\"Total amount of tokens staked\"}},\"initializeCreatorPool(address,address)\":{\"params\":{\"creator\":\"Address of the creator for this pool\",\"stakingToken\":\"The ERC20 token associated with this creator pool\"}},\"initializeStakingPool(address)\":{\"params\":{\"stakingToken\":\"The ERC20 token that will be staked in this pool\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"stake(address,uint256)\":{\"params\":{\"amount\":\"Amount of tokens to stake\",\"stakingToken\":\"The token to stake\"}},\"stakingAccountExists(address,address)\":{\"params\":{\"token\":\"The token address\",\"user\":\"The user address\"},\"returns\":{\"exists\":\"True if the staking account exists\"}},\"stakingPoolExists(address)\":{\"params\":{\"token\":\"The token address\"},\"returns\":{\"exists\":\"True if the staking pool exists\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"unstake(address,uint256)\":{\"params\":{\"stakingToken\":\"The token to unstake\",\"unstakeAmount\":\"Amount of tokens to unstake\"}},\"updateConfig(address,uint256)\":{\"params\":{\"newDenyUnstakeDuration\":\"New deny unstake duration\"}},\"updateRewardIndex(address)\":{\"params\":{\"stakingToken\":\"The token associated with the staking pool\"}}},\"title\":\"Moonbags Staking\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"calculateRewardsEarned(address)\":{\"notice\":\"Calculates the rewards earned by the caller for staking tokens\"},\"claimCreatorPool(address)\":{\"notice\":\"Claims rewards from a creator pool\"},\"claimStakingPool(address)\":{\"notice\":\"Claims rewards from a staking pool\"},\"creatorPoolExists(address)\":{\"notice\":\"Check if a creator pool exists for a token\"},\"depositCreatorPool(address)\":{\"notice\":\"Deposits HYPE rewards into a creator pool\"},\"getCreatorPoolInfo(address)\":{\"notice\":\"Get creator pool information\"},\"getStakingAccountInfo(address,address)\":{\"notice\":\"Get staking account information\"},\"getStakingPoolInfo(address)\":{\"notice\":\"Get staking pool information\"},\"initialize()\":{\"notice\":\"Initialize the contract\"},\"initializeCreatorPool(address,address)\":{\"notice\":\"Initializes a creator pool for a specific token\"},\"initializeStakingPool(address)\":{\"notice\":\"Initializes a new staking pool for a specific token\"},\"stake(address,uint256)\":{\"notice\":\"Stakes tokens in a staking pool\"},\"stakingAccountExists(address,address)\":{\"notice\":\"Check if a staking account exists for a user\"},\"stakingPoolExists(address)\":{\"notice\":\"Check if a staking pool exists for a token\"},\"unstake(address,uint256)\":{\"notice\":\"Unstakes tokens from a staking pool\"},\"updateConfig(address,uint256)\":{\"notice\":\"Update configuration\"},\"updateRewardIndex(address)\":{\"notice\":\"Updates the reward index of a staking pool by adding new rewards\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/MoonbagsStake.sol\":\"MoonbagsStake\"},\"evmVersion\":\"paris\",\"libraries\":{\":__CACHE_BREAKER__\":\"0x00000000d41867734bbee4c6863d9255b2b06ac1\"},\"metadata\":{\"bytecodeHash\":\"none\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {ContextUpgradeable} from \\\"../utils/ContextUpgradeable.sol\\\";\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * The initial owner is set to the address provided by the deployer. This can\\n * later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\\n    /// @custom:storage-location erc7201:openzeppelin.storage.Ownable\\n    struct OwnableStorage {\\n        address _owner;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.Ownable\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant OwnableStorageLocation = 0x9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300;\\n\\n    function _getOwnableStorage() private pure returns (OwnableStorage storage $) {\\n        assembly {\\n            $.slot := OwnableStorageLocation\\n        }\\n    }\\n\\n    /**\\n     * @dev The caller account is not authorized to perform an operation.\\n     */\\n    error OwnableUnauthorizedAccount(address account);\\n\\n    /**\\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\\n     */\\n    error OwnableInvalidOwner(address owner);\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\\n     */\\n    function __Ownable_init(address initialOwner) internal onlyInitializing {\\n        __Ownable_init_unchained(initialOwner);\\n    }\\n\\n    function __Ownable_init_unchained(address initialOwner) internal onlyInitializing {\\n        if (initialOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(initialOwner);\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        OwnableStorage storage $ = _getOwnableStorage();\\n        return $._owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        if (owner() != _msgSender()) {\\n            revert OwnableUnauthorizedAccount(_msgSender());\\n        }\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        if (newOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        OwnableStorage storage $ = _getOwnableStorage();\\n        address oldOwner = $._owner;\\n        $._owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (proxy/utils/Initializable.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\\n *\\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\\n * reused. This mechanism prevents re-execution of each \\\"step\\\" but allows the creation of new initialization steps in\\n * case an upgrade adds a module that needs to be initialized.\\n *\\n * For example:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```solidity\\n * contract MyToken is ERC20Upgradeable {\\n *     function initialize() initializer public {\\n *         __ERC20_init(\\\"MyToken\\\", \\\"MTK\\\");\\n *     }\\n * }\\n *\\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\\n *     function initializeV2() reinitializer(2) public {\\n *         __ERC20Permit_init(\\\"MyToken\\\");\\n *     }\\n * }\\n * ```\\n *\\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\\n *\\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\\n *\\n * [CAUTION]\\n * ====\\n * Avoid leaving a contract uninitialized.\\n *\\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```\\n * /// @custom:oz-upgrades-unsafe-allow constructor\\n * constructor() {\\n *     _disableInitializers();\\n * }\\n * ```\\n * ====\\n */\\nabstract contract Initializable {\\n    /**\\n     * @dev Storage of the initializable contract.\\n     *\\n     * It's implemented on a custom ERC-7201 namespace to reduce the risk of storage collisions\\n     * when using with upgradeable contracts.\\n     *\\n     * @custom:storage-location erc7201:openzeppelin.storage.Initializable\\n     */\\n    struct InitializableStorage {\\n        /**\\n         * @dev Indicates that the contract has been initialized.\\n         */\\n        uint64 _initialized;\\n        /**\\n         * @dev Indicates that the contract is in the process of being initialized.\\n         */\\n        bool _initializing;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.Initializable\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant INITIALIZABLE_STORAGE = 0xf0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00;\\n\\n    /**\\n     * @dev The contract is already initialized.\\n     */\\n    error InvalidInitialization();\\n\\n    /**\\n     * @dev The contract is not initializing.\\n     */\\n    error NotInitializing();\\n\\n    /**\\n     * @dev Triggered when the contract has been initialized or reinitialized.\\n     */\\n    event Initialized(uint64 version);\\n\\n    /**\\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\\n     * `onlyInitializing` functions can be used to initialize parent contracts.\\n     *\\n     * Similar to `reinitializer(1)`, except that in the context of a constructor an `initializer` may be invoked any\\n     * number of times. This behavior in the constructor can be useful during testing and is not expected to be used in\\n     * production.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier initializer() {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        // Cache values to avoid duplicated sloads\\n        bool isTopLevelCall = !$._initializing;\\n        uint64 initialized = $._initialized;\\n\\n        // Allowed calls:\\n        // - initialSetup: the contract is not in the initializing state and no previous version was\\n        //                 initialized\\n        // - construction: the contract is initialized at version 1 (no reinitialization) and the\\n        //                 current contract is just being deployed\\n        bool initialSetup = initialized == 0 && isTopLevelCall;\\n        bool construction = initialized == 1 && address(this).code.length == 0;\\n\\n        if (!initialSetup && !construction) {\\n            revert InvalidInitialization();\\n        }\\n        $._initialized = 1;\\n        if (isTopLevelCall) {\\n            $._initializing = true;\\n        }\\n        _;\\n        if (isTopLevelCall) {\\n            $._initializing = false;\\n            emit Initialized(1);\\n        }\\n    }\\n\\n    /**\\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\\n     * used to initialize parent contracts.\\n     *\\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\\n     * are added through upgrades and that require initialization.\\n     *\\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\\n     *\\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\\n     * a contract, executing them in the right order is up to the developer or operator.\\n     *\\n     * WARNING: Setting the version to 2**64 - 1 will prevent any future reinitialization.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier reinitializer(uint64 version) {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        if ($._initializing || $._initialized >= version) {\\n            revert InvalidInitialization();\\n        }\\n        $._initialized = version;\\n        $._initializing = true;\\n        _;\\n        $._initializing = false;\\n        emit Initialized(version);\\n    }\\n\\n    /**\\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\\n     */\\n    modifier onlyInitializing() {\\n        _checkInitializing();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Reverts if the contract is not in an initializing state. See {onlyInitializing}.\\n     */\\n    function _checkInitializing() internal view virtual {\\n        if (!_isInitializing()) {\\n            revert NotInitializing();\\n        }\\n    }\\n\\n    /**\\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\\n     * through proxies.\\n     *\\n     * Emits an {Initialized} event the first time it is successfully executed.\\n     */\\n    function _disableInitializers() internal virtual {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        if ($._initializing) {\\n            revert InvalidInitialization();\\n        }\\n        if ($._initialized != type(uint64).max) {\\n            $._initialized = type(uint64).max;\\n            emit Initialized(type(uint64).max);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\\n     */\\n    function _getInitializedVersion() internal view returns (uint64) {\\n        return _getInitializableStorage()._initialized;\\n    }\\n\\n    /**\\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\\n     */\\n    function _isInitializing() internal view returns (bool) {\\n        return _getInitializableStorage()._initializing;\\n    }\\n\\n    /**\\n     * @dev Pointer to storage slot. Allows integrators to override it with a custom storage location.\\n     *\\n     * NOTE: Consider following the ERC-7201 formula to derive storage locations.\\n     */\\n    function _initializableStorageSlot() internal pure virtual returns (bytes32) {\\n        return INITIALIZABLE_STORAGE;\\n    }\\n\\n    /**\\n     * @dev Returns a pointer to the storage namespace.\\n     */\\n    // solhint-disable-next-line var-name-mixedcase\\n    function _getInitializableStorage() private pure returns (InitializableStorage storage $) {\\n        bytes32 slot = _initializableStorageSlot();\\n        assembly {\\n            $.slot := slot\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\\n\\npragma solidity ^0.8.20;\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract ContextUpgradeable is Initializable {\\n    function __Context_init() internal onlyInitializing {\\n    }\\n\\n    function __Context_init_unchained() internal onlyInitializing {\\n    }\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    function _contextSuffixLength() internal view virtual returns (uint256) {\\n        return 0;\\n    }\\n}\\n\",\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\\n\\npragma solidity ^0.8.20;\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module that helps prevent reentrant calls to a function.\\n *\\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\\n * available, which can be applied to functions to make sure there are no nested\\n * (reentrant) calls to them.\\n *\\n * Note that because there is a single `nonReentrant` guard, functions marked as\\n * `nonReentrant` may not call one another. This can be worked around by making\\n * those functions `private`, and then adding `external` `nonReentrant` entry\\n * points to them.\\n *\\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\\n * consider using {ReentrancyGuardTransient} instead.\\n *\\n * TIP: If you would like to learn more about reentrancy and alternative ways\\n * to protect against it, check out our blog post\\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\\n */\\nabstract contract ReentrancyGuardUpgradeable is Initializable {\\n    // Booleans are more expensive than uint256 or any type that takes up a full\\n    // word because each write operation emits an extra SLOAD to first read the\\n    // slot's contents, replace the bits taken up by the boolean, and then write\\n    // back. This is the compiler's defense against contract upgrades and\\n    // pointer aliasing, and it cannot be disabled.\\n\\n    // The values being non-zero value makes deployment a bit more expensive,\\n    // but in exchange the refund on every call to nonReentrant will be lower in\\n    // amount. Since refunds are capped to a percentage of the total\\n    // transaction's gas, it is best to keep them low in cases like this one, to\\n    // increase the likelihood of the full refund coming into effect.\\n    uint256 private constant NOT_ENTERED = 1;\\n    uint256 private constant ENTERED = 2;\\n\\n    /// @custom:storage-location erc7201:openzeppelin.storage.ReentrancyGuard\\n    struct ReentrancyGuardStorage {\\n        uint256 _status;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.ReentrancyGuard\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant ReentrancyGuardStorageLocation = 0x9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00;\\n\\n    function _getReentrancyGuardStorage() private pure returns (ReentrancyGuardStorage storage $) {\\n        assembly {\\n            $.slot := ReentrancyGuardStorageLocation\\n        }\\n    }\\n\\n    /**\\n     * @dev Unauthorized reentrant call.\\n     */\\n    error ReentrancyGuardReentrantCall();\\n\\n    function __ReentrancyGuard_init() internal onlyInitializing {\\n        __ReentrancyGuard_init_unchained();\\n    }\\n\\n    function __ReentrancyGuard_init_unchained() internal onlyInitializing {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        $._status = NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and making it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        _nonReentrantBefore();\\n        _;\\n        _nonReentrantAfter();\\n    }\\n\\n    function _nonReentrantBefore() private {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\\n        if ($._status == ENTERED) {\\n            revert ReentrancyGuardReentrantCall();\\n        }\\n\\n        // Any calls to nonReentrant after this point will fail\\n        $._status = ENTERED;\\n    }\\n\\n    function _nonReentrantAfter() private {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        $._status = NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Returns true if the reentrancy guard is currently set to \\\"entered\\\", which indicates there is a\\n     * `nonReentrant` function in the call stack.\\n     */\\n    function _reentrancyGuardEntered() internal view returns (bool) {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        return $._status == ENTERED;\\n    }\\n}\\n\",\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (interfaces/IERC1363.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"./IERC20.sol\\\";\\nimport {IERC165} from \\\"./IERC165.sol\\\";\\n\\n/**\\n * @title IERC1363\\n * @dev Interface of the ERC-1363 standard as defined in the https://eips.ethereum.org/EIPS/eip-1363[ERC-1363].\\n *\\n * Defines an extension interface for ERC-20 tokens that supports executing code on a recipient contract\\n * after `transfer` or `transferFrom`, or code on a spender contract after `approve`, in a single transaction.\\n */\\ninterface IERC1363 is IERC20, IERC165 {\\n    /*\\n     * Note: the ERC-165 identifier for this interface is 0xb0202a11.\\n     * 0xb0202a11 ===\\n     *   bytes4(keccak256('transferAndCall(address,uint256)')) ^\\n     *   bytes4(keccak256('transferAndCall(address,uint256,bytes)')) ^\\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256)')) ^\\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256,bytes)')) ^\\n     *   bytes4(keccak256('approveAndCall(address,uint256)')) ^\\n     *   bytes4(keccak256('approveAndCall(address,uint256,bytes)'))\\n     */\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferAndCall(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @param data Additional data with no specified format, sent in call to `to`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferAndCall(address to, uint256 value, bytes calldata data) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param from The address which you want to send tokens from.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferFromAndCall(address from, address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param from The address which you want to send tokens from.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @param data Additional data with no specified format, sent in call to `to`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferFromAndCall(address from, address to, uint256 value, bytes calldata data) external returns (bool);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\\n     * @param spender The address which will spend the funds.\\n     * @param value The amount of tokens to be spent.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function approveAndCall(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\\n     * @param spender The address which will spend the funds.\\n     * @param value The amount of tokens to be spent.\\n     * @param data Additional data with no specified format, sent in call to `spender`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function approveAndCall(address spender, uint256 value, bytes calldata data) external returns (bool);\\n}\\n\",\"keccak256\":\"0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC165} from \\\"../utils/introspection/IERC165.sol\\\";\\n\",\"keccak256\":\"0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../token/ERC20/IERC20.sol\\\";\\n\",\"keccak256\":\"0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC-20 standard as defined in the ERC.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the value of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the value of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\\n     * allowance mechanism. `value` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\\n}\\n\",\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (token/ERC20/utils/SafeERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../IERC20.sol\\\";\\nimport {IERC1363} from \\\"../../../interfaces/IERC1363.sol\\\";\\n\\n/**\\n * @title SafeERC20\\n * @dev Wrappers around ERC-20 operations that throw on failure (when the token\\n * contract returns false). Tokens that return no value (and instead revert or\\n * throw on failure) are also supported, non-reverting calls are assumed to be\\n * successful.\\n * To use this library you can add a `using SafeERC20 for IERC20;` statement to your contract,\\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\\n */\\nlibrary SafeERC20 {\\n    /**\\n     * @dev An operation with an ERC-20 token failed.\\n     */\\n    error SafeERC20FailedOperation(address token);\\n\\n    /**\\n     * @dev Indicates a failed `decreaseAllowance` request.\\n     */\\n    error SafeERC20FailedDecreaseAllowance(address spender, uint256 currentAllowance, uint256 requestedDecrease);\\n\\n    /**\\n     * @dev Transfer `value` amount of `token` from the calling contract to `to`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful.\\n     */\\n    function safeTransfer(IERC20 token, address to, uint256 value) internal {\\n        _callOptionalReturn(token, abi.encodeCall(token.transfer, (to, value)));\\n    }\\n\\n    /**\\n     * @dev Transfer `value` amount of `token` from `from` to `to`, spending the approval given by `from` to the\\n     * calling contract. If `token` returns no value, non-reverting calls are assumed to be successful.\\n     */\\n    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\\n        _callOptionalReturn(token, abi.encodeCall(token.transferFrom, (from, to, value)));\\n    }\\n\\n    /**\\n     * @dev Variant of {safeTransfer} that returns a bool instead of reverting if the operation is not successful.\\n     */\\n    function trySafeTransfer(IERC20 token, address to, uint256 value) internal returns (bool) {\\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transfer, (to, value)));\\n    }\\n\\n    /**\\n     * @dev Variant of {safeTransferFrom} that returns a bool instead of reverting if the operation is not successful.\\n     */\\n    function trySafeTransferFrom(IERC20 token, address from, address to, uint256 value) internal returns (bool) {\\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transferFrom, (from, to, value)));\\n    }\\n\\n    /**\\n     * @dev Increase the calling contract's allowance toward `spender` by `value`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful.\\n     *\\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \\\"client\\\"\\n     * smart contract uses ERC-7674 to set temporary allowances, then the \\\"client\\\" smart contract should avoid using\\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\\n     */\\n    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n        uint256 oldAllowance = token.allowance(address(this), spender);\\n        forceApprove(token, spender, oldAllowance + value);\\n    }\\n\\n    /**\\n     * @dev Decrease the calling contract's allowance toward `spender` by `requestedDecrease`. If `token` returns no\\n     * value, non-reverting calls are assumed to be successful.\\n     *\\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \\\"client\\\"\\n     * smart contract uses ERC-7674 to set temporary allowances, then the \\\"client\\\" smart contract should avoid using\\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\\n     */\\n    function safeDecreaseAllowance(IERC20 token, address spender, uint256 requestedDecrease) internal {\\n        unchecked {\\n            uint256 currentAllowance = token.allowance(address(this), spender);\\n            if (currentAllowance < requestedDecrease) {\\n                revert SafeERC20FailedDecreaseAllowance(spender, currentAllowance, requestedDecrease);\\n            }\\n            forceApprove(token, spender, currentAllowance - requestedDecrease);\\n        }\\n    }\\n\\n    /**\\n     * @dev Set the calling contract's allowance toward `spender` to `value`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful. Meant to be used with tokens that require the approval\\n     * to be set to zero before setting it to a non-zero value, such as USDT.\\n     *\\n     * NOTE: If the token implements ERC-7674, this function will not modify any temporary allowance. This function\\n     * only sets the \\\"standard\\\" allowance. Any temporary allowance will remain active, in addition to the value being\\n     * set here.\\n     */\\n    function forceApprove(IERC20 token, address spender, uint256 value) internal {\\n        bytes memory approvalCall = abi.encodeCall(token.approve, (spender, value));\\n\\n        if (!_callOptionalReturnBool(token, approvalCall)) {\\n            _callOptionalReturn(token, abi.encodeCall(token.approve, (spender, 0)));\\n            _callOptionalReturn(token, approvalCall);\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} transferAndCall, with a fallback to the simple {ERC20} transfer if the target has no\\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function transferAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\\n        if (to.code.length == 0) {\\n            safeTransfer(token, to, value);\\n        } else if (!token.transferAndCall(to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} transferFromAndCall, with a fallback to the simple {ERC20} transferFrom if the target\\n     * has no code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function transferFromAndCallRelaxed(\\n        IERC1363 token,\\n        address from,\\n        address to,\\n        uint256 value,\\n        bytes memory data\\n    ) internal {\\n        if (to.code.length == 0) {\\n            safeTransferFrom(token, from, to, value);\\n        } else if (!token.transferFromAndCall(from, to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} approveAndCall, with a fallback to the simple {ERC20} approve if the target has no\\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * NOTE: When the recipient address (`to`) has no code (i.e. is an EOA), this function behaves as {forceApprove}.\\n     * Opposedly, when the recipient address (`to`) has code, this function only attempts to call {ERC1363-approveAndCall}\\n     * once without retrying, and relies on the returned value to be true.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function approveAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\\n        if (to.code.length == 0) {\\n            forceApprove(token, to, value);\\n        } else if (!token.approveAndCall(to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     *\\n     * This is a variant of {_callOptionalReturnBool} that reverts if call fails to meet the requirements.\\n     */\\n    function _callOptionalReturn(IERC20 token, bytes memory data) private {\\n        uint256 returnSize;\\n        uint256 returnValue;\\n        assembly (\\\"memory-safe\\\") {\\n            let success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\\n            // bubble errors\\n            if iszero(success) {\\n                let ptr := mload(0x40)\\n                returndatacopy(ptr, 0, returndatasize())\\n                revert(ptr, returndatasize())\\n            }\\n            returnSize := returndatasize()\\n            returnValue := mload(0)\\n        }\\n\\n        if (returnSize == 0 ? address(token).code.length == 0 : returnValue != 1) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     *\\n     * This is a variant of {_callOptionalReturn} that silently catches all reverts and returns a bool instead.\\n     */\\n    function _callOptionalReturnBool(IERC20 token, bytes memory data) private returns (bool) {\\n        bool success;\\n        uint256 returnSize;\\n        uint256 returnValue;\\n        assembly (\\\"memory-safe\\\") {\\n            success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\\n            returnSize := returndatasize()\\n            returnValue := mload(0)\\n        }\\n        return success && (returnSize == 0 ? address(token).code.length > 0 : returnValue == 1);\\n    }\\n}\\n\",\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Panic.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/Panic.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Helper library for emitting standardized panic codes.\\n *\\n * ```solidity\\n * contract Example {\\n *      using Panic for uint256;\\n *\\n *      // Use any of the declared internal constants\\n *      function foo() { Panic.GENERIC.panic(); }\\n *\\n *      // Alternatively\\n *      function foo() { Panic.panic(Panic.GENERIC); }\\n * }\\n * ```\\n *\\n * Follows the list from https://github.com/ethereum/solidity/blob/v0.8.24/libsolutil/ErrorCodes.h[libsolutil].\\n *\\n * _Available since v5.1._\\n */\\n// slither-disable-next-line unused-state\\nlibrary Panic {\\n    /// @dev generic / unspecified error\\n    uint256 internal constant GENERIC = 0x00;\\n    /// @dev used by the assert() builtin\\n    uint256 internal constant ASSERT = 0x01;\\n    /// @dev arithmetic underflow or overflow\\n    uint256 internal constant UNDER_OVERFLOW = 0x11;\\n    /// @dev division or modulo by zero\\n    uint256 internal constant DIVISION_BY_ZERO = 0x12;\\n    /// @dev enum conversion error\\n    uint256 internal constant ENUM_CONVERSION_ERROR = 0x21;\\n    /// @dev invalid encoding in storage\\n    uint256 internal constant STORAGE_ENCODING_ERROR = 0x22;\\n    /// @dev empty array pop\\n    uint256 internal constant EMPTY_ARRAY_POP = 0x31;\\n    /// @dev array out of bounds access\\n    uint256 internal constant ARRAY_OUT_OF_BOUNDS = 0x32;\\n    /// @dev resource error (too large allocation or too large array)\\n    uint256 internal constant RESOURCE_ERROR = 0x41;\\n    /// @dev calling invalid internal function\\n    uint256 internal constant INVALID_INTERNAL_FUNCTION = 0x51;\\n\\n    /// @dev Reverts with a panic code. Recommended to use with\\n    /// the internal constants with predefined codes.\\n    function panic(uint256 code) internal pure {\\n        assembly (\\\"memory-safe\\\") {\\n            mstore(0x00, 0x4e487b71)\\n            mstore(0x20, code)\\n            revert(0x1c, 0x24)\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC-165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[ERC].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/Math.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (utils/math/Math.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {Panic} from \\\"../Panic.sol\\\";\\nimport {SafeCast} from \\\"./SafeCast.sol\\\";\\n\\n/**\\n * @dev Standard math utilities missing in the Solidity language.\\n */\\nlibrary Math {\\n    enum Rounding {\\n        Floor, // Toward negative infinity\\n        Ceil, // Toward positive infinity\\n        Trunc, // Toward zero\\n        Expand // Away from zero\\n    }\\n\\n    /**\\n     * @dev Return the 512-bit addition of two uint256.\\n     *\\n     * The result is stored in two 256 variables such that sum = high * 2\\u00b2\\u2075\\u2076 + low.\\n     */\\n    function add512(uint256 a, uint256 b) internal pure returns (uint256 high, uint256 low) {\\n        assembly (\\\"memory-safe\\\") {\\n            low := add(a, b)\\n            high := lt(low, a)\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the 512-bit multiplication of two uint256.\\n     *\\n     * The result is stored in two 256 variables such that product = high * 2\\u00b2\\u2075\\u2076 + low.\\n     */\\n    function mul512(uint256 a, uint256 b) internal pure returns (uint256 high, uint256 low) {\\n        // 512-bit multiply [high low] = x * y. Compute the product mod 2\\u00b2\\u2075\\u2076 and mod 2\\u00b2\\u2075\\u2076 - 1, then use\\n        // the Chinese Remainder Theorem to reconstruct the 512 bit result. The result is stored in two 256\\n        // variables such that product = high * 2\\u00b2\\u2075\\u2076 + low.\\n        assembly (\\\"memory-safe\\\") {\\n            let mm := mulmod(a, b, not(0))\\n            low := mul(a, b)\\n            high := sub(sub(mm, low), lt(mm, low))\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the addition of two unsigned integers, with a success flag (no overflow).\\n     */\\n    function tryAdd(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            uint256 c = a + b;\\n            success = c >= a;\\n            result = c * SafeCast.toUint(success);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, with a success flag (no overflow).\\n     */\\n    function trySub(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            uint256 c = a - b;\\n            success = c <= a;\\n            result = c * SafeCast.toUint(success);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the multiplication of two unsigned integers, with a success flag (no overflow).\\n     */\\n    function tryMul(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            uint256 c = a * b;\\n            assembly (\\\"memory-safe\\\") {\\n                // Only true when the multiplication doesn't overflow\\n                // (c / a == b) || (a == 0)\\n                success := or(eq(div(c, a), b), iszero(a))\\n            }\\n            // equivalent to: success ? c : 0\\n            result = c * SafeCast.toUint(success);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the division of two unsigned integers, with a success flag (no division by zero).\\n     */\\n    function tryDiv(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            success = b > 0;\\n            assembly (\\\"memory-safe\\\") {\\n                // The `DIV` opcode returns zero when the denominator is 0.\\n                result := div(a, b)\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers, with a success flag (no division by zero).\\n     */\\n    function tryMod(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            success = b > 0;\\n            assembly (\\\"memory-safe\\\") {\\n                // The `MOD` opcode returns zero when the denominator is 0.\\n                result := mod(a, b)\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Unsigned saturating addition, bounds to `2\\u00b2\\u2075\\u2076 - 1` instead of overflowing.\\n     */\\n    function saturatingAdd(uint256 a, uint256 b) internal pure returns (uint256) {\\n        (bool success, uint256 result) = tryAdd(a, b);\\n        return ternary(success, result, type(uint256).max);\\n    }\\n\\n    /**\\n     * @dev Unsigned saturating subtraction, bounds to zero instead of overflowing.\\n     */\\n    function saturatingSub(uint256 a, uint256 b) internal pure returns (uint256) {\\n        (, uint256 result) = trySub(a, b);\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Unsigned saturating multiplication, bounds to `2\\u00b2\\u2075\\u2076 - 1` instead of overflowing.\\n     */\\n    function saturatingMul(uint256 a, uint256 b) internal pure returns (uint256) {\\n        (bool success, uint256 result) = tryMul(a, b);\\n        return ternary(success, result, type(uint256).max);\\n    }\\n\\n    /**\\n     * @dev Branchless ternary evaluation for `a ? b : c`. Gas costs are constant.\\n     *\\n     * IMPORTANT: This function may reduce bytecode size and consume less gas when used standalone.\\n     * However, the compiler may optimize Solidity ternary operations (i.e. `a ? b : c`) to only compute\\n     * one branch when needed, making this function more expensive.\\n     */\\n    function ternary(bool condition, uint256 a, uint256 b) internal pure returns (uint256) {\\n        unchecked {\\n            // branchless ternary works because:\\n            // b ^ (a ^ b) == a\\n            // b ^ 0 == b\\n            return b ^ ((a ^ b) * SafeCast.toUint(condition));\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the largest of two numbers.\\n     */\\n    function max(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return ternary(a > b, a, b);\\n    }\\n\\n    /**\\n     * @dev Returns the smallest of two numbers.\\n     */\\n    function min(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return ternary(a < b, a, b);\\n    }\\n\\n    /**\\n     * @dev Returns the average of two numbers. The result is rounded towards\\n     * zero.\\n     */\\n    function average(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b) / 2 can overflow.\\n        return (a & b) + (a ^ b) / 2;\\n    }\\n\\n    /**\\n     * @dev Returns the ceiling of the division of two numbers.\\n     *\\n     * This differs from standard division with `/` in that it rounds towards infinity instead\\n     * of rounding towards zero.\\n     */\\n    function ceilDiv(uint256 a, uint256 b) internal pure returns (uint256) {\\n        if (b == 0) {\\n            // Guarantee the same behavior as in a regular Solidity division.\\n            Panic.panic(Panic.DIVISION_BY_ZERO);\\n        }\\n\\n        // The following calculation ensures accurate ceiling division without overflow.\\n        // Since a is non-zero, (a - 1) / b will not overflow.\\n        // The largest possible result occurs when (a - 1) / b is type(uint256).max,\\n        // but the largest value we can obtain is type(uint256).max - 1, which happens\\n        // when a = type(uint256).max and b = 1.\\n        unchecked {\\n            return SafeCast.toUint(a > 0) * ((a - 1) / b + 1);\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates floor(x * y / denominator) with full precision. Throws if result overflows a uint256 or\\n     * denominator == 0.\\n     *\\n     * Original credit to Remco Bloemen under MIT license (https://xn--2-umb.com/21/muldiv) with further edits by\\n     * Uniswap Labs also under MIT license.\\n     */\\n    function mulDiv(uint256 x, uint256 y, uint256 denominator) internal pure returns (uint256 result) {\\n        unchecked {\\n            (uint256 high, uint256 low) = mul512(x, y);\\n\\n            // Handle non-overflow cases, 256 by 256 division.\\n            if (high == 0) {\\n                // Solidity will revert if denominator == 0, unlike the div opcode on its own.\\n                // The surrounding unchecked block does not change this fact.\\n                // See https://docs.soliditylang.org/en/latest/control-structures.html#checked-or-unchecked-arithmetic.\\n                return low / denominator;\\n            }\\n\\n            // Make sure the result is less than 2\\u00b2\\u2075\\u2076. Also prevents denominator == 0.\\n            if (denominator <= high) {\\n                Panic.panic(ternary(denominator == 0, Panic.DIVISION_BY_ZERO, Panic.UNDER_OVERFLOW));\\n            }\\n\\n            ///////////////////////////////////////////////\\n            // 512 by 256 division.\\n            ///////////////////////////////////////////////\\n\\n            // Make division exact by subtracting the remainder from [high low].\\n            uint256 remainder;\\n            assembly (\\\"memory-safe\\\") {\\n                // Compute remainder using mulmod.\\n                remainder := mulmod(x, y, denominator)\\n\\n                // Subtract 256 bit number from 512 bit number.\\n                high := sub(high, gt(remainder, low))\\n                low := sub(low, remainder)\\n            }\\n\\n            // Factor powers of two out of denominator and compute largest power of two divisor of denominator.\\n            // Always >= 1. See https://cs.stackexchange.com/q/138556/92363.\\n\\n            uint256 twos = denominator & (0 - denominator);\\n            assembly (\\\"memory-safe\\\") {\\n                // Divide denominator by twos.\\n                denominator := div(denominator, twos)\\n\\n                // Divide [high low] by twos.\\n                low := div(low, twos)\\n\\n                // Flip twos such that it is 2\\u00b2\\u2075\\u2076 / twos. If twos is zero, then it becomes one.\\n                twos := add(div(sub(0, twos), twos), 1)\\n            }\\n\\n            // Shift in bits from high into low.\\n            low |= high * twos;\\n\\n            // Invert denominator mod 2\\u00b2\\u2075\\u2076. Now that denominator is an odd number, it has an inverse modulo 2\\u00b2\\u2075\\u2076 such\\n            // that denominator * inv \\u2261 1 mod 2\\u00b2\\u2075\\u2076. Compute the inverse by starting with a seed that is correct for\\n            // four bits. That is, denominator * inv \\u2261 1 mod 2\\u2074.\\n            uint256 inverse = (3 * denominator) ^ 2;\\n\\n            // Use the Newton-Raphson iteration to improve the precision. Thanks to Hensel's lifting lemma, this also\\n            // works in modular arithmetic, doubling the correct bits in each step.\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u2078\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b9\\u2076\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b3\\u00b2\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u2076\\u2074\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b9\\u00b2\\u2078\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b2\\u2075\\u2076\\n\\n            // Because the division is now exact we can divide by multiplying with the modular inverse of denominator.\\n            // This will give us the correct result modulo 2\\u00b2\\u2075\\u2076. Since the preconditions guarantee that the outcome is\\n            // less than 2\\u00b2\\u2075\\u2076, this is the final result. We don't need to compute the high bits of the result and high\\n            // is no longer required.\\n            result = low * inverse;\\n            return result;\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates x * y / denominator with full precision, following the selected rounding direction.\\n     */\\n    function mulDiv(uint256 x, uint256 y, uint256 denominator, Rounding rounding) internal pure returns (uint256) {\\n        return mulDiv(x, y, denominator) + SafeCast.toUint(unsignedRoundsUp(rounding) && mulmod(x, y, denominator) > 0);\\n    }\\n\\n    /**\\n     * @dev Calculates floor(x * y >> n) with full precision. Throws if result overflows a uint256.\\n     */\\n    function mulShr(uint256 x, uint256 y, uint8 n) internal pure returns (uint256 result) {\\n        unchecked {\\n            (uint256 high, uint256 low) = mul512(x, y);\\n            if (high >= 1 << n) {\\n                Panic.panic(Panic.UNDER_OVERFLOW);\\n            }\\n            return (high << (256 - n)) | (low >> n);\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates x * y >> n with full precision, following the selected rounding direction.\\n     */\\n    function mulShr(uint256 x, uint256 y, uint8 n, Rounding rounding) internal pure returns (uint256) {\\n        return mulShr(x, y, n) + SafeCast.toUint(unsignedRoundsUp(rounding) && mulmod(x, y, 1 << n) > 0);\\n    }\\n\\n    /**\\n     * @dev Calculate the modular multiplicative inverse of a number in Z/nZ.\\n     *\\n     * If n is a prime, then Z/nZ is a field. In that case all elements are inversible, except 0.\\n     * If n is not a prime, then Z/nZ is not a field, and some elements might not be inversible.\\n     *\\n     * If the input value is not inversible, 0 is returned.\\n     *\\n     * NOTE: If you know for sure that n is (big) a prime, it may be cheaper to use Fermat's little theorem and get the\\n     * inverse using `Math.modExp(a, n - 2, n)`. See {invModPrime}.\\n     */\\n    function invMod(uint256 a, uint256 n) internal pure returns (uint256) {\\n        unchecked {\\n            if (n == 0) return 0;\\n\\n            // The inverse modulo is calculated using the Extended Euclidean Algorithm (iterative version)\\n            // Used to compute integers x and y such that: ax + ny = gcd(a, n).\\n            // When the gcd is 1, then the inverse of a modulo n exists and it's x.\\n            // ax + ny = 1\\n            // ax = 1 + (-y)n\\n            // ax \\u2261 1 (mod n) # x is the inverse of a modulo n\\n\\n            // If the remainder is 0 the gcd is n right away.\\n            uint256 remainder = a % n;\\n            uint256 gcd = n;\\n\\n            // Therefore the initial coefficients are:\\n            // ax + ny = gcd(a, n) = n\\n            // 0a + 1n = n\\n            int256 x = 0;\\n            int256 y = 1;\\n\\n            while (remainder != 0) {\\n                uint256 quotient = gcd / remainder;\\n\\n                (gcd, remainder) = (\\n                    // The old remainder is the next gcd to try.\\n                    remainder,\\n                    // Compute the next remainder.\\n                    // Can't overflow given that (a % gcd) * (gcd // (a % gcd)) <= gcd\\n                    // where gcd is at most n (capped to type(uint256).max)\\n                    gcd - remainder * quotient\\n                );\\n\\n                (x, y) = (\\n                    // Increment the coefficient of a.\\n                    y,\\n                    // Decrement the coefficient of n.\\n                    // Can overflow, but the result is casted to uint256 so that the\\n                    // next value of y is \\\"wrapped around\\\" to a value between 0 and n - 1.\\n                    x - y * int256(quotient)\\n                );\\n            }\\n\\n            if (gcd != 1) return 0; // No inverse exists.\\n            return ternary(x < 0, n - uint256(-x), uint256(x)); // Wrap the result if it's negative.\\n        }\\n    }\\n\\n    /**\\n     * @dev Variant of {invMod}. More efficient, but only works if `p` is known to be a prime greater than `2`.\\n     *\\n     * From https://en.wikipedia.org/wiki/Fermat%27s_little_theorem[Fermat's little theorem], we know that if p is\\n     * prime, then `a**(p-1) \\u2261 1 mod p`. As a consequence, we have `a * a**(p-2) \\u2261 1 mod p`, which means that\\n     * `a**(p-2)` is the modular multiplicative inverse of a in Fp.\\n     *\\n     * NOTE: this function does NOT check that `p` is a prime greater than `2`.\\n     */\\n    function invModPrime(uint256 a, uint256 p) internal view returns (uint256) {\\n        unchecked {\\n            return Math.modExp(a, p - 2, p);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the modular exponentiation of the specified base, exponent and modulus (b ** e % m)\\n     *\\n     * Requirements:\\n     * - modulus can't be zero\\n     * - underlying staticcall to precompile must succeed\\n     *\\n     * IMPORTANT: The result is only valid if the underlying call succeeds. When using this function, make\\n     * sure the chain you're using it on supports the precompiled contract for modular exponentiation\\n     * at address 0x05 as specified in https://eips.ethereum.org/EIPS/eip-198[EIP-198]. Otherwise,\\n     * the underlying function will succeed given the lack of a revert, but the result may be incorrectly\\n     * interpreted as 0.\\n     */\\n    function modExp(uint256 b, uint256 e, uint256 m) internal view returns (uint256) {\\n        (bool success, uint256 result) = tryModExp(b, e, m);\\n        if (!success) {\\n            Panic.panic(Panic.DIVISION_BY_ZERO);\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Returns the modular exponentiation of the specified base, exponent and modulus (b ** e % m).\\n     * It includes a success flag indicating if the operation succeeded. Operation will be marked as failed if trying\\n     * to operate modulo 0 or if the underlying precompile reverted.\\n     *\\n     * IMPORTANT: The result is only valid if the success flag is true. When using this function, make sure the chain\\n     * you're using it on supports the precompiled contract for modular exponentiation at address 0x05 as specified in\\n     * https://eips.ethereum.org/EIPS/eip-198[EIP-198]. Otherwise, the underlying function will succeed given the lack\\n     * of a revert, but the result may be incorrectly interpreted as 0.\\n     */\\n    function tryModExp(uint256 b, uint256 e, uint256 m) internal view returns (bool success, uint256 result) {\\n        if (m == 0) return (false, 0);\\n        assembly (\\\"memory-safe\\\") {\\n            let ptr := mload(0x40)\\n            // | Offset    | Content    | Content (Hex)                                                      |\\n            // |-----------|------------|--------------------------------------------------------------------|\\n            // | 0x00:0x1f | size of b  | 0x******************************************************0000000020 |\\n            // | 0x20:0x3f | size of e  | 0x******************************************************0000000020 |\\n            // | 0x40:0x5f | size of m  | 0x******************************************************0000000020 |\\n            // | 0x60:0x7f | value of b | 0x<.............................................................b> |\\n            // | 0x80:0x9f | value of e | 0x<.............................................................e> |\\n            // | 0xa0:0xbf | value of m | 0x<.............................................................m> |\\n            mstore(ptr, 0x20)\\n            mstore(add(ptr, 0x20), 0x20)\\n            mstore(add(ptr, 0x40), 0x20)\\n            mstore(add(ptr, 0x60), b)\\n            mstore(add(ptr, 0x80), e)\\n            mstore(add(ptr, 0xa0), m)\\n\\n            // Given the result < m, it's guaranteed to fit in 32 bytes,\\n            // so we can use the memory scratch space located at offset 0.\\n            success := staticcall(gas(), 0x05, ptr, 0xc0, 0x00, 0x20)\\n            result := mload(0x00)\\n        }\\n    }\\n\\n    /**\\n     * @dev Variant of {modExp} that supports inputs of arbitrary length.\\n     */\\n    function modExp(bytes memory b, bytes memory e, bytes memory m) internal view returns (bytes memory) {\\n        (bool success, bytes memory result) = tryModExp(b, e, m);\\n        if (!success) {\\n            Panic.panic(Panic.DIVISION_BY_ZERO);\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Variant of {tryModExp} that supports inputs of arbitrary length.\\n     */\\n    function tryModExp(\\n        bytes memory b,\\n        bytes memory e,\\n        bytes memory m\\n    ) internal view returns (bool success, bytes memory result) {\\n        if (_zeroBytes(m)) return (false, new bytes(0));\\n\\n        uint256 mLen = m.length;\\n\\n        // Encode call args in result and move the free memory pointer\\n        result = abi.encodePacked(b.length, e.length, mLen, b, e, m);\\n\\n        assembly (\\\"memory-safe\\\") {\\n            let dataPtr := add(result, 0x20)\\n            // Write result on top of args to avoid allocating extra memory.\\n            success := staticcall(gas(), 0x05, dataPtr, mload(result), dataPtr, mLen)\\n            // Overwrite the length.\\n            // result.length > returndatasize() is guaranteed because returndatasize() == m.length\\n            mstore(result, mLen)\\n            // Set the memory pointer after the returned data.\\n            mstore(0x40, add(dataPtr, mLen))\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns whether the provided byte array is zero.\\n     */\\n    function _zeroBytes(bytes memory byteArray) private pure returns (bool) {\\n        for (uint256 i = 0; i < byteArray.length; ++i) {\\n            if (byteArray[i] != 0) {\\n                return false;\\n            }\\n        }\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Returns the square root of a number. If the number is not a perfect square, the value is rounded\\n     * towards zero.\\n     *\\n     * This method is based on Newton's method for computing square roots; the algorithm is restricted to only\\n     * using integer operations.\\n     */\\n    function sqrt(uint256 a) internal pure returns (uint256) {\\n        unchecked {\\n            // Take care of easy edge cases when a == 0 or a == 1\\n            if (a <= 1) {\\n                return a;\\n            }\\n\\n            // In this function, we use Newton's method to get a root of `f(x) := x\\u00b2 - a`. It involves building a\\n            // sequence x_n that converges toward sqrt(a). For each iteration x_n, we also define the error between\\n            // the current value as `\\u03b5_n = | x_n - sqrt(a) |`.\\n            //\\n            // For our first estimation, we consider `e` the smallest power of 2 which is bigger than the square root\\n            // of the target. (i.e. `2**(e-1) \\u2264 sqrt(a) < 2**e`). We know that `e \\u2264 128` because `(2\\u00b9\\u00b2\\u2078)\\u00b2 = 2\\u00b2\\u2075\\u2076` is\\n            // bigger than any uint256.\\n            //\\n            // By noticing that\\n            // `2**(e-1) \\u2264 sqrt(a) < 2**e \\u2192 (2**(e-1))\\u00b2 \\u2264 a < (2**e)\\u00b2 \\u2192 2**(2*e-2) \\u2264 a < 2**(2*e)`\\n            // we can deduce that `e - 1` is `log2(a) / 2`. We can thus compute `x_n = 2**(e-1)` using a method similar\\n            // to the msb function.\\n            uint256 aa = a;\\n            uint256 xn = 1;\\n\\n            if (aa >= (1 << 128)) {\\n                aa >>= 128;\\n                xn <<= 64;\\n            }\\n            if (aa >= (1 << 64)) {\\n                aa >>= 64;\\n                xn <<= 32;\\n            }\\n            if (aa >= (1 << 32)) {\\n                aa >>= 32;\\n                xn <<= 16;\\n            }\\n            if (aa >= (1 << 16)) {\\n                aa >>= 16;\\n                xn <<= 8;\\n            }\\n            if (aa >= (1 << 8)) {\\n                aa >>= 8;\\n                xn <<= 4;\\n            }\\n            if (aa >= (1 << 4)) {\\n                aa >>= 4;\\n                xn <<= 2;\\n            }\\n            if (aa >= (1 << 2)) {\\n                xn <<= 1;\\n            }\\n\\n            // We now have x_n such that `x_n = 2**(e-1) \\u2264 sqrt(a) < 2**e = 2 * x_n`. This implies \\u03b5_n \\u2264 2**(e-1).\\n            //\\n            // We can refine our estimation by noticing that the middle of that interval minimizes the error.\\n            // If we move x_n to equal 2**(e-1) + 2**(e-2), then we reduce the error to \\u03b5_n \\u2264 2**(e-2).\\n            // This is going to be our x_0 (and \\u03b5_0)\\n            xn = (3 * xn) >> 1; // \\u03b5_0 := | x_0 - sqrt(a) | \\u2264 2**(e-2)\\n\\n            // From here, Newton's method give us:\\n            // x_{n+1} = (x_n + a / x_n) / 2\\n            //\\n            // One should note that:\\n            // x_{n+1}\\u00b2 - a = ((x_n + a / x_n) / 2)\\u00b2 - a\\n            //              = ((x_n\\u00b2 + a) / (2 * x_n))\\u00b2 - a\\n            //              = (x_n\\u2074 + 2 * a * x_n\\u00b2 + a\\u00b2) / (4 * x_n\\u00b2) - a\\n            //              = (x_n\\u2074 + 2 * a * x_n\\u00b2 + a\\u00b2 - 4 * a * x_n\\u00b2) / (4 * x_n\\u00b2)\\n            //              = (x_n\\u2074 - 2 * a * x_n\\u00b2 + a\\u00b2) / (4 * x_n\\u00b2)\\n            //              = (x_n\\u00b2 - a)\\u00b2 / (2 * x_n)\\u00b2\\n            //              = ((x_n\\u00b2 - a) / (2 * x_n))\\u00b2\\n            //              \\u2265 0\\n            // Which proves that for all n \\u2265 1, sqrt(a) \\u2264 x_n\\n            //\\n            // This gives us the proof of quadratic convergence of the sequence:\\n            // \\u03b5_{n+1} = | x_{n+1} - sqrt(a) |\\n            //         = | (x_n + a / x_n) / 2 - sqrt(a) |\\n            //         = | (x_n\\u00b2 + a - 2*x_n*sqrt(a)) / (2 * x_n) |\\n            //         = | (x_n - sqrt(a))\\u00b2 / (2 * x_n) |\\n            //         = | \\u03b5_n\\u00b2 / (2 * x_n) |\\n            //         = \\u03b5_n\\u00b2 / | (2 * x_n) |\\n            //\\n            // For the first iteration, we have a special case where x_0 is known:\\n            // \\u03b5_1 = \\u03b5_0\\u00b2 / | (2 * x_0) |\\n            //     \\u2264 (2**(e-2))\\u00b2 / (2 * (2**(e-1) + 2**(e-2)))\\n            //     \\u2264 2**(2*e-4) / (3 * 2**(e-1))\\n            //     \\u2264 2**(e-3) / 3\\n            //     \\u2264 2**(e-3-log2(3))\\n            //     \\u2264 2**(e-4.5)\\n            //\\n            // For the following iterations, we use the fact that, 2**(e-1) \\u2264 sqrt(a) \\u2264 x_n:\\n            // \\u03b5_{n+1} = \\u03b5_n\\u00b2 / | (2 * x_n) |\\n            //         \\u2264 (2**(e-k))\\u00b2 / (2 * 2**(e-1))\\n            //         \\u2264 2**(2*e-2*k) / 2**e\\n            //         \\u2264 2**(e-2*k)\\n            xn = (xn + a / xn) >> 1; // \\u03b5_1 := | x_1 - sqrt(a) | \\u2264 2**(e-4.5)  -- special case, see above\\n            xn = (xn + a / xn) >> 1; // \\u03b5_2 := | x_2 - sqrt(a) | \\u2264 2**(e-9)    -- general case with k = 4.5\\n            xn = (xn + a / xn) >> 1; // \\u03b5_3 := | x_3 - sqrt(a) | \\u2264 2**(e-18)   -- general case with k = 9\\n            xn = (xn + a / xn) >> 1; // \\u03b5_4 := | x_4 - sqrt(a) | \\u2264 2**(e-36)   -- general case with k = 18\\n            xn = (xn + a / xn) >> 1; // \\u03b5_5 := | x_5 - sqrt(a) | \\u2264 2**(e-72)   -- general case with k = 36\\n            xn = (xn + a / xn) >> 1; // \\u03b5_6 := | x_6 - sqrt(a) | \\u2264 2**(e-144)  -- general case with k = 72\\n\\n            // Because e \\u2264 128 (as discussed during the first estimation phase), we know have reached a precision\\n            // \\u03b5_6 \\u2264 2**(e-144) < 1. Given we're operating on integers, then we can ensure that xn is now either\\n            // sqrt(a) or sqrt(a) + 1.\\n            return xn - SafeCast.toUint(xn > a / xn);\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates sqrt(a), following the selected rounding direction.\\n     */\\n    function sqrt(uint256 a, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = sqrt(a);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && result * result < a);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2 of a positive value rounded towards zero.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 x) internal pure returns (uint256 r) {\\n        // If value has upper 128 bits set, log2 result is at least 128\\n        r = SafeCast.toUint(x > 0xffffffffffffffffffffffffffffffff) << 7;\\n        // If upper 64 bits of 128-bit half set, add 64 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffffffffffff) << 6;\\n        // If upper 32 bits of 64-bit half set, add 32 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffff) << 5;\\n        // If upper 16 bits of 32-bit half set, add 16 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffff) << 4;\\n        // If upper 8 bits of 16-bit half set, add 8 to result\\n        r |= SafeCast.toUint((x >> r) > 0xff) << 3;\\n        // If upper 4 bits of 8-bit half set, add 4 to result\\n        r |= SafeCast.toUint((x >> r) > 0xf) << 2;\\n\\n        // Shifts value right by the current result and use it as an index into this lookup table:\\n        //\\n        // | x (4 bits) |  index  | table[index] = MSB position |\\n        // |------------|---------|-----------------------------|\\n        // |    0000    |    0    |        table[0] = 0         |\\n        // |    0001    |    1    |        table[1] = 0         |\\n        // |    0010    |    2    |        table[2] = 1         |\\n        // |    0011    |    3    |        table[3] = 1         |\\n        // |    0100    |    4    |        table[4] = 2         |\\n        // |    0101    |    5    |        table[5] = 2         |\\n        // |    0110    |    6    |        table[6] = 2         |\\n        // |    0111    |    7    |        table[7] = 2         |\\n        // |    1000    |    8    |        table[8] = 3         |\\n        // |    1001    |    9    |        table[9] = 3         |\\n        // |    1010    |   10    |        table[10] = 3        |\\n        // |    1011    |   11    |        table[11] = 3        |\\n        // |    1100    |   12    |        table[12] = 3        |\\n        // |    1101    |   13    |        table[13] = 3        |\\n        // |    1110    |   14    |        table[14] = 3        |\\n        // |    1111    |   15    |        table[15] = 3        |\\n        //\\n        // The lookup table is represented as a 32-byte value with the MSB positions for 0-15 in the last 16 bytes.\\n        assembly (\\\"memory-safe\\\") {\\n            r := or(r, byte(shr(r, x), 0x00000101020202020303030303030303***************************00000))\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log2(value);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 1 << result < value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10 of a positive value rounded towards zero.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >= 10 ** 64) {\\n                value /= 10 ** 64;\\n                result += 64;\\n            }\\n            if (value >= 10 ** 32) {\\n                value /= 10 ** 32;\\n                result += 32;\\n            }\\n            if (value >= 10 ** 16) {\\n                value /= 10 ** 16;\\n                result += 16;\\n            }\\n            if (value >= 10 ** 8) {\\n                value /= 10 ** 8;\\n                result += 8;\\n            }\\n            if (value >= 10 ** 4) {\\n                value /= 10 ** 4;\\n                result += 4;\\n            }\\n            if (value >= 10 ** 2) {\\n                value /= 10 ** 2;\\n                result += 2;\\n            }\\n            if (value >= 10 ** 1) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log10(value);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 10 ** result < value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256 of a positive value rounded towards zero.\\n     * Returns 0 if given 0.\\n     *\\n     * Adding one to the result gives the number of pairs of hex symbols needed to represent `value` as a hex string.\\n     */\\n    function log256(uint256 x) internal pure returns (uint256 r) {\\n        // If value has upper 128 bits set, log2 result is at least 128\\n        r = SafeCast.toUint(x > 0xffffffffffffffffffffffffffffffff) << 7;\\n        // If upper 64 bits of 128-bit half set, add 64 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffffffffffff) << 6;\\n        // If upper 32 bits of 64-bit half set, add 32 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffff) << 5;\\n        // If upper 16 bits of 32-bit half set, add 16 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffff) << 4;\\n        // Add 1 if upper 8 bits of 16-bit half set, and divide accumulated result by 8\\n        return (r >> 3) | SafeCast.toUint((x >> r) > 0xff);\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log256(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log256(value);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 1 << (result << 3) < value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns whether a provided rounding mode is considered rounding up for unsigned integers.\\n     */\\n    function unsignedRoundsUp(Rounding rounding) internal pure returns (bool) {\\n        return uint8(rounding) % 2 == 1;\\n    }\\n}\\n\",\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/SafeCast.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/math/SafeCast.sol)\\n// This file was procedurally generated from scripts/generate/templates/SafeCast.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Wrappers over Solidity's uintXX/intXX/bool casting operators with added overflow\\n * checks.\\n *\\n * Downcasting from uint256/int256 in Solidity does not revert on overflow. This can\\n * easily result in undesired exploitation or bugs, since developers usually\\n * assume that overflows raise errors. `SafeCast` restores this intuition by\\n * reverting the transaction when such an operation overflows.\\n *\\n * Using this library instead of the unchecked operations eliminates an entire\\n * class of bugs, so it's recommended to use it always.\\n */\\nlibrary SafeCast {\\n    /**\\n     * @dev Value doesn't fit in an uint of `bits` size.\\n     */\\n    error SafeCastOverflowedUintDowncast(uint8 bits, uint256 value);\\n\\n    /**\\n     * @dev An int value doesn't fit in an uint of `bits` size.\\n     */\\n    error SafeCastOverflowedIntToUint(int256 value);\\n\\n    /**\\n     * @dev Value doesn't fit in an int of `bits` size.\\n     */\\n    error SafeCastOverflowedIntDowncast(uint8 bits, int256 value);\\n\\n    /**\\n     * @dev An uint value doesn't fit in an int of `bits` size.\\n     */\\n    error SafeCastOverflowedUintToInt(uint256 value);\\n\\n    /**\\n     * @dev Returns the downcasted uint248 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint248).\\n     *\\n     * Counterpart to Solidity's `uint248` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 248 bits\\n     */\\n    function toUint248(uint256 value) internal pure returns (uint248) {\\n        if (value > type(uint248).max) {\\n            revert SafeCastOverflowedUintDowncast(248, value);\\n        }\\n        return uint248(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint240 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint240).\\n     *\\n     * Counterpart to Solidity's `uint240` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 240 bits\\n     */\\n    function toUint240(uint256 value) internal pure returns (uint240) {\\n        if (value > type(uint240).max) {\\n            revert SafeCastOverflowedUintDowncast(240, value);\\n        }\\n        return uint240(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint232 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint232).\\n     *\\n     * Counterpart to Solidity's `uint232` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 232 bits\\n     */\\n    function toUint232(uint256 value) internal pure returns (uint232) {\\n        if (value > type(uint232).max) {\\n            revert SafeCastOverflowedUintDowncast(232, value);\\n        }\\n        return uint232(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint224 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint224).\\n     *\\n     * Counterpart to Solidity's `uint224` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 224 bits\\n     */\\n    function toUint224(uint256 value) internal pure returns (uint224) {\\n        if (value > type(uint224).max) {\\n            revert SafeCastOverflowedUintDowncast(224, value);\\n        }\\n        return uint224(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint216 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint216).\\n     *\\n     * Counterpart to Solidity's `uint216` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 216 bits\\n     */\\n    function toUint216(uint256 value) internal pure returns (uint216) {\\n        if (value > type(uint216).max) {\\n            revert SafeCastOverflowedUintDowncast(216, value);\\n        }\\n        return uint216(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint208 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint208).\\n     *\\n     * Counterpart to Solidity's `uint208` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 208 bits\\n     */\\n    function toUint208(uint256 value) internal pure returns (uint208) {\\n        if (value > type(uint208).max) {\\n            revert SafeCastOverflowedUintDowncast(208, value);\\n        }\\n        return uint208(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint200 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint200).\\n     *\\n     * Counterpart to Solidity's `uint200` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 200 bits\\n     */\\n    function toUint200(uint256 value) internal pure returns (uint200) {\\n        if (value > type(uint200).max) {\\n            revert SafeCastOverflowedUintDowncast(200, value);\\n        }\\n        return uint200(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint192 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint192).\\n     *\\n     * Counterpart to Solidity's `uint192` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 192 bits\\n     */\\n    function toUint192(uint256 value) internal pure returns (uint192) {\\n        if (value > type(uint192).max) {\\n            revert SafeCastOverflowedUintDowncast(192, value);\\n        }\\n        return uint192(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint184 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint184).\\n     *\\n     * Counterpart to Solidity's `uint184` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 184 bits\\n     */\\n    function toUint184(uint256 value) internal pure returns (uint184) {\\n        if (value > type(uint184).max) {\\n            revert SafeCastOverflowedUintDowncast(184, value);\\n        }\\n        return uint184(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint176 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint176).\\n     *\\n     * Counterpart to Solidity's `uint176` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 176 bits\\n     */\\n    function toUint176(uint256 value) internal pure returns (uint176) {\\n        if (value > type(uint176).max) {\\n            revert SafeCastOverflowedUintDowncast(176, value);\\n        }\\n        return uint176(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint168 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint168).\\n     *\\n     * Counterpart to Solidity's `uint168` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 168 bits\\n     */\\n    function toUint168(uint256 value) internal pure returns (uint168) {\\n        if (value > type(uint168).max) {\\n            revert SafeCastOverflowedUintDowncast(168, value);\\n        }\\n        return uint168(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint160 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint160).\\n     *\\n     * Counterpart to Solidity's `uint160` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 160 bits\\n     */\\n    function toUint160(uint256 value) internal pure returns (uint160) {\\n        if (value > type(uint160).max) {\\n            revert SafeCastOverflowedUintDowncast(160, value);\\n        }\\n        return uint160(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint152 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint152).\\n     *\\n     * Counterpart to Solidity's `uint152` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 152 bits\\n     */\\n    function toUint152(uint256 value) internal pure returns (uint152) {\\n        if (value > type(uint152).max) {\\n            revert SafeCastOverflowedUintDowncast(152, value);\\n        }\\n        return uint152(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint144 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint144).\\n     *\\n     * Counterpart to Solidity's `uint144` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 144 bits\\n     */\\n    function toUint144(uint256 value) internal pure returns (uint144) {\\n        if (value > type(uint144).max) {\\n            revert SafeCastOverflowedUintDowncast(144, value);\\n        }\\n        return uint144(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint136 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint136).\\n     *\\n     * Counterpart to Solidity's `uint136` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 136 bits\\n     */\\n    function toUint136(uint256 value) internal pure returns (uint136) {\\n        if (value > type(uint136).max) {\\n            revert SafeCastOverflowedUintDowncast(136, value);\\n        }\\n        return uint136(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint128 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint128).\\n     *\\n     * Counterpart to Solidity's `uint128` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 128 bits\\n     */\\n    function toUint128(uint256 value) internal pure returns (uint128) {\\n        if (value > type(uint128).max) {\\n            revert SafeCastOverflowedUintDowncast(128, value);\\n        }\\n        return uint128(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint120 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint120).\\n     *\\n     * Counterpart to Solidity's `uint120` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 120 bits\\n     */\\n    function toUint120(uint256 value) internal pure returns (uint120) {\\n        if (value > type(uint120).max) {\\n            revert SafeCastOverflowedUintDowncast(120, value);\\n        }\\n        return uint120(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint112 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint112).\\n     *\\n     * Counterpart to Solidity's `uint112` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 112 bits\\n     */\\n    function toUint112(uint256 value) internal pure returns (uint112) {\\n        if (value > type(uint112).max) {\\n            revert SafeCastOverflowedUintDowncast(112, value);\\n        }\\n        return uint112(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint104 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint104).\\n     *\\n     * Counterpart to Solidity's `uint104` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 104 bits\\n     */\\n    function toUint104(uint256 value) internal pure returns (uint104) {\\n        if (value > type(uint104).max) {\\n            revert SafeCastOverflowedUintDowncast(104, value);\\n        }\\n        return uint104(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint96 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint96).\\n     *\\n     * Counterpart to Solidity's `uint96` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 96 bits\\n     */\\n    function toUint96(uint256 value) internal pure returns (uint96) {\\n        if (value > type(uint96).max) {\\n            revert SafeCastOverflowedUintDowncast(96, value);\\n        }\\n        return uint96(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint88 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint88).\\n     *\\n     * Counterpart to Solidity's `uint88` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 88 bits\\n     */\\n    function toUint88(uint256 value) internal pure returns (uint88) {\\n        if (value > type(uint88).max) {\\n            revert SafeCastOverflowedUintDowncast(88, value);\\n        }\\n        return uint88(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint80 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint80).\\n     *\\n     * Counterpart to Solidity's `uint80` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 80 bits\\n     */\\n    function toUint80(uint256 value) internal pure returns (uint80) {\\n        if (value > type(uint80).max) {\\n            revert SafeCastOverflowedUintDowncast(80, value);\\n        }\\n        return uint80(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint72 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint72).\\n     *\\n     * Counterpart to Solidity's `uint72` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 72 bits\\n     */\\n    function toUint72(uint256 value) internal pure returns (uint72) {\\n        if (value > type(uint72).max) {\\n            revert SafeCastOverflowedUintDowncast(72, value);\\n        }\\n        return uint72(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint64 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint64).\\n     *\\n     * Counterpart to Solidity's `uint64` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 64 bits\\n     */\\n    function toUint64(uint256 value) internal pure returns (uint64) {\\n        if (value > type(uint64).max) {\\n            revert SafeCastOverflowedUintDowncast(64, value);\\n        }\\n        return uint64(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint56 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint56).\\n     *\\n     * Counterpart to Solidity's `uint56` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 56 bits\\n     */\\n    function toUint56(uint256 value) internal pure returns (uint56) {\\n        if (value > type(uint56).max) {\\n            revert SafeCastOverflowedUintDowncast(56, value);\\n        }\\n        return uint56(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint48 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint48).\\n     *\\n     * Counterpart to Solidity's `uint48` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 48 bits\\n     */\\n    function toUint48(uint256 value) internal pure returns (uint48) {\\n        if (value > type(uint48).max) {\\n            revert SafeCastOverflowedUintDowncast(48, value);\\n        }\\n        return uint48(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint40 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint40).\\n     *\\n     * Counterpart to Solidity's `uint40` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 40 bits\\n     */\\n    function toUint40(uint256 value) internal pure returns (uint40) {\\n        if (value > type(uint40).max) {\\n            revert SafeCastOverflowedUintDowncast(40, value);\\n        }\\n        return uint40(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint32 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint32).\\n     *\\n     * Counterpart to Solidity's `uint32` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 32 bits\\n     */\\n    function toUint32(uint256 value) internal pure returns (uint32) {\\n        if (value > type(uint32).max) {\\n            revert SafeCastOverflowedUintDowncast(32, value);\\n        }\\n        return uint32(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint24 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint24).\\n     *\\n     * Counterpart to Solidity's `uint24` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 24 bits\\n     */\\n    function toUint24(uint256 value) internal pure returns (uint24) {\\n        if (value > type(uint24).max) {\\n            revert SafeCastOverflowedUintDowncast(24, value);\\n        }\\n        return uint24(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint16 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint16).\\n     *\\n     * Counterpart to Solidity's `uint16` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 16 bits\\n     */\\n    function toUint16(uint256 value) internal pure returns (uint16) {\\n        if (value > type(uint16).max) {\\n            revert SafeCastOverflowedUintDowncast(16, value);\\n        }\\n        return uint16(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint8 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint8).\\n     *\\n     * Counterpart to Solidity's `uint8` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 8 bits\\n     */\\n    function toUint8(uint256 value) internal pure returns (uint8) {\\n        if (value > type(uint8).max) {\\n            revert SafeCastOverflowedUintDowncast(8, value);\\n        }\\n        return uint8(value);\\n    }\\n\\n    /**\\n     * @dev Converts a signed int256 into an unsigned uint256.\\n     *\\n     * Requirements:\\n     *\\n     * - input must be greater than or equal to 0.\\n     */\\n    function toUint256(int256 value) internal pure returns (uint256) {\\n        if (value < 0) {\\n            revert SafeCastOverflowedIntToUint(value);\\n        }\\n        return uint256(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int248 from int256, reverting on\\n     * overflow (when the input is less than smallest int248 or\\n     * greater than largest int248).\\n     *\\n     * Counterpart to Solidity's `int248` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 248 bits\\n     */\\n    function toInt248(int256 value) internal pure returns (int248 downcasted) {\\n        downcasted = int248(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(248, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int240 from int256, reverting on\\n     * overflow (when the input is less than smallest int240 or\\n     * greater than largest int240).\\n     *\\n     * Counterpart to Solidity's `int240` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 240 bits\\n     */\\n    function toInt240(int256 value) internal pure returns (int240 downcasted) {\\n        downcasted = int240(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(240, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int232 from int256, reverting on\\n     * overflow (when the input is less than smallest int232 or\\n     * greater than largest int232).\\n     *\\n     * Counterpart to Solidity's `int232` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 232 bits\\n     */\\n    function toInt232(int256 value) internal pure returns (int232 downcasted) {\\n        downcasted = int232(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(232, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int224 from int256, reverting on\\n     * overflow (when the input is less than smallest int224 or\\n     * greater than largest int224).\\n     *\\n     * Counterpart to Solidity's `int224` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 224 bits\\n     */\\n    function toInt224(int256 value) internal pure returns (int224 downcasted) {\\n        downcasted = int224(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(224, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int216 from int256, reverting on\\n     * overflow (when the input is less than smallest int216 or\\n     * greater than largest int216).\\n     *\\n     * Counterpart to Solidity's `int216` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 216 bits\\n     */\\n    function toInt216(int256 value) internal pure returns (int216 downcasted) {\\n        downcasted = int216(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(216, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int208 from int256, reverting on\\n     * overflow (when the input is less than smallest int208 or\\n     * greater than largest int208).\\n     *\\n     * Counterpart to Solidity's `int208` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 208 bits\\n     */\\n    function toInt208(int256 value) internal pure returns (int208 downcasted) {\\n        downcasted = int208(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(208, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int200 from int256, reverting on\\n     * overflow (when the input is less than smallest int200 or\\n     * greater than largest int200).\\n     *\\n     * Counterpart to Solidity's `int200` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 200 bits\\n     */\\n    function toInt200(int256 value) internal pure returns (int200 downcasted) {\\n        downcasted = int200(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(200, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int192 from int256, reverting on\\n     * overflow (when the input is less than smallest int192 or\\n     * greater than largest int192).\\n     *\\n     * Counterpart to Solidity's `int192` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 192 bits\\n     */\\n    function toInt192(int256 value) internal pure returns (int192 downcasted) {\\n        downcasted = int192(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(192, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int184 from int256, reverting on\\n     * overflow (when the input is less than smallest int184 or\\n     * greater than largest int184).\\n     *\\n     * Counterpart to Solidity's `int184` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 184 bits\\n     */\\n    function toInt184(int256 value) internal pure returns (int184 downcasted) {\\n        downcasted = int184(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(184, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int176 from int256, reverting on\\n     * overflow (when the input is less than smallest int176 or\\n     * greater than largest int176).\\n     *\\n     * Counterpart to Solidity's `int176` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 176 bits\\n     */\\n    function toInt176(int256 value) internal pure returns (int176 downcasted) {\\n        downcasted = int176(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(176, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int168 from int256, reverting on\\n     * overflow (when the input is less than smallest int168 or\\n     * greater than largest int168).\\n     *\\n     * Counterpart to Solidity's `int168` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 168 bits\\n     */\\n    function toInt168(int256 value) internal pure returns (int168 downcasted) {\\n        downcasted = int168(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(168, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int160 from int256, reverting on\\n     * overflow (when the input is less than smallest int160 or\\n     * greater than largest int160).\\n     *\\n     * Counterpart to Solidity's `int160` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 160 bits\\n     */\\n    function toInt160(int256 value) internal pure returns (int160 downcasted) {\\n        downcasted = int160(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(160, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int152 from int256, reverting on\\n     * overflow (when the input is less than smallest int152 or\\n     * greater than largest int152).\\n     *\\n     * Counterpart to Solidity's `int152` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 152 bits\\n     */\\n    function toInt152(int256 value) internal pure returns (int152 downcasted) {\\n        downcasted = int152(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(152, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int144 from int256, reverting on\\n     * overflow (when the input is less than smallest int144 or\\n     * greater than largest int144).\\n     *\\n     * Counterpart to Solidity's `int144` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 144 bits\\n     */\\n    function toInt144(int256 value) internal pure returns (int144 downcasted) {\\n        downcasted = int144(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(144, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int136 from int256, reverting on\\n     * overflow (when the input is less than smallest int136 or\\n     * greater than largest int136).\\n     *\\n     * Counterpart to Solidity's `int136` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 136 bits\\n     */\\n    function toInt136(int256 value) internal pure returns (int136 downcasted) {\\n        downcasted = int136(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(136, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int128 from int256, reverting on\\n     * overflow (when the input is less than smallest int128 or\\n     * greater than largest int128).\\n     *\\n     * Counterpart to Solidity's `int128` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 128 bits\\n     */\\n    function toInt128(int256 value) internal pure returns (int128 downcasted) {\\n        downcasted = int128(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(128, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int120 from int256, reverting on\\n     * overflow (when the input is less than smallest int120 or\\n     * greater than largest int120).\\n     *\\n     * Counterpart to Solidity's `int120` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 120 bits\\n     */\\n    function toInt120(int256 value) internal pure returns (int120 downcasted) {\\n        downcasted = int120(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(120, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int112 from int256, reverting on\\n     * overflow (when the input is less than smallest int112 or\\n     * greater than largest int112).\\n     *\\n     * Counterpart to Solidity's `int112` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 112 bits\\n     */\\n    function toInt112(int256 value) internal pure returns (int112 downcasted) {\\n        downcasted = int112(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(112, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int104 from int256, reverting on\\n     * overflow (when the input is less than smallest int104 or\\n     * greater than largest int104).\\n     *\\n     * Counterpart to Solidity's `int104` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 104 bits\\n     */\\n    function toInt104(int256 value) internal pure returns (int104 downcasted) {\\n        downcasted = int104(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(104, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int96 from int256, reverting on\\n     * overflow (when the input is less than smallest int96 or\\n     * greater than largest int96).\\n     *\\n     * Counterpart to Solidity's `int96` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 96 bits\\n     */\\n    function toInt96(int256 value) internal pure returns (int96 downcasted) {\\n        downcasted = int96(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(96, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int88 from int256, reverting on\\n     * overflow (when the input is less than smallest int88 or\\n     * greater than largest int88).\\n     *\\n     * Counterpart to Solidity's `int88` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 88 bits\\n     */\\n    function toInt88(int256 value) internal pure returns (int88 downcasted) {\\n        downcasted = int88(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(88, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int80 from int256, reverting on\\n     * overflow (when the input is less than smallest int80 or\\n     * greater than largest int80).\\n     *\\n     * Counterpart to Solidity's `int80` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 80 bits\\n     */\\n    function toInt80(int256 value) internal pure returns (int80 downcasted) {\\n        downcasted = int80(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(80, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int72 from int256, reverting on\\n     * overflow (when the input is less than smallest int72 or\\n     * greater than largest int72).\\n     *\\n     * Counterpart to Solidity's `int72` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 72 bits\\n     */\\n    function toInt72(int256 value) internal pure returns (int72 downcasted) {\\n        downcasted = int72(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(72, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int64 from int256, reverting on\\n     * overflow (when the input is less than smallest int64 or\\n     * greater than largest int64).\\n     *\\n     * Counterpart to Solidity's `int64` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 64 bits\\n     */\\n    function toInt64(int256 value) internal pure returns (int64 downcasted) {\\n        downcasted = int64(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(64, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int56 from int256, reverting on\\n     * overflow (when the input is less than smallest int56 or\\n     * greater than largest int56).\\n     *\\n     * Counterpart to Solidity's `int56` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 56 bits\\n     */\\n    function toInt56(int256 value) internal pure returns (int56 downcasted) {\\n        downcasted = int56(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(56, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int48 from int256, reverting on\\n     * overflow (when the input is less than smallest int48 or\\n     * greater than largest int48).\\n     *\\n     * Counterpart to Solidity's `int48` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 48 bits\\n     */\\n    function toInt48(int256 value) internal pure returns (int48 downcasted) {\\n        downcasted = int48(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(48, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int40 from int256, reverting on\\n     * overflow (when the input is less than smallest int40 or\\n     * greater than largest int40).\\n     *\\n     * Counterpart to Solidity's `int40` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 40 bits\\n     */\\n    function toInt40(int256 value) internal pure returns (int40 downcasted) {\\n        downcasted = int40(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(40, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int32 from int256, reverting on\\n     * overflow (when the input is less than smallest int32 or\\n     * greater than largest int32).\\n     *\\n     * Counterpart to Solidity's `int32` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 32 bits\\n     */\\n    function toInt32(int256 value) internal pure returns (int32 downcasted) {\\n        downcasted = int32(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(32, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int24 from int256, reverting on\\n     * overflow (when the input is less than smallest int24 or\\n     * greater than largest int24).\\n     *\\n     * Counterpart to Solidity's `int24` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 24 bits\\n     */\\n    function toInt24(int256 value) internal pure returns (int24 downcasted) {\\n        downcasted = int24(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(24, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int16 from int256, reverting on\\n     * overflow (when the input is less than smallest int16 or\\n     * greater than largest int16).\\n     *\\n     * Counterpart to Solidity's `int16` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 16 bits\\n     */\\n    function toInt16(int256 value) internal pure returns (int16 downcasted) {\\n        downcasted = int16(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(16, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int8 from int256, reverting on\\n     * overflow (when the input is less than smallest int8 or\\n     * greater than largest int8).\\n     *\\n     * Counterpart to Solidity's `int8` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 8 bits\\n     */\\n    function toInt8(int256 value) internal pure returns (int8 downcasted) {\\n        downcasted = int8(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(8, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts an unsigned uint256 into a signed int256.\\n     *\\n     * Requirements:\\n     *\\n     * - input must be less than or equal to maxInt256.\\n     */\\n    function toInt256(uint256 value) internal pure returns (int256) {\\n        // Note: Unsafe cast below is okay because `type(int256).max` is guaranteed to be positive\\n        if (value > uint256(type(int256).max)) {\\n            revert SafeCastOverflowedUintToInt(value);\\n        }\\n        return int256(value);\\n    }\\n\\n    /**\\n     * @dev Cast a boolean (false or true) to a uint256 (0 or 1) with no jump.\\n     */\\n    function toUint(bool b) internal pure returns (uint256 u) {\\n        assembly (\\\"memory-safe\\\") {\\n            u := iszero(iszero(b))\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\"},\"contracts/MoonbagsStake.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/math/Math.sol\\\";\\n\\n/**\\n * @title Moonbags Staking\\n */\\ncontract MoonbagsStake is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {\\n    using SafeERC20 for IERC20;\\n    using Math for uint256;\\n\\n    // === Constants ===\\n    uint256 private constant MULTIPLIER = 1e18;\\n    uint256 public constant DEFAULT_DENY_UNSTAKE_DURATION = 1 hours;\\n\\n    // === Structs ===\\n    struct Configuration {\\n        address admin;\\n        uint256 denyUnstakeDuration; // Duration in seconds users must wait before unstaking\\n    }\\n\\n    struct StakingPool {\\n        address initializer;\\n        uint256 totalSupply;\\n        uint256 rewardIndex;\\n        uint256 pendingInitialRewards;\\n        uint256 totalRewards; // Total HYPE rewards in pool\\n    }\\n\\n    struct CreatorPool {\\n        address initializer;\\n        address creator;\\n        uint256 totalRewards; // Total HYPE rewards for creator\\n    }\\n\\n    struct StakingAccount {\\n        address staker;\\n        uint256 balance;\\n        uint256 rewardIndex;\\n        uint256 earned;\\n        uint256 unstakeDeadline;\\n    }\\n\\n    // === State Variables ===\\n    Configuration public config;\\n\\n    // Mapping from token address to staking pool\\n    mapping(address => StakingPool) public stakingPools;\\n\\n    // Mapping from token address to creator pool\\n    mapping(address => CreatorPool) public creatorPools;\\n\\n    // Mapping from token address to user address to staking account\\n    mapping(address => mapping(address => StakingAccount)) public stakingAccounts;\\n\\n    // === Events ===\\n    event InitializeStakingPoolEvent(\\n        address indexed tokenAddress, address indexed initializer, uint256 timestamp\\n    );\\n\\n    event InitializeCreatorPoolEvent(\\n        address indexed tokenAddress,\\n        address indexed initializer,\\n        address indexed creator,\\n        uint256 timestamp\\n    );\\n\\n    event StakeEvent(\\n        address indexed tokenAddress, address indexed staker, uint256 amount, uint256 timestamp\\n    );\\n\\n    event UnstakeEvent(\\n        address indexed tokenAddress,\\n        address indexed unstaker,\\n        uint256 amount,\\n        bool isStakingAccountDeleted,\\n        uint256 timestamp\\n    );\\n\\n    event UpdateRewardIndexEvent(\\n        address indexed tokenAddress,\\n        address indexed rewardUpdater,\\n        uint256 reward,\\n        bool isInitialRewards,\\n        uint256 timestamp\\n    );\\n\\n    event DepositPoolCreatorEvent(\\n        address indexed tokenAddress, address indexed depositor, uint256 amount, uint256 timestamp\\n    );\\n\\n    event ClaimStakingPoolEvent(\\n        address indexed tokenAddress,\\n        address indexed claimer,\\n        uint256 reward,\\n        bool isStakingAccountDeleted,\\n        uint256 timestamp\\n    );\\n\\n    event ClaimCreatorPoolEvent(\\n        address indexed tokenAddress, address indexed claimer, uint256 reward, uint256 timestamp\\n    );\\n\\n    // === Custom Errors ===\\n    error StakingPoolNotExist();\\n    error StakingCreatorNotExist();\\n    error StakingAccountNotExist();\\n    error AccountBalanceNotEnough();\\n    error InvalidCreator();\\n    error InvalidAmount();\\n    error RewardToClaimNotValid();\\n    error UnstakeDeadlineNotAllow();\\n    error NotUpgrade();\\n    error ZeroAddress();\\n    error StakingPoolAlreadyExists();\\n    error CreatorPoolAlreadyExists();\\n    error InvalidTokenAddress();\\n    error OnlyAdmin();\\n\\n    // === Modifiers ===\\n    modifier onlyAdmin() {\\n        if (msg.sender != config.admin) revert OnlyAdmin();\\n        _;\\n    }\\n\\n    // === Initialize Function ===\\n    /**\\n     * @notice Initialize the contract\\n     */\\n    function initialize() public initializer {\\n        __ReentrancyGuard_init();\\n        __Ownable_init(msg.sender);\\n\\n        config =\\n            Configuration({ admin: msg.sender, denyUnstakeDuration: DEFAULT_DENY_UNSTAKE_DURATION });\\n    }\\n\\n    // === Public Functions ===\\n\\n    /**\\n     * @notice Initializes a new staking pool for a specific token\\n     * @param stakingToken The ERC20 token that will be staked in this pool\\n     */\\n    function initializeStakingPool(address stakingToken) external {\\n        if (stakingToken == address(0)) revert InvalidTokenAddress();\\n\\n        if (stakingPoolExists(stakingToken)) {\\n            return;\\n        }\\n\\n        stakingPools[stakingToken] = StakingPool({\\n            initializer: msg.sender,\\n            totalSupply: 0,\\n            rewardIndex: 0,\\n            pendingInitialRewards: 0,\\n            totalRewards: 0\\n        });\\n\\n        emit InitializeStakingPoolEvent(stakingToken, msg.sender, block.timestamp);\\n    }\\n\\n    /**\\n     * @notice Initializes a creator pool for a specific token\\n     * @param stakingToken The ERC20 token associated with this creator pool\\n     * @param creator Address of the creator for this pool\\n     */\\n    function initializeCreatorPool(address stakingToken, address creator) external {\\n        if (stakingToken == address(0)) revert InvalidTokenAddress();\\n        if (creator == address(0)) revert ZeroAddress();\\n\\n        if (creatorPoolExists(stakingToken)) {\\n            return;\\n        }\\n\\n        creatorPools[stakingToken] =\\n            CreatorPool({ initializer: msg.sender, creator: creator, totalRewards: 0 });\\n\\n        emit InitializeCreatorPoolEvent(stakingToken, msg.sender, creator, block.timestamp);\\n    }\\n\\n    /**\\n     * @notice Updates the reward index of a staking pool by adding new rewards\\n     * @param stakingToken The token associated with the staking pool\\n     */\\n    function updateRewardIndex(address stakingToken) external payable {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (msg.value == 0) revert InvalidAmount();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        uint256 rewardAmount = msg.value;\\n\\n        // No stakers - add to pending initial rewards\\n        if (pool.totalSupply == 0) {\\n            pool.pendingInitialRewards += rewardAmount;\\n            pool.totalRewards += rewardAmount;\\n\\n            emit UpdateRewardIndexEvent(\\n                stakingToken,\\n                msg.sender,\\n                rewardAmount,\\n                true, // is initial rewards\\n                block.timestamp\\n            );\\n            return;\\n        }\\n\\n        // Update reward index\\n        pool.rewardIndex += (rewardAmount * MULTIPLIER) / pool.totalSupply;\\n        pool.totalRewards += rewardAmount;\\n\\n        emit UpdateRewardIndexEvent(\\n            stakingToken,\\n            msg.sender,\\n            rewardAmount,\\n            false, // not initial rewards\\n            block.timestamp\\n        );\\n    }\\n\\n    /**\\n     * @notice Deposits HYPE rewards into a creator pool\\n     * @param stakingToken The token associated with the creator pool\\n     */\\n    function depositCreatorPool(address stakingToken) external payable {\\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\\n        if (msg.value == 0) revert InvalidAmount();\\n\\n        CreatorPool storage pool = creatorPools[stakingToken];\\n        pool.totalRewards += msg.value;\\n\\n        emit DepositPoolCreatorEvent(stakingToken, msg.sender, msg.value, block.timestamp);\\n    }\\n\\n    /**\\n     * @notice Stakes tokens in a staking pool\\n     * @param stakingToken The token to stake\\n     * @param amount Amount of tokens to stake\\n     */\\n    function stake(address stakingToken, uint256 amount) external nonReentrant {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (amount == 0) revert InvalidAmount();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n\\n        IERC20(stakingToken).safeTransferFrom(msg.sender, address(this), amount);\\n\\n        // Initialize staking account if first time staking\\n        if (!stakingAccountExists(stakingToken, msg.sender)) {\\n            stakingAccounts[stakingToken][msg.sender] = StakingAccount({\\n                staker: msg.sender,\\n                balance: 0,\\n                rewardIndex: 0,\\n                earned: pool.pendingInitialRewards,\\n                unstakeDeadline: 0\\n            });\\n\\n            // Reset pending initial rewards since first staker gets them\\n            pool.pendingInitialRewards = 0;\\n        }\\n\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        // Update rewards before staking\\n        _updateRewards(pool.rewardIndex, account);\\n\\n        // Update staking account\\n        uint256 currentTime = block.timestamp;\\n        account.unstakeDeadline = currentTime + config.denyUnstakeDuration;\\n        account.balance += amount;\\n        pool.totalSupply += amount;\\n\\n        emit StakeEvent(stakingToken, msg.sender, amount, currentTime);\\n    }\\n\\n    /**\\n     * @notice Unstakes tokens from a staking pool\\n     * @param stakingToken The token to unstake\\n     * @param unstakeAmount Amount of tokens to unstake\\n     */\\n    function unstake(address stakingToken, uint256 unstakeAmount) external nonReentrant {\\n        if (unstakeAmount == 0) revert InvalidAmount();\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        uint256 currentTime = block.timestamp;\\n        if (currentTime < account.unstakeDeadline) revert UnstakeDeadlineNotAllow();\\n\\n        // Update rewards before unstaking\\n        _updateRewards(pool.rewardIndex, account);\\n\\n        if (account.balance < unstakeAmount) revert AccountBalanceNotEnough();\\n\\n        // Update balances\\n        account.balance -= unstakeAmount;\\n        pool.totalSupply -= unstakeAmount;\\n\\n        IERC20(stakingToken).safeTransfer(msg.sender, unstakeAmount);\\n\\n        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);\\n\\n        emit UnstakeEvent(stakingToken, msg.sender, unstakeAmount, isAccountDeleted, currentTime);\\n    }\\n\\n    /**\\n     * @notice Claims rewards from a staking pool\\n     * @param stakingToken The token associated with the staking pool\\n     * @return rewardAmount The amount of HYPE claimed as rewards\\n     */\\n    function claimStakingPool(address stakingToken)\\n        external\\n        nonReentrant\\n        returns (uint256 rewardAmount)\\n    {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        // Update rewards before claiming\\n        _updateRewards(pool.rewardIndex, account);\\n\\n        rewardAmount = account.earned;\\n        if (rewardAmount == 0) revert RewardToClaimNotValid();\\n\\n        account.earned = 0;\\n        pool.totalRewards -= rewardAmount;\\n\\n        payable(msg.sender).transfer(rewardAmount);\\n\\n        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);\\n\\n        emit ClaimStakingPoolEvent(\\n            stakingToken, msg.sender, rewardAmount, isAccountDeleted, block.timestamp\\n        );\\n\\n        return rewardAmount;\\n    }\\n\\n    /**\\n     * @notice Claims rewards from a creator pool\\n     * @param stakingToken The token associated with the creator pool\\n     * @return rewardAmount The amount of HYPE claimed from the creator pool\\n     */\\n    function claimCreatorPool(address stakingToken)\\n        external\\n        nonReentrant\\n        returns (uint256 rewardAmount)\\n    {\\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\\n\\n        CreatorPool storage pool = creatorPools[stakingToken];\\n        if (pool.creator != msg.sender) revert InvalidCreator();\\n\\n        rewardAmount = pool.totalRewards;\\n        if (rewardAmount == 0) revert RewardToClaimNotValid();\\n\\n        pool.totalRewards = 0;\\n\\n        payable(msg.sender).transfer(rewardAmount);\\n\\n        emit ClaimCreatorPoolEvent(stakingToken, msg.sender, rewardAmount, block.timestamp);\\n\\n        return rewardAmount;\\n    }\\n\\n    // === View Functions ===\\n\\n    /**\\n     * @notice Check if a staking account exists for a user\\n     * @param token The token address\\n     * @param user The user address\\n     * @return exists True if the staking account exists\\n     */\\n    function stakingAccountExists(address token, address user) public view returns (bool exists) {\\n        return stakingAccounts[token][user].staker != address(0);\\n    }\\n\\n    /**\\n     * @notice Check if a staking pool exists for a token\\n     * @param token The token address\\n     * @return exists True if the staking pool exists\\n     */\\n    function stakingPoolExists(address token) public view returns (bool exists) {\\n        return stakingPools[token].initializer != address(0);\\n    }\\n\\n    /**\\n     * @notice Check if a creator pool exists for a token\\n     * @param token The token address\\n     * @return exists True if the creator pool exists\\n     */\\n    function creatorPoolExists(address token) public view returns (bool exists) {\\n        return creatorPools[token].initializer != address(0);\\n    }\\n\\n    /**\\n     * @notice Calculates the rewards earned by the caller for staking tokens\\n     * @param stakingToken The token associated with the staking pool\\n     * @return totalEarned The total amount of rewards earned\\n     */\\n    function calculateRewardsEarned(address stakingToken)\\n        external\\n        view\\n        returns (uint256 totalEarned)\\n    {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        uint256 pendingRewards = _calculateRewards(pool.rewardIndex, account);\\n        return account.earned + pendingRewards;\\n    }\\n\\n    /**\\n     * @notice Get staking pool information\\n     * @param stakingToken The token associated with the staking pool\\n     * @return initializer Address that initialized the pool\\n     * @return totalSupply Total amount of tokens staked\\n     * @return rewardIndex Current reward index\\n     * @return pendingInitialRewards Pending initial rewards\\n     * @return totalRewards Total HYPE rewards in pool\\n     */\\n    function getStakingPoolInfo(address stakingToken)\\n        external\\n        view\\n        returns (\\n            address initializer,\\n            uint256 totalSupply,\\n            uint256 rewardIndex,\\n            uint256 pendingInitialRewards,\\n            uint256 totalRewards\\n        )\\n    {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        return (\\n            pool.initializer,\\n            pool.totalSupply,\\n            pool.rewardIndex,\\n            pool.pendingInitialRewards,\\n            pool.totalRewards\\n        );\\n    }\\n\\n    /**\\n     * @notice Get creator pool information\\n     * @param stakingToken The token associated with the creator pool\\n     * @return initializer Address that initialized the pool\\n     * @return creator Address of the creator\\n     * @return totalRewards Total HYPE rewards for creator\\n     */\\n    function getCreatorPoolInfo(address stakingToken)\\n        external\\n        view\\n        returns (address initializer, address creator, uint256 totalRewards)\\n    {\\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\\n\\n        CreatorPool storage pool = creatorPools[stakingToken];\\n        return (pool.initializer, pool.creator, pool.totalRewards);\\n    }\\n\\n    /**\\n     * @notice Get staking account information\\n     * @param stakingToken The token associated with the staking pool\\n     * @param staker Address of the staker\\n     * @return balance Staked token balance\\n     * @return rewardIndex Last reward index when rewards were updated\\n     * @return earned Earned rewards ready to claim\\n     * @return unstakeDeadline Timestamp when unstaking is allowed\\n     */\\n    function getStakingAccountInfo(\\n        address stakingToken,\\n        address staker\\n    )\\n        external\\n        view\\n        returns (uint256 balance, uint256 rewardIndex, uint256 earned, uint256 unstakeDeadline)\\n    {\\n        if (!stakingAccountExists(stakingToken, staker)) revert StakingAccountNotExist();\\n\\n        StakingAccount storage account = stakingAccounts[stakingToken][staker];\\n        return (account.balance, account.rewardIndex, account.earned, account.unstakeDeadline);\\n    }\\n\\n    // === Admin Functions ===\\n\\n    /**\\n     * @notice Update configuration\\n     * @param newDenyUnstakeDuration New deny unstake duration\\n     */\\n    function updateConfig(address newAdmin, uint256 newDenyUnstakeDuration) external onlyAdmin {\\n        config.admin = newAdmin;\\n        config.denyUnstakeDuration = newDenyUnstakeDuration;\\n    }\\n\\n    // === Private Functions ===\\n\\n    /**\\n     * @notice Calculates the pending rewards for a staking account\\n     * @param stakingPoolRewardIndex Current reward index of the staking pool\\n     * @param account The staking account to calculate rewards for\\n     * @return reward The amount of pending rewards\\n     */\\n    function _calculateRewards(\\n        uint256 stakingPoolRewardIndex,\\n        StakingAccount storage account\\n    )\\n        private\\n        view\\n        returns (uint256 reward)\\n    {\\n        if (account.balance == 0) {\\n            return 0;\\n        }\\n\\n        uint256 rewardDiff = stakingPoolRewardIndex - account.rewardIndex;\\n        reward = (account.balance * rewardDiff) / MULTIPLIER;\\n\\n        return reward;\\n    }\\n\\n    /**\\n     * @notice Updates the rewards earned by a staking account based on the current reward index\\n     * @param stakingPoolRewardIndex Current reward index of the staking pool\\n     * @param account The staking account to update rewards for\\n     */\\n    function _updateRewards(\\n        uint256 stakingPoolRewardIndex,\\n        StakingAccount storage account\\n    )\\n        private\\n    {\\n        uint256 pendingRewards = _calculateRewards(stakingPoolRewardIndex, account);\\n        account.earned += pendingRewards;\\n        account.rewardIndex = stakingPoolRewardIndex;\\n    }\\n\\n    /**\\n     * @notice Attempts to clean up a staking account if it has zero balance and zero earned rewards\\n     * @param stakingToken The token associated with the staking pool\\n     * @param staker The address of the staker whose account should be checked\\n     * @return isDeleted Whether the account was successfully deleted\\n     */\\n    function _tryCleanupEmptyAccount(\\n        address stakingToken,\\n        address staker\\n    )\\n        private\\n        returns (bool isDeleted)\\n    {\\n        if (!stakingAccountExists(stakingToken, staker)) {\\n            return false;\\n        }\\n\\n        StakingAccount storage account = stakingAccounts[stakingToken][staker];\\n\\n        if (account.balance == 0 && account.earned == 0) {\\n            delete stakingAccounts[stakingToken][staker];\\n            return true;\\n        }\\n\\n        return false;\\n    }\\n}\\n\",\"keccak256\":\"0xe0d2b89433d56c12d28bf09b6e0a4a296b585723b6eaf43f0d3b2784181d7d3e\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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*********00000001916905551600181527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d290602090a138808280f35b634e487b7160e01b875260419052602486fd5b68ffffffffffffffffff19166801*********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", "deployedBytecode": "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*********00000001916905551600181527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d290602090a138808280f35b634e487b7160e01b875260419052602486fd5b68ffffffffffffffffff19166801*********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", "devdoc": {"errors": {"InvalidInitialization()": [{"details": "The contract is already initialized."}], "NotInitializing()": [{"details": "The contract is not initializing."}], "OwnableInvalidOwner(address)": [{"details": "The owner is not a valid owner account. (eg. `address(0)`)"}], "OwnableUnauthorizedAccount(address)": [{"details": "The caller account is not authorized to perform an operation."}], "ReentrancyGuardReentrantCall()": [{"details": "Unauthorized reentrant call."}], "SafeERC20FailedOperation(address)": [{"details": "An operation with an ERC-20 token failed."}]}, "events": {"Initialized(uint64)": {"details": "Triggered when the contract has been initialized or reinitialized."}}, "kind": "dev", "methods": {"calculateRewardsEarned(address)": {"params": {"stakingToken": "The token associated with the staking pool"}, "returns": {"totalEarned": "The total amount of rewards earned"}}, "claimCreatorPool(address)": {"params": {"stakingToken": "The token associated with the creator pool"}, "returns": {"rewardAmount": "The amount of HYPE claimed from the creator pool"}}, "claimStakingPool(address)": {"params": {"stakingToken": "The token associated with the staking pool"}, "returns": {"rewardAmount": "The amount of HYPE claimed as rewards"}}, "creatorPoolExists(address)": {"params": {"token": "The token address"}, "returns": {"exists": "True if the creator pool exists"}}, "depositCreatorPool(address)": {"params": {"stakingToken": "The token associated with the creator pool"}}, "getCreatorPoolInfo(address)": {"params": {"stakingToken": "The token associated with the creator pool"}, "returns": {"creator": "Address of the creator", "initializer": "Address that initialized the pool", "totalRewards": "Total HYPE rewards for creator"}}, "getStakingAccountInfo(address,address)": {"params": {"staker": "Address of the staker", "stakingToken": "The token associated with the staking pool"}, "returns": {"balance": "Staked token balance", "earned": "Earned rewards ready to claim", "rewardIndex": "Last reward index when rewards were updated", "unstakeDeadline": "Timestamp when unstaking is allowed"}}, "getStakingPoolInfo(address)": {"params": {"stakingToken": "The token associated with the staking pool"}, "returns": {"initializer": "Address that initialized the pool", "pendingInitialRewards": "Pending initial rewards", "rewardIndex": "Current reward index", "totalRewards": "Total HYPE rewards in pool", "totalSupply": "Total amount of tokens staked"}}, "initializeCreatorPool(address,address)": {"params": {"creator": "Address of the creator for this pool", "stakingToken": "The ERC20 token associated with this creator pool"}}, "initializeStakingPool(address)": {"params": {"stakingToken": "The ERC20 token that will be staked in this pool"}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "stake(address,uint256)": {"params": {"amount": "Amount of tokens to stake", "stakingToken": "The token to stake"}}, "stakingAccountExists(address,address)": {"params": {"token": "The token address", "user": "The user address"}, "returns": {"exists": "True if the staking account exists"}}, "stakingPoolExists(address)": {"params": {"token": "The token address"}, "returns": {"exists": "True if the staking pool exists"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "unstake(address,uint256)": {"params": {"stakingToken": "The token to unstake", "unstakeAmount": "Amount of tokens to unstake"}}, "updateConfig(address,uint256)": {"params": {"newDenyUnstakeDuration": "New deny unstake duration"}}, "updateRewardIndex(address)": {"params": {"stakingToken": "The token associated with the staking pool"}}}, "title": "Moonbags Staking", "version": 1}, "userdoc": {"kind": "user", "methods": {"calculateRewardsEarned(address)": {"notice": "Calculates the rewards earned by the caller for staking tokens"}, "claimCreatorPool(address)": {"notice": "Claims rewards from a creator pool"}, "claimStakingPool(address)": {"notice": "Claims rewards from a staking pool"}, "creatorPoolExists(address)": {"notice": "Check if a creator pool exists for a token"}, "depositCreatorPool(address)": {"notice": "Deposits HYPE rewards into a creator pool"}, "getCreatorPoolInfo(address)": {"notice": "Get creator pool information"}, "getStakingAccountInfo(address,address)": {"notice": "Get staking account information"}, "getStakingPoolInfo(address)": {"notice": "Get staking pool information"}, "initialize()": {"notice": "Initialize the contract"}, "initializeCreatorPool(address,address)": {"notice": "Initializes a creator pool for a specific token"}, "initializeStakingPool(address)": {"notice": "Initializes a new staking pool for a specific token"}, "stake(address,uint256)": {"notice": "Stakes tokens in a staking pool"}, "stakingAccountExists(address,address)": {"notice": "Check if a staking account exists for a user"}, "stakingPoolExists(address)": {"notice": "Check if a staking pool exists for a token"}, "unstake(address,uint256)": {"notice": "Unstakes tokens from a staking pool"}, "updateConfig(address,uint256)": {"notice": "Update configuration"}, "updateRewardIndex(address)": {"notice": "Updates the reward index of a staking pool by adding new rewards"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 9368, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "config", "offset": 0, "slot": "0", "type": "t_struct(Configuration)9336_storage"}, {"astId": 9373, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "stakingPools", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_struct(StakingPool)9347_storage)"}, {"astId": 9378, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "creatorPools", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_struct(CreatorPool)9354_storage)"}, {"astId": 9385, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "stakingAccounts", "offset": 0, "slot": "4", "type": "t_mapping(t_address,t_mapping(t_address,t_struct(StakingAccount)9365_storage))"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_address,t_struct(StakingAccount)9365_storage))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => struct MoonbagsStake.StakingAccount))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_struct(StakingAccount)9365_storage)"}, "t_mapping(t_address,t_struct(CreatorPool)9354_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct MoonbagsStake.CreatorPool)", "numberOfBytes": "32", "value": "t_struct(CreatorPool)9354_storage"}, "t_mapping(t_address,t_struct(StakingAccount)9365_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct MoonbagsStake.StakingAccount)", "numberOfBytes": "32", "value": "t_struct(StakingAccount)9365_storage"}, "t_mapping(t_address,t_struct(StakingPool)9347_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct MoonbagsStake.StakingPool)", "numberOfBytes": "32", "value": "t_struct(StakingPool)9347_storage"}, "t_struct(Configuration)9336_storage": {"encoding": "inplace", "label": "struct MoonbagsStake.Configuration", "members": [{"astId": 9333, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "admin", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 9335, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "denyUnstakeDuration", "offset": 0, "slot": "1", "type": "t_uint256"}], "numberOfBytes": "64"}, "t_struct(CreatorPool)9354_storage": {"encoding": "inplace", "label": "struct MoonbagsStake.CreatorPool", "members": [{"astId": 9349, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "initializer", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 9351, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "creator", "offset": 0, "slot": "1", "type": "t_address"}, {"astId": 9353, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "totalRewards", "offset": 0, "slot": "2", "type": "t_uint256"}], "numberOfBytes": "96"}, "t_struct(StakingAccount)9365_storage": {"encoding": "inplace", "label": "struct MoonbagsStake.StakingAccount", "members": [{"astId": 9356, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "staker", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 9358, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "balance", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 9360, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "rewardIndex", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 9362, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "earned", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 9364, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "unstakeDeadline", "offset": 0, "slot": "4", "type": "t_uint256"}], "numberOfBytes": "160"}, "t_struct(StakingPool)9347_storage": {"encoding": "inplace", "label": "struct MoonbagsStake.StakingPool", "members": [{"astId": 9338, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "initializer", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 9340, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "totalSupply", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 9342, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "rewardIndex", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 9344, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "pendingInitialRewards", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 9346, "contract": "contracts/MoonbagsStake.sol:MoonbagsStake", "label": "totalRewards", "offset": 0, "slot": "4", "type": "t_uint256"}], "numberOfBytes": "160"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}