{"address": "0x6d83f4184aB39d10D7cdf63313046ADbf70A576B", "abi": [{"inputs": [], "name": "ContractClosed", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "InvalidLockId", "type": "error"}, {"inputs": [], "name": "InvalidParams", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldAd<PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "ConfigUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "lockId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "locker", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "startTime", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "endTime", "type": "uint256"}], "name": "LockCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "lockId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensWithdrawn", "type": "event"}, {"inputs": [], "name": "config", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}], "name": "createLock", "outputs": [{"internalType": "uint256", "name": "lockId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lockId", "type": "uint256"}, {"internalType": "uint256", "name": "newEndTime", "type": "uint256"}], "name": "extendLock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getConfig", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lockId", "type": "uint256"}], "name": "getLock", "outputs": [{"components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "locker", "type": "address"}, {"internalType": "bool", "name": "closed", "type": "bool"}], "internalType": "struct TokenLock.LockContract", "name": "lockContract", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLockCount", "outputs": [{"internalType": "uint256", "name": "count", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lockId", "type": "uint256"}], "name": "isWithdrawable", "outputs": [{"internalType": "bool", "name": "withdrawable", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "locks", "outputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "locker", "type": "address"}, {"internalType": "bool", "name": "closed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextLockId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}], "name": "updateConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lockId", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0x2096e108013b4b7d1c9abf994dab6f30ac0a8bc92a2e9e1a3c0d3c385f46ba12", "receipt": {"to": null, "from": "0xb6981f22cB9A48B12483B7Fcf512Abd0C8a2B3de", "contractAddress": "0x6d83f4184aB39d10D7cdf63313046ADbf70A576B", "transactionIndex": 4, "gasUsed": "823641", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xdc930f602ed3042c224b4c721bde13dd690f50e633356e0de305c12f29008a5a", "transactionHash": "0x2096e108013b4b7d1c9abf994dab6f30ac0a8bc92a2e9e1a3c0d3c385f46ba12", "logs": [], "blockNumber": 7386001, "cumulativeGasUsed": "1362450", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "2d791615bdbd29bde3ba98a0e9c3b3ee", "metadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ContractClosed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidLockId\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidParams\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"oldAdmin\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newAdmin\",\"type\":\"address\"}],\"name\":\"ConfigUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"lockId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"locker\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"startTime\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"endTime\",\"type\":\"uint256\"}],\"name\":\"LockCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"lockId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TokensWithdrawn\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"config\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endTime\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"}],\"name\":\"createLock\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"lockId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"emergencyWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"lockId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"newEndTime\",\"type\":\"uint256\"}],\"name\":\"extendLock\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getConfig\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"lockId\",\"type\":\"uint256\"}],\"name\":\"getLock\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"startTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endTime\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"locker\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"closed\",\"type\":\"bool\"}],\"internalType\":\"struct TokenLock.LockContract\",\"name\":\"lockContract\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getLockCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"count\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"lockId\",\"type\":\"uint256\"}],\"name\":\"isWithdrawable\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"withdrawable\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"locks\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"startTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"endTime\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"locker\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"closed\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"nextLockId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newAdmin\",\"type\":\"address\"}],\"name\":\"updateConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"lockId\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"createLock(address,uint256,uint256,address)\":{\"params\":{\"amount\":\"Amount of tokens to lock\",\"endTime\":\"End time of the lock in seconds\",\"recipient\":\"Address that will be able to claim the tokens after the lock period\",\"token\":\"Address of the ERC20 token to lock\"},\"returns\":{\"lockId\":\"The ID of the created lock\"}},\"emergencyWithdraw(address,uint256)\":{\"params\":{\"amount\":\"Amount to withdraw\",\"token\":\"Token address to withdraw\"}},\"extendLock(uint256,uint256)\":{\"params\":{\"lockId\":\"ID of the lock to extend\",\"newEndTime\":\"New end time for the lock\"}},\"getConfig()\":{\"returns\":{\"admin\":\"Current admin address\"}},\"getLock(uint256)\":{\"params\":{\"lockId\":\"ID of the lock\"},\"returns\":{\"lockContract\":\"The lock contract details\"}},\"getLockCount()\":{\"returns\":{\"count\":\"Total number of locks\"}},\"isWithdrawable(uint256)\":{\"params\":{\"lockId\":\"ID of the lock\"},\"returns\":{\"withdrawable\":\"True if the lock can be withdrawn\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"updateConfig(address)\":{\"params\":{\"newAdmin\":\"New admin address\"}},\"withdraw(uint256)\":{\"params\":{\"lockId\":\"ID of the lock to withdraw from\"}}},\"title\":\"TokenLock\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"createLock(address,uint256,uint256,address)\":{\"notice\":\"Create a new time-locked token contract\"},\"emergencyWithdraw(address,uint256)\":{\"notice\":\"Emergency withdraw function (admin only)\"},\"extendLock(uint256,uint256)\":{\"notice\":\"Extend the lock duration of an existing time-locked token contract\"},\"getConfig()\":{\"notice\":\"Get current configuration\"},\"getLock(uint256)\":{\"notice\":\"Get lock information\"},\"getLockCount()\":{\"notice\":\"Get the number of locks created\"},\"initialize()\":{\"notice\":\"Initialize the contract\"},\"isWithdrawable(uint256)\":{\"notice\":\"Check if a lock is withdrawable\"},\"updateConfig(address)\":{\"notice\":\"Update configuration\"},\"withdraw(uint256)\":{\"notice\":\"Withdraw tokens from a lock after the lock period has ended\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/TokenLock.sol\":\"TokenLock\"},\"evmVersion\":\"paris\",\"libraries\":{\":__CACHE_BREAKER__\":\"0x00000000d41867734bbee4c6863d9255b2b06ac1\"},\"metadata\":{\"bytecodeHash\":\"none\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {ContextUpgradeable} from \\\"../utils/ContextUpgradeable.sol\\\";\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * The initial owner is set to the address provided by the deployer. This can\\n * later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\\n    /// @custom:storage-location erc7201:openzeppelin.storage.Ownable\\n    struct OwnableStorage {\\n        address _owner;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.Ownable\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant OwnableStorageLocation = 0x9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300;\\n\\n    function _getOwnableStorage() private pure returns (OwnableStorage storage $) {\\n        assembly {\\n            $.slot := OwnableStorageLocation\\n        }\\n    }\\n\\n    /**\\n     * @dev The caller account is not authorized to perform an operation.\\n     */\\n    error OwnableUnauthorizedAccount(address account);\\n\\n    /**\\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\\n     */\\n    error OwnableInvalidOwner(address owner);\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\\n     */\\n    function __Ownable_init(address initialOwner) internal onlyInitializing {\\n        __Ownable_init_unchained(initialOwner);\\n    }\\n\\n    function __Ownable_init_unchained(address initialOwner) internal onlyInitializing {\\n        if (initialOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(initialOwner);\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        OwnableStorage storage $ = _getOwnableStorage();\\n        return $._owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        if (owner() != _msgSender()) {\\n            revert OwnableUnauthorizedAccount(_msgSender());\\n        }\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        if (newOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        OwnableStorage storage $ = _getOwnableStorage();\\n        address oldOwner = $._owner;\\n        $._owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (proxy/utils/Initializable.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\\n *\\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\\n * reused. This mechanism prevents re-execution of each \\\"step\\\" but allows the creation of new initialization steps in\\n * case an upgrade adds a module that needs to be initialized.\\n *\\n * For example:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```solidity\\n * contract MyToken is ERC20Upgradeable {\\n *     function initialize() initializer public {\\n *         __ERC20_init(\\\"MyToken\\\", \\\"MTK\\\");\\n *     }\\n * }\\n *\\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\\n *     function initializeV2() reinitializer(2) public {\\n *         __ERC20Permit_init(\\\"MyToken\\\");\\n *     }\\n * }\\n * ```\\n *\\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\\n *\\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\\n *\\n * [CAUTION]\\n * ====\\n * Avoid leaving a contract uninitialized.\\n *\\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```\\n * /// @custom:oz-upgrades-unsafe-allow constructor\\n * constructor() {\\n *     _disableInitializers();\\n * }\\n * ```\\n * ====\\n */\\nabstract contract Initializable {\\n    /**\\n     * @dev Storage of the initializable contract.\\n     *\\n     * It's implemented on a custom ERC-7201 namespace to reduce the risk of storage collisions\\n     * when using with upgradeable contracts.\\n     *\\n     * @custom:storage-location erc7201:openzeppelin.storage.Initializable\\n     */\\n    struct InitializableStorage {\\n        /**\\n         * @dev Indicates that the contract has been initialized.\\n         */\\n        uint64 _initialized;\\n        /**\\n         * @dev Indicates that the contract is in the process of being initialized.\\n         */\\n        bool _initializing;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.Initializable\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant INITIALIZABLE_STORAGE = 0xf0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00;\\n\\n    /**\\n     * @dev The contract is already initialized.\\n     */\\n    error InvalidInitialization();\\n\\n    /**\\n     * @dev The contract is not initializing.\\n     */\\n    error NotInitializing();\\n\\n    /**\\n     * @dev Triggered when the contract has been initialized or reinitialized.\\n     */\\n    event Initialized(uint64 version);\\n\\n    /**\\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\\n     * `onlyInitializing` functions can be used to initialize parent contracts.\\n     *\\n     * Similar to `reinitializer(1)`, except that in the context of a constructor an `initializer` may be invoked any\\n     * number of times. This behavior in the constructor can be useful during testing and is not expected to be used in\\n     * production.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier initializer() {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        // Cache values to avoid duplicated sloads\\n        bool isTopLevelCall = !$._initializing;\\n        uint64 initialized = $._initialized;\\n\\n        // Allowed calls:\\n        // - initialSetup: the contract is not in the initializing state and no previous version was\\n        //                 initialized\\n        // - construction: the contract is initialized at version 1 (no reinitialization) and the\\n        //                 current contract is just being deployed\\n        bool initialSetup = initialized == 0 && isTopLevelCall;\\n        bool construction = initialized == 1 && address(this).code.length == 0;\\n\\n        if (!initialSetup && !construction) {\\n            revert InvalidInitialization();\\n        }\\n        $._initialized = 1;\\n        if (isTopLevelCall) {\\n            $._initializing = true;\\n        }\\n        _;\\n        if (isTopLevelCall) {\\n            $._initializing = false;\\n            emit Initialized(1);\\n        }\\n    }\\n\\n    /**\\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\\n     * used to initialize parent contracts.\\n     *\\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\\n     * are added through upgrades and that require initialization.\\n     *\\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\\n     *\\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\\n     * a contract, executing them in the right order is up to the developer or operator.\\n     *\\n     * WARNING: Setting the version to 2**64 - 1 will prevent any future reinitialization.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier reinitializer(uint64 version) {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        if ($._initializing || $._initialized >= version) {\\n            revert InvalidInitialization();\\n        }\\n        $._initialized = version;\\n        $._initializing = true;\\n        _;\\n        $._initializing = false;\\n        emit Initialized(version);\\n    }\\n\\n    /**\\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\\n     */\\n    modifier onlyInitializing() {\\n        _checkInitializing();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Reverts if the contract is not in an initializing state. See {onlyInitializing}.\\n     */\\n    function _checkInitializing() internal view virtual {\\n        if (!_isInitializing()) {\\n            revert NotInitializing();\\n        }\\n    }\\n\\n    /**\\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\\n     * through proxies.\\n     *\\n     * Emits an {Initialized} event the first time it is successfully executed.\\n     */\\n    function _disableInitializers() internal virtual {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        if ($._initializing) {\\n            revert InvalidInitialization();\\n        }\\n        if ($._initialized != type(uint64).max) {\\n            $._initialized = type(uint64).max;\\n            emit Initialized(type(uint64).max);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\\n     */\\n    function _getInitializedVersion() internal view returns (uint64) {\\n        return _getInitializableStorage()._initialized;\\n    }\\n\\n    /**\\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\\n     */\\n    function _isInitializing() internal view returns (bool) {\\n        return _getInitializableStorage()._initializing;\\n    }\\n\\n    /**\\n     * @dev Pointer to storage slot. Allows integrators to override it with a custom storage location.\\n     *\\n     * NOTE: Consider following the ERC-7201 formula to derive storage locations.\\n     */\\n    function _initializableStorageSlot() internal pure virtual returns (bytes32) {\\n        return INITIALIZABLE_STORAGE;\\n    }\\n\\n    /**\\n     * @dev Returns a pointer to the storage namespace.\\n     */\\n    // solhint-disable-next-line var-name-mixedcase\\n    function _getInitializableStorage() private pure returns (InitializableStorage storage $) {\\n        bytes32 slot = _initializableStorageSlot();\\n        assembly {\\n            $.slot := slot\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\\n\\npragma solidity ^0.8.20;\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract ContextUpgradeable is Initializable {\\n    function __Context_init() internal onlyInitializing {\\n    }\\n\\n    function __Context_init_unchained() internal onlyInitializing {\\n    }\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    function _contextSuffixLength() internal view virtual returns (uint256) {\\n        return 0;\\n    }\\n}\\n\",\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\\n\\npragma solidity ^0.8.20;\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module that helps prevent reentrant calls to a function.\\n *\\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\\n * available, which can be applied to functions to make sure there are no nested\\n * (reentrant) calls to them.\\n *\\n * Note that because there is a single `nonReentrant` guard, functions marked as\\n * `nonReentrant` may not call one another. This can be worked around by making\\n * those functions `private`, and then adding `external` `nonReentrant` entry\\n * points to them.\\n *\\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\\n * consider using {ReentrancyGuardTransient} instead.\\n *\\n * TIP: If you would like to learn more about reentrancy and alternative ways\\n * to protect against it, check out our blog post\\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\\n */\\nabstract contract ReentrancyGuardUpgradeable is Initializable {\\n    // Booleans are more expensive than uint256 or any type that takes up a full\\n    // word because each write operation emits an extra SLOAD to first read the\\n    // slot's contents, replace the bits taken up by the boolean, and then write\\n    // back. This is the compiler's defense against contract upgrades and\\n    // pointer aliasing, and it cannot be disabled.\\n\\n    // The values being non-zero value makes deployment a bit more expensive,\\n    // but in exchange the refund on every call to nonReentrant will be lower in\\n    // amount. Since refunds are capped to a percentage of the total\\n    // transaction's gas, it is best to keep them low in cases like this one, to\\n    // increase the likelihood of the full refund coming into effect.\\n    uint256 private constant NOT_ENTERED = 1;\\n    uint256 private constant ENTERED = 2;\\n\\n    /// @custom:storage-location erc7201:openzeppelin.storage.ReentrancyGuard\\n    struct ReentrancyGuardStorage {\\n        uint256 _status;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.ReentrancyGuard\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant ReentrancyGuardStorageLocation = 0x9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00;\\n\\n    function _getReentrancyGuardStorage() private pure returns (ReentrancyGuardStorage storage $) {\\n        assembly {\\n            $.slot := ReentrancyGuardStorageLocation\\n        }\\n    }\\n\\n    /**\\n     * @dev Unauthorized reentrant call.\\n     */\\n    error ReentrancyGuardReentrantCall();\\n\\n    function __ReentrancyGuard_init() internal onlyInitializing {\\n        __ReentrancyGuard_init_unchained();\\n    }\\n\\n    function __ReentrancyGuard_init_unchained() internal onlyInitializing {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        $._status = NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and making it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        _nonReentrantBefore();\\n        _;\\n        _nonReentrantAfter();\\n    }\\n\\n    function _nonReentrantBefore() private {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\\n        if ($._status == ENTERED) {\\n            revert ReentrancyGuardReentrantCall();\\n        }\\n\\n        // Any calls to nonReentrant after this point will fail\\n        $._status = ENTERED;\\n    }\\n\\n    function _nonReentrantAfter() private {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        $._status = NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Returns true if the reentrancy guard is currently set to \\\"entered\\\", which indicates there is a\\n     * `nonReentrant` function in the call stack.\\n     */\\n    function _reentrancyGuardEntered() internal view returns (bool) {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        return $._status == ENTERED;\\n    }\\n}\\n\",\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (interfaces/IERC1363.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"./IERC20.sol\\\";\\nimport {IERC165} from \\\"./IERC165.sol\\\";\\n\\n/**\\n * @title IERC1363\\n * @dev Interface of the ERC-1363 standard as defined in the https://eips.ethereum.org/EIPS/eip-1363[ERC-1363].\\n *\\n * Defines an extension interface for ERC-20 tokens that supports executing code on a recipient contract\\n * after `transfer` or `transferFrom`, or code on a spender contract after `approve`, in a single transaction.\\n */\\ninterface IERC1363 is IERC20, IERC165 {\\n    /*\\n     * Note: the ERC-165 identifier for this interface is 0xb0202a11.\\n     * 0xb0202a11 ===\\n     *   bytes4(keccak256('transferAndCall(address,uint256)')) ^\\n     *   bytes4(keccak256('transferAndCall(address,uint256,bytes)')) ^\\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256)')) ^\\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256,bytes)')) ^\\n     *   bytes4(keccak256('approveAndCall(address,uint256)')) ^\\n     *   bytes4(keccak256('approveAndCall(address,uint256,bytes)'))\\n     */\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferAndCall(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @param data Additional data with no specified format, sent in call to `to`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferAndCall(address to, uint256 value, bytes calldata data) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param from The address which you want to send tokens from.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferFromAndCall(address from, address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param from The address which you want to send tokens from.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @param data Additional data with no specified format, sent in call to `to`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferFromAndCall(address from, address to, uint256 value, bytes calldata data) external returns (bool);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\\n     * @param spender The address which will spend the funds.\\n     * @param value The amount of tokens to be spent.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function approveAndCall(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\\n     * @param spender The address which will spend the funds.\\n     * @param value The amount of tokens to be spent.\\n     * @param data Additional data with no specified format, sent in call to `spender`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function approveAndCall(address spender, uint256 value, bytes calldata data) external returns (bool);\\n}\\n\",\"keccak256\":\"0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC165} from \\\"../utils/introspection/IERC165.sol\\\";\\n\",\"keccak256\":\"0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../token/ERC20/IERC20.sol\\\";\\n\",\"keccak256\":\"0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC-20 standard as defined in the ERC.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the value of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the value of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\\n     * allowance mechanism. `value` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\\n}\\n\",\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (token/ERC20/utils/SafeERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../IERC20.sol\\\";\\nimport {IERC1363} from \\\"../../../interfaces/IERC1363.sol\\\";\\n\\n/**\\n * @title SafeERC20\\n * @dev Wrappers around ERC-20 operations that throw on failure (when the token\\n * contract returns false). Tokens that return no value (and instead revert or\\n * throw on failure) are also supported, non-reverting calls are assumed to be\\n * successful.\\n * To use this library you can add a `using SafeERC20 for IERC20;` statement to your contract,\\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\\n */\\nlibrary SafeERC20 {\\n    /**\\n     * @dev An operation with an ERC-20 token failed.\\n     */\\n    error SafeERC20FailedOperation(address token);\\n\\n    /**\\n     * @dev Indicates a failed `decreaseAllowance` request.\\n     */\\n    error SafeERC20FailedDecreaseAllowance(address spender, uint256 currentAllowance, uint256 requestedDecrease);\\n\\n    /**\\n     * @dev Transfer `value` amount of `token` from the calling contract to `to`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful.\\n     */\\n    function safeTransfer(IERC20 token, address to, uint256 value) internal {\\n        _callOptionalReturn(token, abi.encodeCall(token.transfer, (to, value)));\\n    }\\n\\n    /**\\n     * @dev Transfer `value` amount of `token` from `from` to `to`, spending the approval given by `from` to the\\n     * calling contract. If `token` returns no value, non-reverting calls are assumed to be successful.\\n     */\\n    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\\n        _callOptionalReturn(token, abi.encodeCall(token.transferFrom, (from, to, value)));\\n    }\\n\\n    /**\\n     * @dev Variant of {safeTransfer} that returns a bool instead of reverting if the operation is not successful.\\n     */\\n    function trySafeTransfer(IERC20 token, address to, uint256 value) internal returns (bool) {\\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transfer, (to, value)));\\n    }\\n\\n    /**\\n     * @dev Variant of {safeTransferFrom} that returns a bool instead of reverting if the operation is not successful.\\n     */\\n    function trySafeTransferFrom(IERC20 token, address from, address to, uint256 value) internal returns (bool) {\\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transferFrom, (from, to, value)));\\n    }\\n\\n    /**\\n     * @dev Increase the calling contract's allowance toward `spender` by `value`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful.\\n     *\\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \\\"client\\\"\\n     * smart contract uses ERC-7674 to set temporary allowances, then the \\\"client\\\" smart contract should avoid using\\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\\n     */\\n    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n        uint256 oldAllowance = token.allowance(address(this), spender);\\n        forceApprove(token, spender, oldAllowance + value);\\n    }\\n\\n    /**\\n     * @dev Decrease the calling contract's allowance toward `spender` by `requestedDecrease`. If `token` returns no\\n     * value, non-reverting calls are assumed to be successful.\\n     *\\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \\\"client\\\"\\n     * smart contract uses ERC-7674 to set temporary allowances, then the \\\"client\\\" smart contract should avoid using\\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\\n     */\\n    function safeDecreaseAllowance(IERC20 token, address spender, uint256 requestedDecrease) internal {\\n        unchecked {\\n            uint256 currentAllowance = token.allowance(address(this), spender);\\n            if (currentAllowance < requestedDecrease) {\\n                revert SafeERC20FailedDecreaseAllowance(spender, currentAllowance, requestedDecrease);\\n            }\\n            forceApprove(token, spender, currentAllowance - requestedDecrease);\\n        }\\n    }\\n\\n    /**\\n     * @dev Set the calling contract's allowance toward `spender` to `value`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful. Meant to be used with tokens that require the approval\\n     * to be set to zero before setting it to a non-zero value, such as USDT.\\n     *\\n     * NOTE: If the token implements ERC-7674, this function will not modify any temporary allowance. This function\\n     * only sets the \\\"standard\\\" allowance. Any temporary allowance will remain active, in addition to the value being\\n     * set here.\\n     */\\n    function forceApprove(IERC20 token, address spender, uint256 value) internal {\\n        bytes memory approvalCall = abi.encodeCall(token.approve, (spender, value));\\n\\n        if (!_callOptionalReturnBool(token, approvalCall)) {\\n            _callOptionalReturn(token, abi.encodeCall(token.approve, (spender, 0)));\\n            _callOptionalReturn(token, approvalCall);\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} transferAndCall, with a fallback to the simple {ERC20} transfer if the target has no\\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function transferAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\\n        if (to.code.length == 0) {\\n            safeTransfer(token, to, value);\\n        } else if (!token.transferAndCall(to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} transferFromAndCall, with a fallback to the simple {ERC20} transferFrom if the target\\n     * has no code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function transferFromAndCallRelaxed(\\n        IERC1363 token,\\n        address from,\\n        address to,\\n        uint256 value,\\n        bytes memory data\\n    ) internal {\\n        if (to.code.length == 0) {\\n            safeTransferFrom(token, from, to, value);\\n        } else if (!token.transferFromAndCall(from, to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} approveAndCall, with a fallback to the simple {ERC20} approve if the target has no\\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * NOTE: When the recipient address (`to`) has no code (i.e. is an EOA), this function behaves as {forceApprove}.\\n     * Opposedly, when the recipient address (`to`) has code, this function only attempts to call {ERC1363-approveAndCall}\\n     * once without retrying, and relies on the returned value to be true.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function approveAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\\n        if (to.code.length == 0) {\\n            forceApprove(token, to, value);\\n        } else if (!token.approveAndCall(to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     *\\n     * This is a variant of {_callOptionalReturnBool} that reverts if call fails to meet the requirements.\\n     */\\n    function _callOptionalReturn(IERC20 token, bytes memory data) private {\\n        uint256 returnSize;\\n        uint256 returnValue;\\n        assembly (\\\"memory-safe\\\") {\\n            let success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\\n            // bubble errors\\n            if iszero(success) {\\n                let ptr := mload(0x40)\\n                returndatacopy(ptr, 0, returndatasize())\\n                revert(ptr, returndatasize())\\n            }\\n            returnSize := returndatasize()\\n            returnValue := mload(0)\\n        }\\n\\n        if (returnSize == 0 ? address(token).code.length == 0 : returnValue != 1) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     *\\n     * This is a variant of {_callOptionalReturn} that silently catches all reverts and returns a bool instead.\\n     */\\n    function _callOptionalReturnBool(IERC20 token, bytes memory data) private returns (bool) {\\n        bool success;\\n        uint256 returnSize;\\n        uint256 returnValue;\\n        assembly (\\\"memory-safe\\\") {\\n            success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\\n            returnSize := returndatasize()\\n            returnValue := mload(0)\\n        }\\n        return success && (returnSize == 0 ? address(token).code.length > 0 : returnValue == 1);\\n    }\\n}\\n\",\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC-165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[ERC].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\"},\"contracts/TokenLock.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\\\";\\n\\n/**\\n * @title TokenLock\\n */\\ncontract TokenLock is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {\\n    using SafeERC20 for IERC20;\\n\\n    struct Configuration {\\n        address admin;\\n    }\\n\\n    Configuration public config;\\n\\n    struct LockContract {\\n        address token;\\n        uint256 amount;\\n        uint256 startTime;\\n        uint256 endTime;\\n        address recipient;\\n        address locker;\\n        bool closed;\\n    }\\n\\n    mapping(uint256 => LockContract) public locks;\\n    uint256 public nextLockId;\\n\\n    // Events\\n    event LockCreated(\\n        uint256 indexed lockId,\\n        address indexed locker,\\n        address indexed recipient,\\n        address tokenAddress,\\n        uint256 amount,\\n        uint256 startTime,\\n        uint256 endTime\\n    );\\n\\n    event TokensWithdrawn(\\n        uint256 indexed lockId, address indexed sender, address indexed recipient, uint256 amount\\n    );\\n\\n    event ConfigUpdated(address oldAdmin, address newAdmin);\\n\\n    // Errors\\n    error InvalidParams();\\n    error Unauthorized();\\n    error ContractClosed();\\n    error InvalidLockId();\\n\\n    /**\\n     * @notice Initialize the contract\\n     */\\n    function initialize() external initializer {\\n        __ReentrancyGuard_init();\\n        __Ownable_init(msg.sender);\\n\\n        config = Configuration({ admin: msg.sender });\\n        nextLockId = 1;\\n    }\\n\\n    /**\\n     * @notice Create a new time-locked token contract\\n     * @param token Address of the ERC20 token to lock\\n     * @param amount Amount of tokens to lock\\n     * @param endTime End time of the lock in seconds\\n     * @param recipient Address that will be able to claim the tokens after the lock period\\n     * @return lockId The ID of the created lock\\n     */\\n    function createLock(\\n        address token,\\n        uint256 amount,\\n        uint256 endTime,\\n        address recipient\\n    )\\n        external\\n        nonReentrant\\n        returns (uint256 lockId)\\n    {\\n        uint256 startTime = block.timestamp;\\n\\n        if (endTime <= startTime) revert InvalidParams();\\n        if (token == address(0) || recipient == address(0)) revert InvalidParams();\\n\\n        lockId = nextLockId++;\\n        locks[lockId] = LockContract({\\n            token: token,\\n            amount: amount,\\n            startTime: startTime,\\n            endTime: endTime,\\n            recipient: recipient,\\n            locker: msg.sender,\\n            closed: false\\n        });\\n\\n        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);\\n\\n        emit LockCreated(lockId, msg.sender, recipient, token, amount, startTime, endTime);\\n\\n        return lockId;\\n    }\\n\\n    /**\\n     * @notice Withdraw tokens from a lock after the lock period has ended\\n     * @param lockId ID of the lock to withdraw from\\n     */\\n    function withdraw(uint256 lockId) external nonReentrant {\\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\\n\\n        LockContract storage lockContract = locks[lockId];\\n        address sender = msg.sender;\\n\\n        if (\\n            sender != config.admin && sender != lockContract.recipient\\n                && sender != lockContract.locker\\n        ) {\\n            revert Unauthorized();\\n        }\\n        if (lockContract.closed) revert ContractClosed();\\n        if (block.timestamp < lockContract.endTime) revert Unauthorized();\\n\\n        lockContract.closed = true;\\n        IERC20(lockContract.token).safeTransfer(lockContract.recipient, lockContract.amount);\\n\\n        emit TokensWithdrawn(lockId, sender, lockContract.recipient, lockContract.amount);\\n    }\\n\\n    /**\\n     * @notice Extend the lock duration of an existing time-locked token contract\\n     * @param lockId ID of the lock to extend\\n     * @param newEndTime New end time for the lock\\n     */\\n    function extendLock(uint256 lockId, uint256 newEndTime) external {\\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\\n\\n        LockContract storage lockContract = locks[lockId];\\n        address sender = msg.sender;\\n\\n        if (sender != lockContract.locker) revert Unauthorized();\\n        if (lockContract.closed) revert ContractClosed();\\n\\n        uint256 currentTime = block.timestamp;\\n        if (newEndTime <= currentTime || newEndTime <= lockContract.endTime) {\\n            revert InvalidParams();\\n        }\\n\\n        lockContract.endTime = newEndTime;\\n\\n        emit LockCreated(\\n            lockId,\\n            lockContract.locker,\\n            lockContract.recipient,\\n            lockContract.token,\\n            lockContract.amount,\\n            lockContract.startTime,\\n            lockContract.endTime\\n        );\\n    }\\n\\n    /**\\n     * @notice Update configuration\\n     * @param newAdmin New admin address\\n     */\\n    function updateConfig(address newAdmin) external onlyOwner {\\n        if (newAdmin == address(0)) revert InvalidParams();\\n\\n        address oldAdmin = config.admin;\\n\\n        config.admin = newAdmin;\\n\\n        emit ConfigUpdated(oldAdmin, newAdmin);\\n    }\\n\\n    /**\\n     * @notice Get lock information\\n     * @param lockId ID of the lock\\n     * @return lockContract The lock contract details\\n     */\\n    function getLock(uint256 lockId) external view returns (LockContract memory lockContract) {\\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\\n        return locks[lockId];\\n    }\\n\\n    /**\\n     * @notice Check if a lock is withdrawable\\n     * @param lockId ID of the lock\\n     * @return withdrawable True if the lock can be withdrawn\\n     */\\n    function isWithdrawable(uint256 lockId) external view returns (bool withdrawable) {\\n        if (lockId == 0 || lockId >= nextLockId) return false;\\n\\n        LockContract memory lockContract = locks[lockId];\\n        return !lockContract.closed && block.timestamp >= lockContract.endTime;\\n    }\\n\\n    /**\\n     * @notice Get the number of locks created\\n     * @return count Total number of locks\\n     */\\n    function getLockCount() external view returns (uint256 count) {\\n        return nextLockId - 1;\\n    }\\n\\n    /**\\n     * @notice Get current configuration\\n     * @return admin Current admin address\\n     */\\n    function getConfig() external view returns (address admin) {\\n        return config.admin;\\n    }\\n\\n    /**\\n     * @notice Emergency withdraw function (admin only)\\n     * @param token Token address to withdraw\\n     * @param amount Amount to withdraw\\n     */\\n    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {\\n        IERC20(token).safeTransfer(config.admin, amount);\\n    }\\n}\\n\",\"keccak256\":\"0xb16fdd788b49ab321fb26fc15572e783f467e2124d61d92224cbd1f79770ffae\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"errors": {"InvalidInitialization()": [{"details": "The contract is already initialized."}], "NotInitializing()": [{"details": "The contract is not initializing."}], "OwnableInvalidOwner(address)": [{"details": "The owner is not a valid owner account. (eg. `address(0)`)"}], "OwnableUnauthorizedAccount(address)": [{"details": "The caller account is not authorized to perform an operation."}], "ReentrancyGuardReentrantCall()": [{"details": "Unauthorized reentrant call."}], "SafeERC20FailedOperation(address)": [{"details": "An operation with an ERC-20 token failed."}]}, "events": {"Initialized(uint64)": {"details": "Triggered when the contract has been initialized or reinitialized."}}, "kind": "dev", "methods": {"createLock(address,uint256,uint256,address)": {"params": {"amount": "Amount of tokens to lock", "endTime": "End time of the lock in seconds", "recipient": "Address that will be able to claim the tokens after the lock period", "token": "Address of the ERC20 token to lock"}, "returns": {"lockId": "The ID of the created lock"}}, "emergencyWithdraw(address,uint256)": {"params": {"amount": "Amount to withdraw", "token": "Token address to withdraw"}}, "extendLock(uint256,uint256)": {"params": {"lockId": "ID of the lock to extend", "newEndTime": "New end time for the lock"}}, "getConfig()": {"returns": {"admin": "Current admin address"}}, "getLock(uint256)": {"params": {"lockId": "ID of the lock"}, "returns": {"lockContract": "The lock contract details"}}, "getLockCount()": {"returns": {"count": "Total number of locks"}}, "isWithdrawable(uint256)": {"params": {"lockId": "ID of the lock"}, "returns": {"withdrawable": "True if the lock can be withdrawn"}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "updateConfig(address)": {"params": {"newAdmin": "New admin address"}}, "withdraw(uint256)": {"params": {"lockId": "ID of the lock to withdraw from"}}}, "title": "TokenLock", "version": 1}, "userdoc": {"kind": "user", "methods": {"createLock(address,uint256,uint256,address)": {"notice": "Create a new time-locked token contract"}, "emergencyWithdraw(address,uint256)": {"notice": "Emergency withdraw function (admin only)"}, "extendLock(uint256,uint256)": {"notice": "Extend the lock duration of an existing time-locked token contract"}, "getConfig()": {"notice": "Get current configuration"}, "getLock(uint256)": {"notice": "Get lock information"}, "getLockCount()": {"notice": "Get the number of locks created"}, "initialize()": {"notice": "Initialize the contract"}, "isWithdrawable(uint256)": {"notice": "Check if a lock is withdrawable"}, "updateConfig(address)": {"notice": "Update configuration"}, "withdraw(uint256)": {"notice": "Withdraw tokens from a lock after the lock period has ended"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 10604, "contract": "contracts/TokenLock.sol:TokenLock", "label": "config", "offset": 0, "slot": "0", "type": "t_struct(Configuration)10601_storage"}, {"astId": 10624, "contract": "contracts/TokenLock.sol:TokenLock", "label": "locks", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_struct(LockContract)10619_storage)"}, {"astId": 10626, "contract": "contracts/TokenLock.sol:TokenLock", "label": "nextLockId", "offset": 0, "slot": "2", "type": "t_uint256"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_mapping(t_uint256,t_struct(LockContract)10619_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct TokenLock.LockContract)", "numberOfBytes": "32", "value": "t_struct(LockContract)10619_storage"}, "t_struct(Configuration)10601_storage": {"encoding": "inplace", "label": "struct TokenLock.Configuration", "members": [{"astId": 10600, "contract": "contracts/TokenLock.sol:TokenLock", "label": "admin", "offset": 0, "slot": "0", "type": "t_address"}], "numberOfBytes": "32"}, "t_struct(LockContract)10619_storage": {"encoding": "inplace", "label": "struct TokenLock.LockContract", "members": [{"astId": 10606, "contract": "contracts/TokenLock.sol:TokenLock", "label": "token", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 10608, "contract": "contracts/TokenLock.sol:TokenLock", "label": "amount", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 10610, "contract": "contracts/TokenLock.sol:TokenLock", "label": "startTime", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 10612, "contract": "contracts/TokenLock.sol:TokenLock", "label": "endTime", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 10614, "contract": "contracts/TokenLock.sol:TokenLock", "label": "recipient", "offset": 0, "slot": "4", "type": "t_address"}, {"astId": 10616, "contract": "contracts/TokenLock.sol:TokenLock", "label": "locker", "offset": 0, "slot": "5", "type": "t_address"}, {"astId": 10618, "contract": "contracts/TokenLock.sol:TokenLock", "label": "closed", "offset": 20, "slot": "5", "type": "t_bool"}], "numberOfBytes": "192"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}