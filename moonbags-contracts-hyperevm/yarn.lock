# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@adraffy/ens-normalize@npm:1.10.0":
  version: 1.10.0
  resolution: "@adraffy/ens-normalize@npm:1.10.0"
  checksum: 10/5cdb5d2a9c9f8c0a71a7bb830967da0069cae1f1235cd41ae11147e4000f368f6958386e622cd4d52bf45c1ed3f8275056b387cba28902b83354e40ff323ecde
  languageName: node
  linkType: hard

"@adraffy/ens-normalize@npm:1.10.1":
  version: 1.10.1
  resolution: "@adraffy/ens-normalize@npm:1.10.1"
  checksum: 10/4cb938c4abb88a346d50cb0ea44243ab3574330c81d4f5aaaf9dfee584b96189d0faa404de0fcbef5a1b73909ea4ebc3e63d84bd23f9949e5c8d4085207a5091
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.22.13, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10/721b8a6e360a1fa0f1c9fe7351ae6c874828e119183688b533c477aa378f1010f37cc9afbfc4722c686d1f5cdd00da02eab4ba7278a0c504fa0d7a321dcd4fdf
  languageName: node
  linkType: hard

"@babel/generator@npm:7.17.7":
  version: 7.17.7
  resolution: "@babel/generator@npm:7.17.7"
  dependencies:
    "@babel/types": "npm:^7.17.0"
    jsesc: "npm:^2.5.1"
    source-map: "npm:^0.5.0"
  checksum: 10/3303afa2b1310e67071d6c998121b2af1038d6450df266d24fe9a86329fb6288b8ab38bb71787917b3f81a1c4edbc5bc7e6735683fe1d0aa288b33e93daacd60
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.23.0":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10/f5e6942670cb32156b3ac2d75ce09b373558823387f15dd1413c27fe9eb5756a7c6011fc7f956c7acc53efb530bfb28afffa24364d46c4e9ffccc4e5c8b3b094
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.22.20":
  version: 7.24.7
  resolution: "@babel/helper-environment-visitor@npm:7.24.7"
  dependencies:
    "@babel/types": "npm:^7.24.7"
  checksum: 10/079d86e65701b29ebc10baf6ed548d17c19b808a07aa6885cc141b690a78581b180ee92b580d755361dc3b16adf975b2d2058b8ce6c86675fcaf43cf22f2f7c6
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.23.0":
  version: 7.24.7
  resolution: "@babel/helper-function-name@npm:7.24.7"
  dependencies:
    "@babel/template": "npm:^7.24.7"
    "@babel/types": "npm:^7.24.7"
  checksum: 10/2ceb3d9b2b35a0fc4100fc06ed7be3bc38f03ff0bf128ff0edbc0cc7dd842967b1496fc70b5c616c747d7711c2b87e7d025c8888f48740631d6148a9d3614f85
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.22.5":
  version: 7.24.7
  resolution: "@babel/helper-hoist-variables@npm:7.24.7"
  dependencies:
    "@babel/types": "npm:^7.24.7"
  checksum: 10/6cfdcf2289cd12185dcdbdf2435fa8d3447b797ac75851166de9fc8503e2fd0021db6baf8dfbecad3753e582c08e6a3f805c8d00cbed756060a877d705bd8d8d
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.24.7
  resolution: "@babel/helper-split-export-declaration@npm:7.24.7"
  dependencies:
    "@babel/types": "npm:^7.24.7"
  checksum: 10/ff04a3071603c87de0d6ee2540b7291ab36305b329bd047cdbb6cbd7db335a12f9a77af1cf708779f75f13c4d9af46093c00b34432e50b2411872c658d1a2e5e
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10/0ae29cc2005084abdae2966afdb86ed14d41c9c37db02c3693d5022fba9f5d59b011d039380b8e537c34daf117c549f52b452398f576e908fb9db3c7abbb3a00
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.16.7, @babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10/75041904d21bdc0cd3b07a8ac90b11d64cd3c881e89cb936fa80edd734bf23c35e6bd1312611e8574c4eab1f3af0f63e8a5894f4699e9cfdf70c06fcf4252320
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.20.5, @babel/parser@npm:^7.23.0, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/0ad671be7994dba7d31ec771bd70ea5090aa34faf73e93b1b072e3c0a704ab69f4a7a68ebfb9d6a7fa455e0aa03dfa65619c4df6bae1cf327cba925b1d233fc4
  languageName: node
  linkType: hard

"@babel/template@npm:^7.24.7":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/fed15a84beb0b9340e5f81566600dbee5eccd92e4b9cc42a944359b1aa1082373391d9d5fc3656981dff27233ec935d0bc96453cf507f60a4b079463999244d8
  languageName: node
  linkType: hard

"@babel/traverse@npm:7.23.2":
  version: 7.23.2
  resolution: "@babel/traverse@npm:7.23.2"
  dependencies:
    "@babel/code-frame": "npm:^7.22.13"
    "@babel/generator": "npm:^7.23.0"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-hoist-variables": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    "@babel/parser": "npm:^7.23.0"
    "@babel/types": "npm:^7.23.0"
    debug: "npm:^4.1.0"
    globals: "npm:^11.1.0"
  checksum: 10/e4fcb8f8395804956df4ae1301230a14b6eb35b74a7058a0e0b40f6f4be7281e619e6dafe400e833d4512da5d61cf17ea177d04b00a8f7cf3d8d69aff83ca3d8
  languageName: node
  linkType: hard

"@babel/types@npm:7.17.0":
  version: 7.17.0
  resolution: "@babel/types@npm:7.17.0"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.16.7"
    to-fast-properties: "npm:^2.0.0"
  checksum: 10/535ccef360d0c74e2bb685050f3a45e6ab30f66c740bbdd0858148ed502043f1ae2006a9d0269ac3b7356b690091ae313efd912e408bc0198d80a14b2a6f1537
  languageName: node
  linkType: hard

"@babel/types@npm:^7.17.0, @babel/types@npm:^7.23.0, @babel/types@npm:^7.24.7, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3":
  version: 7.27.6
  resolution: "@babel/types@npm:7.27.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10/174741c667775680628a09117828bbeffb35ea543f59bf80649d0d60672f7815a0740ddece3cca87516199033a039166a6936434131fce2b6a820227e64f91ae
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: 10/9d226461c1e91e95f067be2bdc5e6f99cfe55a721f45afb44122e23e4b8602eeac4ff7325af6b5a369f36396ee1514d3809af3f57769066d80d83790d8e53339
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": "npm:0.3.9"
  checksum: 10/b6e38a1712fab242c86a241c229cf562195aad985d0564bd352ac404be583029e89e93028ffd2c251d2c407ecac5fb0cbdca94a2d5c10f29ac806ede0508b3ff
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10/43ed5d391526d9f5bbe452aef336389a473026fca92057cf97c576db11401ce9bcf8ef0bf72625bbaf6207ed8ba6bf0dcf4d7e809c24f08faa68a28533c491a7
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.5.1, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10/c08f1dd7dd18fbb60bdd0d85820656d1374dd898af9be7f82cb00451313402a22d5e30569c150315b4385907cdbca78c22389b2a72ab78883b3173be317620cc
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^9.6.0"
    globals: "npm:^13.19.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10/7a3b14f4b40fc1a22624c3f84d9f467a3d9ea1ca6e9a372116cb92507e485260359465b58e25bcb6c9981b155416b98c9973ad9b796053fd7b3f776a6946bce8
  languageName: node
  linkType: hard

"@eslint/js@npm:8.57.1":
  version: 8.57.1
  resolution: "@eslint/js@npm:8.57.1"
  checksum: 10/7562b21be10c2adbfa4aa5bb2eccec2cb9ac649a3569560742202c8d1cb6c931ce634937a2f0f551e078403a1c1285d6c2c0aa345dafc986149665cd69fe8b59
  languageName: node
  linkType: hard

"@ethereumjs/rlp@npm:^4.0.1":
  version: 4.0.1
  resolution: "@ethereumjs/rlp@npm:4.0.1"
  bin:
    rlp: bin/rlp
  checksum: 10/bfdffd634ce72f3b17e3d085d071f2fe7ce9680aebdf10713d74b30afd80ef882d17f19ff7175fcb049431a56e800bd3558d3b028bd0d82341927edb303ab450
  languageName: node
  linkType: hard

"@ethereumjs/rlp@npm:^5.0.2":
  version: 5.0.2
  resolution: "@ethereumjs/rlp@npm:5.0.2"
  bin:
    rlp: bin/rlp.cjs
  checksum: 10/2af80d98faf7f64dfb6d739c2df7da7350ff5ad52426c3219897e843ee441215db0ffa346873200a6be6d11142edb9536e66acd62436b5005fa935baaf7eb6bd
  languageName: node
  linkType: hard

"@ethereumjs/util@npm:^8.1.0":
  version: 8.1.0
  resolution: "@ethereumjs/util@npm:8.1.0"
  dependencies:
    "@ethereumjs/rlp": "npm:^4.0.1"
    ethereum-cryptography: "npm:^2.0.0"
    micro-ftch: "npm:^0.3.1"
  checksum: 10/cc35338932e49b15e54ca6e548b32a1f48eed7d7e1d34ee743e4d3600dd616668bd50f70139e86c5c35f55aac35fba3b6cc4e6f679cf650aeba66bf93016200c
  languageName: node
  linkType: hard

"@ethereumjs/util@npm:^9.1.0":
  version: 9.1.0
  resolution: "@ethereumjs/util@npm:9.1.0"
  dependencies:
    "@ethereumjs/rlp": "npm:^5.0.2"
    ethereum-cryptography: "npm:^2.2.1"
  checksum: 10/4e22c4081c63eebb808eccd54f7f91cd3407f4cac192da5f30a0d6983fe07d51f25e6a9d08624f1376e604bb7dce574aafcf0fbf0becf42f62687c11e710ac41
  languageName: node
  linkType: hard

"@ethersproject/abi@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abi@npm:5.7.0"
  dependencies:
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 10/6ed002cbc61a7e21bc0182702345659c1984f6f8e6bad166e43aee76ea8f74766dd0f6236574a868e1b4600af27972bf25b973fae7877ae8da3afa90d3965cac
  languageName: node
  linkType: hard

"@ethersproject/abi@npm:5.8.0, @ethersproject/abi@npm:^5.0.9, @ethersproject/abi@npm:^5.1.2, @ethersproject/abi@npm:^5.7.0, @ethersproject/abi@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/abi@npm:5.8.0"
  dependencies:
    "@ethersproject/address": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/constants": "npm:^5.8.0"
    "@ethersproject/hash": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
  checksum: 10/a63ebc2c8ea795ceca5289abaf817bb402c83c330cffd0ae2d355be70c54050a21ddd408abd4fd0dce4c3fd5c5f091707be2095011c233022a52f2110e7012d6
  languageName: node
  linkType: hard

"@ethersproject/abstract-provider@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-provider@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/networks": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/web": "npm:^5.7.0"
  checksum: 10/c03e413a812486002525f4036bf2cb90e77a19b98fa3d16279e28e0a05520a1085690fac2ee9f94b7931b9a803249ff8a8bbb26ff8dee52196a6ef7a3fc5edc5
  languageName: node
  linkType: hard

"@ethersproject/abstract-provider@npm:5.8.0, @ethersproject/abstract-provider@npm:^5.7.0, @ethersproject/abstract-provider@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/abstract-provider@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/networks": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/transactions": "npm:^5.8.0"
    "@ethersproject/web": "npm:^5.8.0"
  checksum: 10/2066aa717c7ecf0b6defe47f4f0af21943ee76e47f6fdc461d89b15d8af76c37d25355b4f5d635ed30e7378eafb0599b283df8ef9133cef389d938946874200d
  languageName: node
  linkType: hard

"@ethersproject/abstract-signer@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-signer@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
  checksum: 10/0a6ffade0a947c9ba617048334e1346838f394d1d0a5307ac435a0c63ed1033b247e25ffb0cd6880d7dcf5459581f52f67e3804ebba42ff462050f1e4321ba0c
  languageName: node
  linkType: hard

"@ethersproject/abstract-signer@npm:5.8.0, @ethersproject/abstract-signer@npm:^5.7.0, @ethersproject/abstract-signer@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/abstract-signer@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
  checksum: 10/10986eb1520dd94efb34bc19de4f53a49bea023493a0df686711872eb2cb446f3cca3c98c1ecec7831497004822e16ead756d6c7d6977971eaa780f4d41db327
  languageName: node
  linkType: hard

"@ethersproject/address@npm:5.6.1":
  version: 5.6.1
  resolution: "@ethersproject/address@npm:5.6.1"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.6.2"
    "@ethersproject/bytes": "npm:^5.6.1"
    "@ethersproject/keccak256": "npm:^5.6.1"
    "@ethersproject/logger": "npm:^5.6.0"
    "@ethersproject/rlp": "npm:^5.6.1"
  checksum: 10/630cf3203c8d9d57a4551e2c9b290a0009bdb591d42e1db9535bd7b3a345329148d180a6b1c98e52d51d40fd3caa1af0555feae8473db1b99d18d2b270c7854b
  languageName: node
  linkType: hard

"@ethersproject/address@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/address@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
  checksum: 10/1ac4f3693622ed9fbbd7e966a941ec1eba0d9445e6e8154b1daf8e93b8f62ad91853d1de5facf4c27b41e6f1e47b94a317a2492ba595bee1841fd3030c3e9a27
  languageName: node
  linkType: hard

"@ethersproject/address@npm:5.8.0, @ethersproject/address@npm:^5.0.2, @ethersproject/address@npm:^5.7.0, @ethersproject/address@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/address@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/rlp": "npm:^5.8.0"
  checksum: 10/4b8ef5b3001f065fae571d86f113395d0dd081a2f411c99e354da912d4138e14a1fbe206265725daeb55c4e735ddb761891b58779208c5e2acec03f3219ce6ef
  languageName: node
  linkType: hard

"@ethersproject/base64@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/base64@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
  checksum: 10/7105105f401e1c681e61db1e9da1b5960d8c5fbd262bbcacc99d61dbb9674a9db1181bb31903d98609f10e8a0eb64c850475f3b040d67dea953e2b0ac6380e96
  languageName: node
  linkType: hard

"@ethersproject/base64@npm:5.8.0, @ethersproject/base64@npm:^5.7.0, @ethersproject/base64@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/base64@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
  checksum: 10/c83e4ee01a1e69d874277d05c0e3fbc2afcdb9c80507be6963d31c77e505e355191cbba2d8fecf1c922b68c1ff072ede7914981fd965f1d8771c5b0706beb911
  languageName: node
  linkType: hard

"@ethersproject/basex@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/basex@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
  checksum: 10/840e333e109bff2fcf8d91dcfd45fa951835844ef0e1ba710037e87291c7b5f3c189ba86f6cee2ca7de2ede5b7d59fbb930346607695855bee20d2f9f63371ef
  languageName: node
  linkType: hard

"@ethersproject/basex@npm:5.8.0, @ethersproject/basex@npm:^5.7.0, @ethersproject/basex@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/basex@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
  checksum: 10/1a8d48a9397461ea42ec43b69a15a0d13ba0b9192695713750d9d391503c55b258cca435fa78a4014d23a813053f1a471593b89c7c0d89351639a78d50a12ef2
  languageName: node
  linkType: hard

"@ethersproject/bignumber@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bignumber@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    bn.js: "npm:^5.2.1"
  checksum: 10/09cffa18a9f0730856b57c14c345bd68ba451159417e5aff684a8808011cd03b27b7c465d423370333a7d1c9a621392fc74f064a3b02c9edc49ebe497da6d45d
  languageName: node
  linkType: hard

"@ethersproject/bignumber@npm:5.8.0, @ethersproject/bignumber@npm:^5.5.0, @ethersproject/bignumber@npm:^5.6.2, @ethersproject/bignumber@npm:^5.7.0, @ethersproject/bignumber@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/bignumber@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    bn.js: "npm:^5.2.1"
  checksum: 10/15538ba9eef8475bc14a2a2bb5f0d7ae8775cf690283cb4c7edc836761a4310f83d67afe33f6d0b8befd896b10f878d8ca79b89de6e6ebd41a9e68375ec77123
  languageName: node
  linkType: hard

"@ethersproject/bytes@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bytes@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10/8b3ffedb68c1a82cfb875e9738361409cc33e2dcb1286b6ccfdc4dd8dd0317f7eacc8937b736c467d213dffc44b469690fe1a951e901953d5a90c5af2b675ae4
  languageName: node
  linkType: hard

"@ethersproject/bytes@npm:5.8.0, @ethersproject/bytes@npm:^5.6.1, @ethersproject/bytes@npm:^5.7.0, @ethersproject/bytes@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/bytes@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10/b8956aa4f607d326107cec522a881effed62585d5b5c5ad66ada4f7f83b42fd6c6acb76f355ec7a57e4cadea62a0194e923f4b5142d50129fe03d2fe7fc664f8
  languageName: node
  linkType: hard

"@ethersproject/constants@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/constants@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
  checksum: 10/6d4b1355747cce837b3e76ec3bde70e4732736f23b04f196f706ebfa5d4d9c2be50904a390d4d40ce77803b98d03d16a9b6898418e04ba63491933ce08c4ba8a
  languageName: node
  linkType: hard

"@ethersproject/constants@npm:5.8.0, @ethersproject/constants@npm:^5.7.0, @ethersproject/constants@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/constants@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.8.0"
  checksum: 10/74830c44f4315a1058b905c73be7a9bb92850e45213cb28a957447b8a100f22a514f4500b0ea5ac7a995427cecef9918af39ae4e0e0ecf77aa4835b1ea5c3432
  languageName: node
  linkType: hard

"@ethersproject/contracts@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/contracts@npm:5.7.0"
  dependencies:
    "@ethersproject/abi": "npm:^5.7.0"
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
  checksum: 10/5df66179af242faabea287a83fd2f8f303a4244dc87a6ff802e1e3b643f091451295c8e3d088c7739970b7915a16a581c192d4e007d848f1fdf3cc9e49010053
  languageName: node
  linkType: hard

"@ethersproject/contracts@npm:5.8.0, @ethersproject/contracts@npm:^5.7.0":
  version: 5.8.0
  resolution: "@ethersproject/contracts@npm:5.8.0"
  dependencies:
    "@ethersproject/abi": "npm:^5.8.0"
    "@ethersproject/abstract-provider": "npm:^5.8.0"
    "@ethersproject/abstract-signer": "npm:^5.8.0"
    "@ethersproject/address": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/constants": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/transactions": "npm:^5.8.0"
  checksum: 10/839f8211f5e560f15468ae843ba316ffeacab5cebcece1eec76bc5714472ebfe3453484f283d3e46b9d3faaffef1e17cc3583cf24e01638a1fd52f69012cf8d4
  languageName: node
  linkType: hard

"@ethersproject/hash@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/hash@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 10/d83de3f3a1b99b404a2e7bb503f5cdd90c66a97a32cce1d36b09bb8e3fb7205b96e30ad28e2b9f30083beea6269b157d0c6e3425052bb17c0a35fddfdd1c72a3
  languageName: node
  linkType: hard

"@ethersproject/hash@npm:5.8.0, @ethersproject/hash@npm:^5.7.0, @ethersproject/hash@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/hash@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.8.0"
    "@ethersproject/address": "npm:^5.8.0"
    "@ethersproject/base64": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
  checksum: 10/a355cc1120b51c5912d960c66e2d1e2fb9cceca7d02e48c3812abd32ac2480035d8345885f129d2ed1cde9fb044adad1f98e4ea39652fa96c5de9c2720e83d28
  languageName: node
  linkType: hard

"@ethersproject/hdnode@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/hdnode@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/basex": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/pbkdf2": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/wordlists": "npm:^5.7.0"
  checksum: 10/2fbe6278c324235afaa88baa5dea24d8674c72b14ad037fe2096134d41025977f410b04fd146e333a1b6cac9482e9de62d6375d1705fd42667543f2d0eb66655
  languageName: node
  linkType: hard

"@ethersproject/hdnode@npm:5.8.0, @ethersproject/hdnode@npm:^5.7.0, @ethersproject/hdnode@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/hdnode@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.8.0"
    "@ethersproject/basex": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/pbkdf2": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/sha2": "npm:^5.8.0"
    "@ethersproject/signing-key": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
    "@ethersproject/transactions": "npm:^5.8.0"
    "@ethersproject/wordlists": "npm:^5.8.0"
  checksum: 10/55b35cf30f0dd40e2d5ecd4b2f005ebea82a85a440717a61d4a483074f652d2c7063e9c704272b894bfdd500f7883aa36692931c6808591f702c1da7107ebb61
  languageName: node
  linkType: hard

"@ethersproject/json-wallets@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/json-wallets@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/hdnode": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/pbkdf2": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/random": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    aes-js: "npm:3.0.0"
    scrypt-js: "npm:3.0.1"
  checksum: 10/4a1ef0912ffc8d18c392ae4e292948d86bffd715fe3dd3e66d1cd21f6c9267aeadad4da84261db853327f97cdfd765a377f9a87e39d4c6749223a69226faf0a1
  languageName: node
  linkType: hard

"@ethersproject/json-wallets@npm:5.8.0, @ethersproject/json-wallets@npm:^5.7.0, @ethersproject/json-wallets@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/json-wallets@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.8.0"
    "@ethersproject/address": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/hdnode": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/pbkdf2": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/random": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
    "@ethersproject/transactions": "npm:^5.8.0"
    aes-js: "npm:3.0.0"
    scrypt-js: "npm:3.0.1"
  checksum: 10/5cbf7e698ee7f26f54fceb672d9824b01816cd785182e638cb5cd1eaed5d80d8a4576e3cad92af46ac6d23404a806a47a72d5dee908af42322d091553a0d8da6
  languageName: node
  linkType: hard

"@ethersproject/keccak256@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/keccak256@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    js-sha3: "npm:0.8.0"
  checksum: 10/ff70950d82203aab29ccda2553422cbac2e7a0c15c986bd20a69b13606ed8bb6e4fdd7b67b8d3b27d4f841e8222cbaccd33ed34be29f866fec7308f96ed244c6
  languageName: node
  linkType: hard

"@ethersproject/keccak256@npm:5.8.0, @ethersproject/keccak256@npm:^5.6.1, @ethersproject/keccak256@npm:^5.7.0, @ethersproject/keccak256@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/keccak256@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    js-sha3: "npm:0.8.0"
  checksum: 10/af3621d2b18af6c8f5181dacad91e1f6da4e8a6065668b20e4c24684bdb130b31e45e0d4dbaed86d4f1314d01358aa119f05be541b696e455424c47849d81913
  languageName: node
  linkType: hard

"@ethersproject/logger@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/logger@npm:5.7.0"
  checksum: 10/683a939f467ae7510deedc23d7611d0932c3046137f5ffb92ba1e3c8cd9cf2fbbaa676b660c248441a0fa9143783137c46d6e6d17d676188dd5a6ef0b72dd091
  languageName: node
  linkType: hard

"@ethersproject/logger@npm:5.8.0, @ethersproject/logger@npm:^5.6.0, @ethersproject/logger@npm:^5.7.0, @ethersproject/logger@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/logger@npm:5.8.0"
  checksum: 10/dab862d6cc3a4312f4c49d62b4a603f4b60707da8b8ff0fee6bdfee3cbed48b34ec8f23fedfef04dd3d24f2fa2d7ad2be753c775aa00fe24dcd400631d65004a
  languageName: node
  linkType: hard

"@ethersproject/networks@npm:5.7.1":
  version: 5.7.1
  resolution: "@ethersproject/networks@npm:5.7.1"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10/5265d0b4b72ef91af57be804b44507f4943038d609699764d8a69157ed381e30fe22ebf63630ed8e530ceb220f15d69dae8cda2e5023ccd793285c9d5882e599
  languageName: node
  linkType: hard

"@ethersproject/networks@npm:5.8.0, @ethersproject/networks@npm:^5.7.0, @ethersproject/networks@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/networks@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10/8e2f4c3fd3a701ebd3d767a5f3217f8ced45a9f8ebf830c73b2dd87107dd50777f4869c3c9cc946698e2c597d3fe53eadeec55d19af7769c7d6bdb4a1493fb6f
  languageName: node
  linkType: hard

"@ethersproject/pbkdf2@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/pbkdf2@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
  checksum: 10/dea7ba747805e24b81dfb99e695eb329509bf5cad1a42e48475ade28e060e567458a3d5bf930f302691bded733fd3fa364f0c7adce920f9f05a5ef8c13267aaa
  languageName: node
  linkType: hard

"@ethersproject/pbkdf2@npm:5.8.0, @ethersproject/pbkdf2@npm:^5.7.0, @ethersproject/pbkdf2@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/pbkdf2@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/sha2": "npm:^5.8.0"
  checksum: 10/203bb992eec3042256702f4c8259a37202af7b341cc6e370614cdc52541042fc3b795fb040592bd6be8b67376a798c45312ca1e6d5d179c3e8eb7431882f1fd1
  languageName: node
  linkType: hard

"@ethersproject/properties@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/properties@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10/f8401a161940aa1c32695115a20c65357877002a6f7dc13ab1600064bf54d7b825b4db49de8dc8da69efcbb0c9f34f8813e1540427e63e262ab841c1bf6c1c1e
  languageName: node
  linkType: hard

"@ethersproject/properties@npm:5.8.0, @ethersproject/properties@npm:^5.7.0, @ethersproject/properties@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/properties@npm:5.8.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10/3bc1af678c1cf7c87f39aec24b1d86cfaa5da1f9f54e426558701fff1c088c1dcc9ec3e1f395e138bdfcda94a0161e7192f0596e11c8ff25d31735e6b33edc59
  languageName: node
  linkType: hard

"@ethersproject/providers@npm:5.7.2":
  version: 5.7.2
  resolution: "@ethersproject/providers@npm:5.7.2"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/basex": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/networks": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/random": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/web": "npm:^5.7.0"
    bech32: "npm:1.1.4"
    ws: "npm:7.4.6"
  checksum: 10/8534a1896e61b9f0b66427a639df64a5fe76d0c08ec59b9f0cc64fdd1d0cc28d9fc3312838ae8d7817c8f5e2e76b7f228b689bc33d1cbb8e1b9517d4c4f678d8
  languageName: node
  linkType: hard

"@ethersproject/providers@npm:5.8.0, @ethersproject/providers@npm:^5.7.2":
  version: 5.8.0
  resolution: "@ethersproject/providers@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.8.0"
    "@ethersproject/abstract-signer": "npm:^5.8.0"
    "@ethersproject/address": "npm:^5.8.0"
    "@ethersproject/base64": "npm:^5.8.0"
    "@ethersproject/basex": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/constants": "npm:^5.8.0"
    "@ethersproject/hash": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/networks": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/random": "npm:^5.8.0"
    "@ethersproject/rlp": "npm:^5.8.0"
    "@ethersproject/sha2": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
    "@ethersproject/transactions": "npm:^5.8.0"
    "@ethersproject/web": "npm:^5.8.0"
    bech32: "npm:1.1.4"
    ws: "npm:8.18.0"
  checksum: 10/7d40fc0abb78fc9e69b71cb560beb2a93cf1da2cf978a061031a34c0ed76c2f5936ed8c0bdb9aa1307fe5308d0159e429b83b779dbd550639a886a88d6d17817
  languageName: node
  linkType: hard

"@ethersproject/random@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/random@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10/c23ec447998ce1147651bd58816db4d12dbeb404f66a03d14a13e1edb439879bab18528e1fc46b931502903ac7b1c08ea61d6a86e621a6e060fa63d41aeed3ac
  languageName: node
  linkType: hard

"@ethersproject/random@npm:5.8.0, @ethersproject/random@npm:^5.7.0, @ethersproject/random@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/random@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10/47c34a72c81183ac13a1b4635bb9d5cf1456e6329276f50c9e12711f404a9eb4536db824537ed05ef8839a0a358883dc3342d3ea83147b8bafeb767dc8f57e23
  languageName: node
  linkType: hard

"@ethersproject/rlp@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/rlp@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10/3b8c5279f7654794d5874569f5598ae6a880e19e6616013a31e26c35c5f586851593a6e85c05ed7b391fbc74a1ea8612dd4d867daefe701bf4e8fcf2ab2f29b9
  languageName: node
  linkType: hard

"@ethersproject/rlp@npm:5.8.0, @ethersproject/rlp@npm:^5.6.1, @ethersproject/rlp@npm:^5.7.0, @ethersproject/rlp@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/rlp@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10/353f04618f44c822d20da607b055286b3374fc6ab9fc50b416140f21e410f6d6e89ff9d951bef667b8baf1314e2d5f0b47c5615c3f994a2c8b2d6c01c6329bb4
  languageName: node
  linkType: hard

"@ethersproject/sha2@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/sha2@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    hash.js: "npm:1.1.7"
  checksum: 10/09321057c022effbff4cc2d9b9558228690b5dd916329d75c4b1ffe32ba3d24b480a367a7cc92d0f0c0b1c896814d03351ae4630e2f1f7160be2bcfbde435dbc
  languageName: node
  linkType: hard

"@ethersproject/sha2@npm:5.8.0, @ethersproject/sha2@npm:^5.7.0, @ethersproject/sha2@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/sha2@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    hash.js: "npm:1.1.7"
  checksum: 10/ef8916e3033502476fba9358ba1993722ac3bb99e756d5681e4effa3dfa0f0bf0c29d3fa338662830660b45dd359cccb06ba40bc7b62cfd44f4a177b25829404
  languageName: node
  linkType: hard

"@ethersproject/signing-key@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/signing-key@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    bn.js: "npm:^5.2.1"
    elliptic: "npm:6.5.4"
    hash.js: "npm:1.1.7"
  checksum: 10/ff2f79ded86232b139e7538e4aaa294c6022a7aaa8c95a6379dd7b7c10a6d363685c6967c816f98f609581cf01f0a5943c667af89a154a00bcfe093a8c7f3ce7
  languageName: node
  linkType: hard

"@ethersproject/signing-key@npm:5.8.0, @ethersproject/signing-key@npm:^5.7.0, @ethersproject/signing-key@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/signing-key@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    bn.js: "npm:^5.2.1"
    elliptic: "npm:6.6.1"
    hash.js: "npm:1.1.7"
  checksum: 10/07e5893bf9841e1d608c52b58aa240ed10c7aa01613ff45b15c312c1403887baa8ed543871721052d7b7dd75d80b1fa90945377b231d18ccb6986c6677c8315d
  languageName: node
  linkType: hard

"@ethersproject/solidity@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/solidity@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 10/9a02f37f801c96068c3e7721f83719d060175bc4e80439fe060e92bd7acfcb6ac1330c7e71c49f4c2535ca1308f2acdcb01e00133129aac00581724c2d6293f3
  languageName: node
  linkType: hard

"@ethersproject/solidity@npm:5.8.0, @ethersproject/solidity@npm:^5.7.0":
  version: 5.8.0
  resolution: "@ethersproject/solidity@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/sha2": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
  checksum: 10/305166f3f8e8c2f5ad7b0b03ab96d52082fc79b5136601175e1c76d7abd8fd8e3e4b56569dea745dfa2b7fcbfd180c5d824b03fea7e08dd53d515738a35e51dd
  languageName: node
  linkType: hard

"@ethersproject/strings@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/strings@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10/24191bf30e98d434a9fba2f522784f65162d6712bc3e1ccc98ed85c5da5884cfdb5a1376b7695374655a7b95ec1f5fdbeef5afc7d0ea77ffeb78047e9b791fa5
  languageName: node
  linkType: hard

"@ethersproject/strings@npm:5.8.0, @ethersproject/strings@npm:^5.7.0, @ethersproject/strings@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/strings@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/constants": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10/536264dad4b9ad42d8287be7b7a9f3e243d0172fafa459e22af2d416eb6fe6a46ff623ca5456457f841dec4b080939da03ed02ab9774dcd1f2391df9ef5a96bb
  languageName: node
  linkType: hard

"@ethersproject/transactions@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/transactions@npm:5.7.0"
  dependencies:
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
  checksum: 10/d809e9d40020004b7de9e34bf39c50377dce8ed417cdf001bfabc81ecb1b7d1e0c808fdca0a339ea05e1b380648eaf336fe70f137904df2d3c3135a38190a5af
  languageName: node
  linkType: hard

"@ethersproject/transactions@npm:5.8.0, @ethersproject/transactions@npm:^5.7.0, @ethersproject/transactions@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/transactions@npm:5.8.0"
  dependencies:
    "@ethersproject/address": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/constants": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/rlp": "npm:^5.8.0"
    "@ethersproject/signing-key": "npm:^5.8.0"
  checksum: 10/b43fd97ee359154c9162037c7aedc23abafae3cedf78d8fd2e641e820a0443120d22c473ec9bb79e8301f179f61a6120d61b0b757560e3aad8ae2110127018ba
  languageName: node
  linkType: hard

"@ethersproject/units@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/units@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 10/304714f848cd32e57df31bf545f7ad35c2a72adae957198b28cbc62166daa929322a07bff6e9c9ac4577ab6aa0de0546b065ed1b2d20b19e25748b7d475cb0fc
  languageName: node
  linkType: hard

"@ethersproject/units@npm:5.8.0, @ethersproject/units@npm:^5.7.0":
  version: 5.8.0
  resolution: "@ethersproject/units@npm:5.8.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/constants": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
  checksum: 10/cc7180c85f695449c20572602971145346fc5c169ee32f23d79ac31cc8c9c66a2049e3ac852b940ddccbe39ab1db3b81e3e093b604d9ab7ab27639ecb933b270
  languageName: node
  linkType: hard

"@ethersproject/wallet@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/wallet@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/hdnode": "npm:^5.7.0"
    "@ethersproject/json-wallets": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/random": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/wordlists": "npm:^5.7.0"
  checksum: 10/340f8e5c77c6c47c4d1596c200d97c53c1d4b4eb54d9166d0f2a114cb81685e7689255b0627e917fbcdc29cb54c4bd1f1a9909f3096ef9dff9acc0b24972f1c1
  languageName: node
  linkType: hard

"@ethersproject/wallet@npm:5.8.0, @ethersproject/wallet@npm:^5.7.0":
  version: 5.8.0
  resolution: "@ethersproject/wallet@npm:5.8.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.8.0"
    "@ethersproject/abstract-signer": "npm:^5.8.0"
    "@ethersproject/address": "npm:^5.8.0"
    "@ethersproject/bignumber": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/hash": "npm:^5.8.0"
    "@ethersproject/hdnode": "npm:^5.8.0"
    "@ethersproject/json-wallets": "npm:^5.8.0"
    "@ethersproject/keccak256": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/random": "npm:^5.8.0"
    "@ethersproject/signing-key": "npm:^5.8.0"
    "@ethersproject/transactions": "npm:^5.8.0"
    "@ethersproject/wordlists": "npm:^5.8.0"
  checksum: 10/354c8985a74b1bb0a8ba80f374c1af882f7657716b974dda235184ee98151e30741b24f58a93c84693aa6e72a8a5c3ae62143966967f40f52f62093559388e6a
  languageName: node
  linkType: hard

"@ethersproject/web@npm:5.7.1":
  version: 5.7.1
  resolution: "@ethersproject/web@npm:5.7.1"
  dependencies:
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 10/c83b6b3ac40573ddb67b1750bb4cf21ded7d8555be5e53a97c0f34964622fd88de9220a90a118434bae164a2bff3acbdc5ecb990517b5f6dc32bdad7adf604c2
  languageName: node
  linkType: hard

"@ethersproject/web@npm:5.8.0, @ethersproject/web@npm:^5.7.0, @ethersproject/web@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/web@npm:5.8.0"
  dependencies:
    "@ethersproject/base64": "npm:^5.8.0"
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
  checksum: 10/93aad7041ffae7a4f881cc8df3356a297d736b50e6e48952b3b76e547b83e4d9189bbf2f417543031e91e74568c54395d1bb43c3252c3adf4f7e1c0187012912
  languageName: node
  linkType: hard

"@ethersproject/wordlists@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/wordlists@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 10/737fca67ad743a32020f50f5b9e147e5683cfba2692367c1124a5a5538be78515865257b426ec9141daac91a70295e5e21bef7a193b79fe745f1be378562ccaa
  languageName: node
  linkType: hard

"@ethersproject/wordlists@npm:5.8.0, @ethersproject/wordlists@npm:^5.7.0, @ethersproject/wordlists@npm:^5.8.0":
  version: 5.8.0
  resolution: "@ethersproject/wordlists@npm:5.8.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.8.0"
    "@ethersproject/hash": "npm:^5.8.0"
    "@ethersproject/logger": "npm:^5.8.0"
    "@ethersproject/properties": "npm:^5.8.0"
    "@ethersproject/strings": "npm:^5.8.0"
  checksum: 10/b8e6aa7d2195bb568847f360f6525ddc3d145404fbd4553e2e05daf4a95f58167591feb69e16e3398a28114ea85e1895fc8f5bd1c0cbf8b578123d7c1d21c32d
  languageName: node
  linkType: hard

"@fastify/busboy@npm:^2.0.0":
  version: 2.1.1
  resolution: "@fastify/busboy@npm:2.1.1"
  checksum: 10/2bb8a7eca8289ed14c9eb15239bc1019797454624e769b39a0b90ed204d032403adc0f8ed0d2aef8a18c772205fa7808cf5a1b91f21c7bfc7b6032150b1062c5
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.13.0":
  version: 0.13.0
  resolution: "@humanwhocodes/config-array@npm:0.13.0"
  dependencies:
    "@humanwhocodes/object-schema": "npm:^2.0.3"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.0.5"
  checksum: 10/524df31e61a85392a2433bf5d03164e03da26c03d009f27852e7dcfdafbc4a23f17f021dacf88e0a7a9fe04ca032017945d19b57a16e2676d9114c22a53a9d11
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10/e993950e346331e5a32eefb27948ecdee2a2c4ab3f072b8f566cd213ef485dd50a3ca497050608db91006f5479e43f91a439aef68d2a313bd3ded06909c7c5b3
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.3":
  version: 2.0.3
  resolution: "@humanwhocodes/object-schema@npm:2.0.3"
  checksum: 10/05bb99ed06c16408a45a833f03a732f59bf6184795d4efadd33238ff8699190a8c871ad1121241bb6501589a9598dc83bf25b99dcbcf41e155cdf36e35e937a3
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/9d3a56ab3612ab9b85d38b2a93b87f3324f11c5130859957f6500e4ac8ce35f299d5ccc3ecd1ae87597601ecf83cee29e9afd04c18777c24011073992ff946df
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3, @jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10/97106439d750a409c22c8bff822d648f6a71f3aa9bc8e5129efdc36343cd3096ddc4eeb1c62d2fe48e9bdd4db37b05d4646a17114ecebd3bbcacfa2de51c3c1d
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10/832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10/4ed6123217569a1484419ac53f6ea0d9f3b57e5b57ab30d7c267bdb27792a27eb0e4b08e84a2680aa55cc2f2b411ffd6ec3db01c44fdc6dc43aca4b55f8374fd
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.0.3"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
  checksum: 10/83deafb8e7a5ca98993c2c6eeaa93c270f6f647a4c0dc00deb38c9cf9b2d3b7bf15e8839540155247ef034a052c0ec4466f980bf0c9e2ab63b97d16c0cedd3ff
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10/dced32160a44b49d531b80a4a2159dceab6b3ddf0c8e95a0deae4b0e894b172defa63d5ac52a19c2068e1fe7d31ea4ba931fbeec103233ecb4208953967120fc
  languageName: node
  linkType: hard

"@metamask/eth-sig-util@npm:^4.0.0":
  version: 4.0.1
  resolution: "@metamask/eth-sig-util@npm:4.0.1"
  dependencies:
    ethereumjs-abi: "npm:^0.6.8"
    ethereumjs-util: "npm:^6.2.1"
    ethjs-util: "npm:^0.1.6"
    tweetnacl: "npm:^1.0.3"
    tweetnacl-util: "npm:^0.15.1"
  checksum: 10/a41a986abd14675badeb02041466e30e1c3ef529c1d131f47c27fd48d73144fcf590f45d8ee8b7cd357725ebf75ece93f4484adf1baf6311cc996f7ef82c4ae1
  languageName: node
  linkType: hard

"@noble/curves@npm:1.2.0, @noble/curves@npm:~1.2.0":
  version: 1.2.0
  resolution: "@noble/curves@npm:1.2.0"
  dependencies:
    "@noble/hashes": "npm:1.3.2"
  checksum: 10/94e02e9571a9fd42a3263362451849d2f54405cb3ce9fa7c45bc6b9b36dcd7d1d20e2e1e14cfded24937a13d82f1e60eefc4d7a14982ce0bc219a9fc0f51d1f9
  languageName: node
  linkType: hard

"@noble/curves@npm:1.4.2, @noble/curves@npm:~1.4.0":
  version: 1.4.2
  resolution: "@noble/curves@npm:1.4.2"
  dependencies:
    "@noble/hashes": "npm:1.4.0"
  checksum: 10/f433a2e8811ae345109388eadfa18ef2b0004c1f79417553241db4f0ad0d59550be6298a4f43d989c627e9f7551ffae6e402a4edf0173981e6da95fc7cab5123
  languageName: node
  linkType: hard

"@noble/curves@npm:~1.8.1":
  version: 1.8.2
  resolution: "@noble/curves@npm:1.8.2"
  dependencies:
    "@noble/hashes": "npm:1.7.2"
  checksum: 10/540e7b7a8fe92ecd5cef846f84d07180662eb7fd7d8e9172b8960c31827e74f148fe4630da962138a6be093ae9f8992d14ab23d3682a2cc32be839aa57c03a46
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.2.0, @noble/hashes@npm:~1.2.0":
  version: 1.2.0
  resolution: "@noble/hashes@npm:1.2.0"
  checksum: 10/c295684a2799f4ddad10a855efd9b82c70c27ac5f7437642df9700e120087c796851dd95b12d2e7596802303fe6afbfdf0f8733b5c7453f70c4c080746dde6ff
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.3.2":
  version: 1.3.2
  resolution: "@noble/hashes@npm:1.3.2"
  checksum: 10/685f59d2d44d88e738114b71011d343a9f7dce9dfb0a121f1489132f9247baa60bc985e5ec6f3213d114fbd1e1168e7294644e46cbd0ce2eba37994f28eeb51b
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.4.0, @noble/hashes@npm:~1.4.0":
  version: 1.4.0
  resolution: "@noble/hashes@npm:1.4.0"
  checksum: 10/e156e65794c473794c52fa9d06baf1eb20903d0d96719530f523cc4450f6c721a957c544796e6efd0197b2296e7cd70efeb312f861465e17940a3e3c7e0febc6
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.2, @noble/hashes@npm:~1.7.1":
  version: 1.7.2
  resolution: "@noble/hashes@npm:1.7.2"
  checksum: 10/b5af9e4b91543dcc46a811b5b2c57bfdeb41728361979a19d6110a743e2cb0459872553f68d3a46326d21959964db2776b8c8b4db85ac1d9f63ebcaddf7d59b6
  languageName: node
  linkType: hard

"@noble/hashes@npm:^1.4.0":
  version: 1.8.0
  resolution: "@noble/hashes@npm:1.8.0"
  checksum: 10/474b7f56bc6fb2d5b3a42132561e221b0ea4f91e590f4655312ca13667840896b34195e2b53b7f097ec080a1fdd3b58d902c2a8d0fbdf51d2e238b53808a177e
  languageName: node
  linkType: hard

"@noble/hashes@npm:~1.3.0, @noble/hashes@npm:~1.3.2":
  version: 1.3.3
  resolution: "@noble/hashes@npm:1.3.3"
  checksum: 10/1025ddde4d24630e95c0818e63d2d54ee131b980fe113312d17ed7468bc18f54486ac86c907685759f8a7e13c2f9b9e83ec7b67d1cc20836f36b5e4a65bb102d
  languageName: node
  linkType: hard

"@noble/secp256k1@npm:1.7.1":
  version: 1.7.1
  resolution: "@noble/secp256k1@npm:1.7.1"
  checksum: 10/214d4756c20ed20809d948d0cc161e95664198cb127266faf747fd7deffe5444901f05fe9f833787738f2c6e60b09e544c2f737f42f73b3699e3999ba15b1b63
  languageName: node
  linkType: hard

"@noble/secp256k1@npm:~1.7.0":
  version: 1.7.2
  resolution: "@noble/secp256k1@npm:1.7.2"
  checksum: 10/ce1651f63ebf9269990dbc1002410d63e2f4379fcbf2bb7a567740997852a1fbdb353929e497cd9d09549c28e3edb036ac3905ff20ac5249ad32e64a852af955
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10/6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10/012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10/40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@nomicfoundation/edr-darwin-arm64@npm:0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr-darwin-arm64@npm:0.11.0"
  checksum: 10/83d95fb41f9ef35b01677ec33c5ad9f498b1ae69d1eb97bddd998e8a3b82f98dc72b7e744e7e8e948ff3208638677bea9061ed96c304190f4e8cb0d933435dba
  languageName: node
  linkType: hard

"@nomicfoundation/edr-darwin-arm64@npm:0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr-darwin-arm64@npm:0.5.2"
  checksum: 10/201d75a79cc2482ad7522bec5492426bd9d9324a15e0285fb7357ca8d656f43f0bc9121ad3767cc2629d108530450e396712eeb0226ef7872ead4db108ce5fdd
  languageName: node
  linkType: hard

"@nomicfoundation/edr-darwin-x64@npm:0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr-darwin-x64@npm:0.11.0"
  checksum: 10/9fdf303162210377aa2f405ace40c7139306157bed95968f463b59842d44e0b7398f79e2f00439c17c21705d44000e5311c4aa4b72d88acc00ee09401ba67920
  languageName: node
  linkType: hard

"@nomicfoundation/edr-darwin-x64@npm:0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr-darwin-x64@npm:0.5.2"
  checksum: 10/d64d18471895334a492ee279388e9975f05d6aef03a49a9d48f1a8a15a01fb3854be871724473dc577d6c99d0440ce90e590ad3990eba54aa1ced6bed5827c30
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-arm64-gnu@npm:0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr-linux-arm64-gnu@npm:0.11.0"
  checksum: 10/bb732395f6bda45b1b8df2eb1723614df4cebd2143a3dbf8e2eb6095f72e6ba426144498ade1773fb36e6adf8f528d7feed1dd470a4711e320930e1051b8964f
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-arm64-gnu@npm:0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr-linux-arm64-gnu@npm:0.5.2"
  checksum: 10/b2370caba6562c960e344f03aefb9c5cbdcfe96fef26481c7173867dd4486d0bf0b7d779fcd378b840d201b6021249fc689e99e71f44291371b10025b6eaaf71
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-arm64-musl@npm:0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr-linux-arm64-musl@npm:0.11.0"
  checksum: 10/18e1ac98bf696bbc80fa0f3b065ed816005dcc3936b5b21274d67d3ac98d6b5af8b09485bb97134b6da4402492e689159431aa02539b720b4a600e110e7d0868
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-arm64-musl@npm:0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr-linux-arm64-musl@npm:0.5.2"
  checksum: 10/386bfec2bd2c4e2dfd6cd01418c0c97842e0a9668dd6095532659a0028f9517eadc5d2046a3fcac8a9498111cbed4f289a6e6fa5d6468254cd126ad7500117c8
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-x64-gnu@npm:0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr-linux-x64-gnu@npm:0.11.0"
  checksum: 10/ccaab482f36462a936826caacc1704736ba32d1645a07a29cd48ae687bb60b0b3742c92a57c7e324a7c58b5a7e9e55b490ebd36aca21d110515e81d660dbb4a2
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-x64-gnu@npm:0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr-linux-x64-gnu@npm:0.5.2"
  checksum: 10/feac8d6b9505b8ee01834375d209fc601a1c4d5b059674194db1d9bd4f1ab60204e3f4391867a868db5cc4fb6a27d8f8d4fa2b1cef380057f8235de1da1a69ff
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-x64-musl@npm:0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr-linux-x64-musl@npm:0.11.0"
  checksum: 10/bb98860f4f223bcd4513936dd2de59890033dd758871bd96a03f1ea8f534823c0cc6b3929481720ec6b58baca6c209541a7165d7cc4f6fd12feb6e5e5ce2a868
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-x64-musl@npm:0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr-linux-x64-musl@npm:0.5.2"
  checksum: 10/278ce49c69b088d15c5ad18794b01b18afd6cbb1a739bd2aed3a880a73c0369a3f60dd72dad5a4a605da5a14a8971e7a7513514bd8479a2a1ebe630b329f1b17
  languageName: node
  linkType: hard

"@nomicfoundation/edr-win32-x64-msvc@npm:0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr-win32-x64-msvc@npm:0.11.0"
  checksum: 10/db521d72e8db646d080a461b0f47c861a857a3858163205564efa047afd4a20d9b83bd4f5948adf7e77e553fe7548054a61c0d8f9a14af41f71e098b13cf2ee0
  languageName: node
  linkType: hard

"@nomicfoundation/edr-win32-x64-msvc@npm:0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr-win32-x64-msvc@npm:0.5.2"
  checksum: 10/032ef94e5eb59dea16d243f59e23f4327179cd21c99329d2c3b7a0372d58924c310fd3831af08fa26a9fa04bb99aa19ac0e537b9cc6d6b2880b0d204571054fa
  languageName: node
  linkType: hard

"@nomicfoundation/edr@npm:^0.11.0":
  version: 0.11.0
  resolution: "@nomicfoundation/edr@npm:0.11.0"
  dependencies:
    "@nomicfoundation/edr-darwin-arm64": "npm:0.11.0"
    "@nomicfoundation/edr-darwin-x64": "npm:0.11.0"
    "@nomicfoundation/edr-linux-arm64-gnu": "npm:0.11.0"
    "@nomicfoundation/edr-linux-arm64-musl": "npm:0.11.0"
    "@nomicfoundation/edr-linux-x64-gnu": "npm:0.11.0"
    "@nomicfoundation/edr-linux-x64-musl": "npm:0.11.0"
    "@nomicfoundation/edr-win32-x64-msvc": "npm:0.11.0"
  checksum: 10/1b1c8fb37de2c5b8a755abcce2910d8291ab129e260a51060c7cb0f71e0e7df31be947a3abddd2cfce883adfa56900f7010b84f6062353e23e6d48d1d72df79b
  languageName: node
  linkType: hard

"@nomicfoundation/edr@npm:^0.5.2":
  version: 0.5.2
  resolution: "@nomicfoundation/edr@npm:0.5.2"
  dependencies:
    "@nomicfoundation/edr-darwin-arm64": "npm:0.5.2"
    "@nomicfoundation/edr-darwin-x64": "npm:0.5.2"
    "@nomicfoundation/edr-linux-arm64-gnu": "npm:0.5.2"
    "@nomicfoundation/edr-linux-arm64-musl": "npm:0.5.2"
    "@nomicfoundation/edr-linux-x64-gnu": "npm:0.5.2"
    "@nomicfoundation/edr-linux-x64-musl": "npm:0.5.2"
    "@nomicfoundation/edr-win32-x64-msvc": "npm:0.5.2"
  checksum: 10/c1dad20ae11888962fbb24edb0af74395037fed9fdeef6598f4e5ce4ed16c5517fa7592d39901c535da64705ac0726dce9b82d4fcda4528c975e25a24cb28306
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-common@npm:4.0.4":
  version: 4.0.4
  resolution: "@nomicfoundation/ethereumjs-common@npm:4.0.4"
  dependencies:
    "@nomicfoundation/ethereumjs-util": "npm:9.0.4"
  checksum: 10/1daaede087c5dee92cb1e5309a548da2d64484722b917eccda4118d627293b61f705a990075f4d7f0f350100ed79396b3a25e7ea67824242d36d23716fe75e97
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-rlp@npm:5.0.4":
  version: 5.0.4
  resolution: "@nomicfoundation/ethereumjs-rlp@npm:5.0.4"
  bin:
    rlp: bin/rlp.cjs
  checksum: 10/39fb26340bb2643a66c642315aa7b6fcfbdbddddeee18b4b683b77aa93b8a031bc86d4d4144368e5dd20499dc96b8b27751c6a285ff34e7a9969b530b306ce8c
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-tx@npm:5.0.4":
  version: 5.0.4
  resolution: "@nomicfoundation/ethereumjs-tx@npm:5.0.4"
  dependencies:
    "@nomicfoundation/ethereumjs-common": "npm:4.0.4"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.4"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.4"
    ethereum-cryptography: "npm:0.1.3"
  peerDependencies:
    c-kzg: ^2.1.2
  peerDependenciesMeta:
    c-kzg:
      optional: true
  checksum: 10/5e84de14fa464501c5c60ac6519f536d39ebc52c4d1fb79c63a66ea86f992bde4f338b0b0fdd2e5bc811ebd984e8ff41e4205e47d30001bad5b45370568bc41c
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-util@npm:9.0.4":
  version: 9.0.4
  resolution: "@nomicfoundation/ethereumjs-util@npm:9.0.4"
  dependencies:
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.4"
    ethereum-cryptography: "npm:0.1.3"
  peerDependencies:
    c-kzg: ^2.1.2
  peerDependenciesMeta:
    c-kzg:
      optional: true
  checksum: 10/891806c7edda29c7b3f61551949ff0c1fa5f4e122fba84878bf27362a9e058768fd01194dc0e031de2e523c30ecbeb22e6841b8ab3772c8567fef4af6480872d
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-chai-matchers@npm:^2.0.7":
  version: 2.0.8
  resolution: "@nomicfoundation/hardhat-chai-matchers@npm:2.0.8"
  dependencies:
    "@types/chai-as-promised": "npm:^7.1.3"
    chai-as-promised: "npm:^7.1.1"
    deep-eql: "npm:^4.0.1"
    ordinal: "npm:^1.0.3"
  peerDependencies:
    "@nomicfoundation/hardhat-ethers": ^3.0.0
    chai: ^4.2.0
    ethers: ^6.1.0
    hardhat: ^2.9.4
  checksum: 10/1e40d2ae1627d9c62c90e85f60d38a24768198b3f525659f958c44d395d751876be13198e20ffd2b3336b8650225652610f0c36f0e376c8ee6b0854bcd0f6bb0
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-ethers@npm:^3.0.8":
  version: 3.0.8
  resolution: "@nomicfoundation/hardhat-ethers@npm:3.0.8"
  dependencies:
    debug: "npm:^4.1.1"
    lodash.isequal: "npm:^4.5.0"
  peerDependencies:
    ethers: ^6.1.0
    hardhat: ^2.0.0
  checksum: 10/5fe3d1bbf598ad232040ed3df8990c325098ade46bc00e389b9f1c51e0eafad6bab66f373e9814cdbeaede62c43182956def68e71e40142b5651d88402c4f294
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-foundry@npm:^1.1.2":
  version: 1.1.3
  resolution: "@nomicfoundation/hardhat-foundry@npm:1.1.3"
  dependencies:
    picocolors: "npm:^1.1.0"
  peerDependencies:
    hardhat: ^2.17.2
  checksum: 10/c953b825905a986c21b6d0bf7b08439a0d9e553db3a50d97fa3ffaed6cb3b6d3e42d7892a98ff53372101eb976891433c17d515342648fbb060ad7c59a88e784
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-ignition-ethers@npm:^0.15.5":
  version: 0.15.11
  resolution: "@nomicfoundation/hardhat-ignition-ethers@npm:0.15.11"
  peerDependencies:
    "@nomicfoundation/hardhat-ethers": ^3.0.4
    "@nomicfoundation/hardhat-ignition": ^0.15.11
    "@nomicfoundation/ignition-core": ^0.15.11
    ethers: ^6.7.0
    hardhat: ^2.18.0
  checksum: 10/705716b8c9d5cf563a295499fc07ee1c53307b93afffd96aca4256be385670ba87efdad5b2658579722c3f863d1a50d5b6d90de3ffdc57cac19aa50dfe453a92
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-ignition@npm:^0.15.5":
  version: 0.15.11
  resolution: "@nomicfoundation/hardhat-ignition@npm:0.15.11"
  dependencies:
    "@nomicfoundation/ignition-core": "npm:^0.15.11"
    "@nomicfoundation/ignition-ui": "npm:^0.15.11"
    chalk: "npm:^4.0.0"
    debug: "npm:^4.3.2"
    fs-extra: "npm:^10.0.0"
    json5: "npm:^2.2.3"
    prompts: "npm:^2.4.2"
  peerDependencies:
    "@nomicfoundation/hardhat-verify": ^2.0.1
    hardhat: ^2.18.0
  checksum: 10/c471d59406d22de381eea48c23954afd2088fd135c06c32f0be7fd30bc8054a3d0f0c7de85e25d9bc5331145e216325bb8b1c96b038f21f8336abfdd194aa27a
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-network-helpers@npm:^1.0.11":
  version: 1.0.12
  resolution: "@nomicfoundation/hardhat-network-helpers@npm:1.0.12"
  dependencies:
    ethereumjs-util: "npm:^7.1.4"
  peerDependencies:
    hardhat: ^2.9.5
  checksum: 10/6774bdaa76b6792b408dbdf9b4ff5c19315fa9d5be53e53d6b68cd56aba82d18cfa2548a71a2ced41542989bddd3ab4d11e009579d34678202e26f11c43c2a44
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-toolbox@npm:^5.0.0":
  version: 5.0.0
  resolution: "@nomicfoundation/hardhat-toolbox@npm:5.0.0"
  peerDependencies:
    "@nomicfoundation/hardhat-chai-matchers": ^2.0.0
    "@nomicfoundation/hardhat-ethers": ^3.0.0
    "@nomicfoundation/hardhat-ignition-ethers": ^0.15.0
    "@nomicfoundation/hardhat-network-helpers": ^1.0.0
    "@nomicfoundation/hardhat-verify": ^2.0.0
    "@typechain/ethers-v6": ^0.5.0
    "@typechain/hardhat": ^9.0.0
    "@types/chai": ^4.2.0
    "@types/mocha": ">=9.1.0"
    "@types/node": ">=18.0.0"
    chai: ^4.2.0
    ethers: ^6.4.0
    hardhat: ^2.11.0
    hardhat-gas-reporter: ^1.0.8
    solidity-coverage: ^0.8.1
    ts-node: ">=8.0.0"
    typechain: ^8.3.0
    typescript: ">=4.5.0"
  checksum: 10/9d4936607439c5ab196f5b992f0754c95fbdf3b89cdd6175e0c8f0d948425a789d40ea7bebd1b45b925c96a4783b38f423452c68e21e916edd4d3675f49d0ccf
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-verify@npm:^2.0.10":
  version: 2.0.14
  resolution: "@nomicfoundation/hardhat-verify@npm:2.0.14"
  dependencies:
    "@ethersproject/abi": "npm:^5.1.2"
    "@ethersproject/address": "npm:^5.0.2"
    cbor: "npm:^8.1.0"
    debug: "npm:^4.1.1"
    lodash.clonedeep: "npm:^4.5.0"
    picocolors: "npm:^1.1.0"
    semver: "npm:^6.3.0"
    table: "npm:^6.8.0"
    undici: "npm:^5.14.0"
  peerDependencies:
    hardhat: ^2.24.1
  checksum: 10/c9c5e64990c00537a8248d72fa5ffaeeebc930d7c92b67864c4fbaaf775cb84da6f96cd3da5629d0b58bac305ee36f95ddcb56e7d4ae7deaf5af05e8d0181822
  languageName: node
  linkType: hard

"@nomicfoundation/ignition-core@npm:^0.15.11, @nomicfoundation/ignition-core@npm:^0.15.5":
  version: 0.15.11
  resolution: "@nomicfoundation/ignition-core@npm:0.15.11"
  dependencies:
    "@ethersproject/address": "npm:5.6.1"
    "@nomicfoundation/solidity-analyzer": "npm:^0.1.1"
    cbor: "npm:^9.0.0"
    debug: "npm:^4.3.2"
    ethers: "npm:^6.7.0"
    fs-extra: "npm:^10.0.0"
    immer: "npm:10.0.2"
    lodash: "npm:4.17.21"
    ndjson: "npm:2.0.0"
  checksum: 10/cb34c25325fb737d89cfd8bd7bb5a42709e30d636c920e0726a9a9431350db7b192ebcc7e2464f824ed6c5c43b5282a42f40c114aa7e41e9ef8a1448d0f0f967
  languageName: node
  linkType: hard

"@nomicfoundation/ignition-ui@npm:^0.15.11":
  version: 0.15.11
  resolution: "@nomicfoundation/ignition-ui@npm:0.15.11"
  checksum: 10/1d330ba7320153bf17ac10f5cd6e144b02ec0bd212b1859ffc75b2875028c5e63c743c82fee8abdbef5f811556175350e15991367711ace1868bb0fb72faedba
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-darwin-arm64@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-darwin-arm64@npm:0.1.2"
  checksum: 10/cf241ad2577741ccaaf0e5f723409c3d6e005d46f7a6eeceff17dcdbef1bc3bf603f859b23f3adb827a7e221f55fec781efd6153b52c05e3a85ba7d9fa5121c0
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-darwin-x64@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-darwin-x64@npm:0.1.2"
  checksum: 10/ff85471f3c0a6463896b1da1d433c174bd1b5f09976a9f678ab063baabe883c4f7fdaadc69d46050bf9c50b596b0f1f38d05e689e703386644a533350a2439f0
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-arm64-gnu@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-arm64-gnu@npm:0.1.2"
  checksum: 10/e0e0a8b7b5e81f002fd4e775bcb5035564c08b9c19cc2a99011d0ae691ec22278df343d054d76b9e2eff32b552defa3c63a6f9038996269e8f5b30ea9e07cb15
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-arm64-musl@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-arm64-musl@npm:0.1.2"
  checksum: 10/1e8371db027c379fc9c3470cfdfe0913b32371317052c082b3c1338a569f1171f243d5df999bc5416799c342dda62145dcbce21c8d56eb7033bb31c470af5418
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-x64-gnu@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-x64-gnu@npm:0.1.2"
  checksum: 10/63e9703975b784ad1ff64a44415ae4ab8fef64b776b7235d5e9bcf756cd636cf95e305b74d14072ffb541f5605151933476784f1afbb1e65b081b33860e9fcde
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-x64-musl@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-x64-musl@npm:0.1.2"
  checksum: 10/4c51615931ba8bd2ce144489f91fc0f1872def8f283253de50e6598945305f0b2655788ca03974e696046755c7db763c9457609908384ee91e649ee1899e4457
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-win32-x64-msvc@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-win32-x64-msvc@npm:0.1.2"
  checksum: 10/1a645168510776e469245e61e0139d6509632ba608806b78545b026725e423752987cd3f30b5924893260b9bf6fa106db1e5b69bf77e7e7133d1c3bef0fd1ffa
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer@npm:^0.1.0, @nomicfoundation/solidity-analyzer@npm:^0.1.1":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer@npm:0.1.2"
  dependencies:
    "@nomicfoundation/solidity-analyzer-darwin-arm64": "npm:0.1.2"
    "@nomicfoundation/solidity-analyzer-darwin-x64": "npm:0.1.2"
    "@nomicfoundation/solidity-analyzer-linux-arm64-gnu": "npm:0.1.2"
    "@nomicfoundation/solidity-analyzer-linux-arm64-musl": "npm:0.1.2"
    "@nomicfoundation/solidity-analyzer-linux-x64-gnu": "npm:0.1.2"
    "@nomicfoundation/solidity-analyzer-linux-x64-musl": "npm:0.1.2"
    "@nomicfoundation/solidity-analyzer-win32-x64-msvc": "npm:0.1.2"
  dependenciesMeta:
    "@nomicfoundation/solidity-analyzer-darwin-arm64":
      optional: true
    "@nomicfoundation/solidity-analyzer-darwin-x64":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-arm64-gnu":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-arm64-musl":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-x64-gnu":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-x64-musl":
      optional: true
    "@nomicfoundation/solidity-analyzer-win32-x64-msvc":
      optional: true
  checksum: 10/e86f4c82420e44b22bdf9419c944c0e64f199c71dd539e350dc80ecaf0a9852068a0701a11885f2e460abb731568e5f19949ac403383a5466d12625799237c4e
  languageName: node
  linkType: hard

"@nomiclabs/hardhat-ethers@npm:^2.2.3":
  version: 2.2.3
  resolution: "@nomiclabs/hardhat-ethers@npm:2.2.3"
  peerDependencies:
    ethers: ^5.0.0
    hardhat: ^2.0.0
  checksum: 10/bd239a00d3384b6dfefbf2444bacd7fdaccc9df40efac2255b4f8314f0414416e65296945bbe6debec65479a3f8a5f9d1e69aa66a39d1852e5ac1d690c3b458a
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable@npm:^5.3.0":
  version: 5.3.0
  resolution: "@openzeppelin/contracts-upgradeable@npm:5.3.0"
  peerDependencies:
    "@openzeppelin/contracts": 5.3.0
  checksum: 10/048eb2862aa23dba6f548ba5332e1ec9282c1dd6e73125156c1d918f5e7f48b8a0e0f405d0f7e4e74794d2cea3dd9e7901a146ac315a325730dcd38f99c2a3d9
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:3.4.2-solc-0.7":
  version: 3.4.2-solc-0.7
  resolution: "@openzeppelin/contracts@npm:3.4.2-solc-0.7"
  checksum: 10/a21aa0a623f020cb32cd3c6b7903d806ec458b2a750feb86f5e3bcf0b7ae124d844b9d1c029f9e0707d6263b561f35a694bafc1b0bff30c3e95541124f8fd41c
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:^5.3.0":
  version: 5.3.0
  resolution: "@openzeppelin/contracts@npm:5.3.0"
  checksum: 10/d1b379a56eb443b6ba2caee51f38bd3e2d5be2d3b2c935a083ed3311c106aa3903d7cadd05457999b24175bb80cc405dd6c70297c9825b15471b38051021fd88
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@pnpm/config.env-replace@npm:^1.1.0":
  version: 1.1.0
  resolution: "@pnpm/config.env-replace@npm:1.1.0"
  checksum: 10/fabe35cede1b72ad12877b8bed32f7c2fcd89e94408792c4d69009b886671db7988a2132bc18b7157489d2d0fd4266a06c9583be3d2e10c847bf06687420cb2a
  languageName: node
  linkType: hard

"@pnpm/network.ca-file@npm:^1.0.1":
  version: 1.0.2
  resolution: "@pnpm/network.ca-file@npm:1.0.2"
  dependencies:
    graceful-fs: "npm:4.2.10"
  checksum: 10/d8d0884646500576bd5390464d13db1bb9a62e32a1069293e5bddb2ad8354b354b7e2d2a35e12850025651e795e6a80ce9e601c66312504667b7e3ee7b52becc
  languageName: node
  linkType: hard

"@pnpm/npm-conf@npm:^2.1.0":
  version: 2.3.1
  resolution: "@pnpm/npm-conf@npm:2.3.1"
  dependencies:
    "@pnpm/config.env-replace": "npm:^1.1.0"
    "@pnpm/network.ca-file": "npm:^1.0.1"
    config-chain: "npm:^1.1.11"
  checksum: 10/44fbb0b166eee3e3631ef0e92b1bed6489aa6975e3e722c16577cc0181b81374f5ae90c6e4da183c8160f996e6b4863325525b00542f42d1b757b51ef62bc4e7
  languageName: node
  linkType: hard

"@primitivefi/hardhat-dodoc@npm:^0.2.3":
  version: 0.2.3
  resolution: "@primitivefi/hardhat-dodoc@npm:0.2.3"
  dependencies:
    squirrelly: "npm:^8.0.8"
  peerDependencies:
    hardhat: ^2.6.4
    squirrelly: ^8.0.8
  checksum: 10/0db4f31bf0fbcf3dbce5a58e5570e5bb915247d9fafecc0239bfeb50fda20d6fd76c16bae704b092714d81a42ee378f63b50a51c06423638d536325e5be2f1a9
  languageName: node
  linkType: hard

"@scure/base@npm:~1.1.0, @scure/base@npm:~1.1.2, @scure/base@npm:~1.1.6":
  version: 1.1.9
  resolution: "@scure/base@npm:1.1.9"
  checksum: 10/f0ab7f687bbcdee2a01377fe3cd808bf63977999672751295b6a92625d5322f4754a96d40f6bd579bc367aad48ecf8a4e6d0390e70296e6ded1076f52adb16bb
  languageName: node
  linkType: hard

"@scure/base@npm:~1.2.5":
  version: 1.2.6
  resolution: "@scure/base@npm:1.2.6"
  checksum: 10/c1a7bd5e0b0c8f94c36fbc220f4a67cc832b00e2d2065c7d8a404ed81ab1c94c5443def6d361a70fc382db3496e9487fb9941728f0584782b274c18a4bed4187
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.1.5":
  version: 1.1.5
  resolution: "@scure/bip32@npm:1.1.5"
  dependencies:
    "@noble/hashes": "npm:~1.2.0"
    "@noble/secp256k1": "npm:~1.7.0"
    "@scure/base": "npm:~1.1.0"
  checksum: 10/4c83e943a66e7b212d18f47b4650ed9b1dfeb69d8bdd8b491b12ba70ca8635cda67fb1ac920d642d66c8a3c2c03303b623c1faceafe7141a6f20a7cd7f66191e
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.3.2":
  version: 1.3.2
  resolution: "@scure/bip32@npm:1.3.2"
  dependencies:
    "@noble/curves": "npm:~1.2.0"
    "@noble/hashes": "npm:~1.3.2"
    "@scure/base": "npm:~1.1.2"
  checksum: 10/b90da28dfe75519496a85c97e77c9443734873910f32b8557762910a5c4e642290a462b0ed14fa42e0efed6acb9a7f6155ad5cb5d38d4ff87eb2de4760eb32a4
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.4.0":
  version: 1.4.0
  resolution: "@scure/bip32@npm:1.4.0"
  dependencies:
    "@noble/curves": "npm:~1.4.0"
    "@noble/hashes": "npm:~1.4.0"
    "@scure/base": "npm:~1.1.6"
  checksum: 10/6cd5062d902564d9e970597ec8b1adacb415b2eadfbb95aee1a1a0480a52eb0de4d294d3753aa8b48548064c9795ed108d348a31a8ce3fc88785377bb12c63b9
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.1.1":
  version: 1.1.1
  resolution: "@scure/bip39@npm:1.1.1"
  dependencies:
    "@noble/hashes": "npm:~1.2.0"
    "@scure/base": "npm:~1.1.0"
  checksum: 10/08908145e0890e481e3398191424961d9ebfb8913fed6e6cdfc63eb1281bd1895244d46c0e8762b0e30d8dc6f498ed296311382fecbf034253838e3a50f60ca1
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.2.1":
  version: 1.2.1
  resolution: "@scure/bip39@npm:1.2.1"
  dependencies:
    "@noble/hashes": "npm:~1.3.0"
    "@scure/base": "npm:~1.1.0"
  checksum: 10/2ea368bbed34d6b1701c20683bf465e147f231a9e37e639b8c82f585d6f978bb0f3855fca7ceff04954ae248b3e313f5d322d0210614fb7acb402739415aaf31
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.3.0":
  version: 1.3.0
  resolution: "@scure/bip39@npm:1.3.0"
  dependencies:
    "@noble/hashes": "npm:~1.4.0"
    "@scure/base": "npm:~1.1.6"
  checksum: 10/7d71fd58153de22fe8cd65b525f6958a80487bc9d0fbc32c71c328aeafe41fa259f989d2f1e0fa4fdfeaf83b8fcf9310d52ed9862987e46c2f2bfb9dd8cf9fc1
  languageName: node
  linkType: hard

"@sentry/core@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/core@npm:5.30.0"
  dependencies:
    "@sentry/hub": "npm:5.30.0"
    "@sentry/minimal": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: 10/fef7808017cc9581e94c51fbce3ffeb6bdb62b30d94920fae143d298aed194176ac7c026923d569a33606b93a3747b877e78215a1668ed8eb44e5941527e17e0
  languageName: node
  linkType: hard

"@sentry/hub@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/hub@npm:5.30.0"
  dependencies:
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: 10/b0e21a7acb1c363a3097c7578dd483b2e534bc62541977da7d3c643703767bbcfd65831b70b102fefa715e6b75004ca1dab680d117e1a7455e839042118c1051
  languageName: node
  linkType: hard

"@sentry/minimal@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/minimal@npm:5.30.0"
  dependencies:
    "@sentry/hub": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: 10/e74bf519f5e284decb81eea8fd7c75b02827bde36c8ccef5ad0b941043e62a6d6578d7f1ad9dba33e03d240593140990b1999215a35abb344e2b4f3e09b15c90
  languageName: node
  linkType: hard

"@sentry/node@npm:^5.18.1":
  version: 5.30.0
  resolution: "@sentry/node@npm:5.30.0"
  dependencies:
    "@sentry/core": "npm:5.30.0"
    "@sentry/hub": "npm:5.30.0"
    "@sentry/tracing": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    cookie: "npm:^0.4.1"
    https-proxy-agent: "npm:^5.0.0"
    lru_map: "npm:^0.3.3"
    tslib: "npm:^1.9.3"
  checksum: 10/9fa37b3ce646954f68e4b7506d17c67f5779c69cd432801aaf6796f9ecea9632eb8729b77b71a31dcd5a9f57fb7759fd213222955a667d8ad557df6e997a00c4
  languageName: node
  linkType: hard

"@sentry/tracing@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/tracing@npm:5.30.0"
  dependencies:
    "@sentry/hub": "npm:5.30.0"
    "@sentry/minimal": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: 10/7e74a29823b445adb104c323324348882987554d049e83e5d3439149d2677024350974161c28b1a55a2750509b030525f81056a48427be06183f3744220ba4b0
  languageName: node
  linkType: hard

"@sentry/types@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/types@npm:5.30.0"
  checksum: 10/3ca60689871b298dbab16c1bb6fb4637f72d3c21820017bac9df1765fd560004862cc9e75fb438e5714048b3a9bc641c396cdbb3c3573ac62481d2ea83f1da6d
  languageName: node
  linkType: hard

"@sentry/utils@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/utils@npm:5.30.0"
  dependencies:
    "@sentry/types": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: 10/4aa8acf7d0d9688c927a620cbb9fd37d6d2738f701863af772be329baca2cede909dcae6c7b4b449474787245c09212909ee740b4cae143d21ddb1fed910cc3a
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^5.2.0":
  version: 5.6.0
  resolution: "@sindresorhus/is@npm:5.6.0"
  checksum: 10/b077c325acec98e30f7d86df158aaba2e7af2acb9bb6a00fda4b91578539fbff4ecebe9b934e24fec0e6950de3089d89d79ec02d9062476b20ce185be0e01bd6
  languageName: node
  linkType: hard

"@solidity-parser/parser@npm:^0.18.0":
  version: 0.18.0
  resolution: "@solidity-parser/parser@npm:0.18.0"
  checksum: 10/3b600b584f49bd84d6d27aeeb453c49c279df49324e104bda00d12cd3b26f18cb6230ff63db6a0ba1f383868620d318b15b7417a92aa8c580099449adaa13d76
  languageName: node
  linkType: hard

"@solidity-parser/parser@npm:^0.20.0":
  version: 0.20.1
  resolution: "@solidity-parser/parser@npm:0.20.1"
  checksum: 10/6497d74c67386ad3c91c906fbea4cf46df1b0eb3f597c7c881c5bbf33a5c689b36d22211fedc36e023e59facf8a6d7cff315dc117d3215d38cc5be95ecc106db
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^5.0.1":
  version: 5.0.1
  resolution: "@szmarczak/http-timer@npm:5.0.1"
  dependencies:
    defer-to-connect: "npm:^2.0.1"
  checksum: 10/fc9cb993e808806692e4a3337c90ece0ec00c89f4b67e3652a356b89730da98bc824273a6d67ca84d5f33cd85f317dcd5ce39d8cc0a2f060145a608a7cb8ce92
  languageName: node
  linkType: hard

"@trivago/prettier-plugin-sort-imports@npm:^4.3.0":
  version: 4.3.0
  resolution: "@trivago/prettier-plugin-sort-imports@npm:4.3.0"
  dependencies:
    "@babel/generator": "npm:7.17.7"
    "@babel/parser": "npm:^7.20.5"
    "@babel/traverse": "npm:7.23.2"
    "@babel/types": "npm:7.17.0"
    javascript-natural-sort: "npm:0.7.1"
    lodash: "npm:^4.17.21"
  peerDependencies:
    "@vue/compiler-sfc": 3.x
    prettier: 2.x - 3.x
  peerDependenciesMeta:
    "@vue/compiler-sfc":
      optional: true
  checksum: 10/eb25cbeeaf85d3acd54019d1f3563447337a2faee7a35558adb69dff44ce3b93714a5b64ba4d0374f3df3191c32c993d441493fdc43a2c97c9b8a0e3d58702cf
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 10/51fe47d55fe1b80ec35e6e5ed30a13665fd3a531945350aa74a14a1e82875fb60b350c2f2a5e72a64831b1b6bc02acb6760c30b3738b54954ec2dea82db7a267
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 10/5ce29a41b13e7897a58b8e2df11269c5395999e588b9a467386f99d1d26f6c77d1af2719e407621412520ea30517d718d5192a32403b8dfcc163bf33e40a338a
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 10/19275fe80c4c8d0ad0abed6a96dbf00642e88b220b090418609c4376e1cef81bf16237bf170ad1b341452feddb8115d8dd2e5acdfdea1b27422071163dc9ba9d
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 10/202319785901f942a6e1e476b872d421baec20cf09f4b266a1854060efbf78cde16a4d256e8bc949d31e6cd9a90f1e8ef8fb06af96a65e98338a2b6b0de0a0ff
  languageName: node
  linkType: hard

"@typechain/ethers-v6@npm:^0.5.1":
  version: 0.5.1
  resolution: "@typechain/ethers-v6@npm:0.5.1"
  dependencies:
    lodash: "npm:^4.17.15"
    ts-essentials: "npm:^7.0.1"
  peerDependencies:
    ethers: 6.x
    typechain: ^8.3.2
    typescript: ">=4.7.0"
  checksum: 10/51dd8be3548fe3c061d2a5372beb9214e767e2b69f10c12424b699bba7ff409a13c4bdff2e513ef49046b51153db56489752205541be8fb1775f3b9ad884b85b
  languageName: node
  linkType: hard

"@typechain/hardhat@npm:^8.0.0":
  version: 8.0.3
  resolution: "@typechain/hardhat@npm:8.0.3"
  dependencies:
    fs-extra: "npm:^9.1.0"
  peerDependencies:
    "@typechain/ethers-v6": ^0.4.3
    ethers: ^6.1.0
    hardhat: ^2.9.9
    typechain: ^8.3.1
  checksum: 10/71d6acf29b335e20ab38c2c634be0ecf55965f3a0a9f5dc5fd0f9da8c4a511a2875802817c66cf829b12a1bab6d7f26b925ca69bf4b42ea343a5b6993a02b237
  languageName: node
  linkType: hard

"@typechain/hardhat@npm:^9.1.0":
  version: 9.1.0
  resolution: "@typechain/hardhat@npm:9.1.0"
  dependencies:
    fs-extra: "npm:^9.1.0"
  peerDependencies:
    "@typechain/ethers-v6": ^0.5.1
    ethers: ^6.1.0
    hardhat: ^2.9.9
    typechain: ^8.3.2
  checksum: 10/1f59d8243af020905f3cdb96125cf9fcad33bd16f919d5e07762c63e0220bccb658abf59b0ad74be784a3387c0a6b0262612fa1adeb9f6c99061a6abaa6afc53
  languageName: node
  linkType: hard

"@types/bn.js@npm:^4.11.3":
  version: 4.11.6
  resolution: "@types/bn.js@npm:4.11.6"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/9ff3e7a1539a953c381c0d30ea2049162e3cab894cda91ee10f3a84d603f9afa2b2bc2a38fe9b427de94b6e2b7b77aefd217c1c7b07a10ae8d7499f9d6697a41
  languageName: node
  linkType: hard

"@types/bn.js@npm:^5.1.0":
  version: 5.2.0
  resolution: "@types/bn.js@npm:5.2.0"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/06c93841f74e4a5e5b81b74427d56303b223c9af36389b4cd3c562bda93f43c425c7e241aee1b0b881dde57238dc2e07f21d30d412b206a7dae4435af4c054e8
  languageName: node
  linkType: hard

"@types/chai-as-promised@npm:^7.1.3":
  version: 7.1.8
  resolution: "@types/chai-as-promised@npm:7.1.8"
  dependencies:
    "@types/chai": "npm:*"
  checksum: 10/88e2d42f14d1de19ba1c7b5c35f263fef37d3ad241c71f5eb59b10763706f3902f4131b93854c9c6ed520081c7e36be555849f202418357f905bea71178b7d02
  languageName: node
  linkType: hard

"@types/chai@npm:*":
  version: 5.2.2
  resolution: "@types/chai@npm:5.2.2"
  dependencies:
    "@types/deep-eql": "npm:*"
  checksum: 10/de425e7b02cc1233a93923866e019dffbafa892774813940b780ebb1ac9f8a8c57b7438c78686bf4e5db05cd3fc8a970fedf6b83638543995ecca88ef2060668
  languageName: node
  linkType: hard

"@types/chai@npm:^4.3.19":
  version: 4.3.20
  resolution: "@types/chai@npm:4.3.20"
  checksum: 10/94fd87036fb63f62c79caf58ccaec88e23cc109e4d41607d83adc609acd6b24eabc345feb7850095a53f76f99c470888251da9bd1b90849c8b2b5a813296bb19
  languageName: node
  linkType: hard

"@types/deep-eql@npm:*":
  version: 4.0.2
  resolution: "@types/deep-eql@npm:4.0.2"
  checksum: 10/249a27b0bb22f6aa28461db56afa21ec044fa0e303221a62dff81831b20c8530502175f1a49060f7099e7be06181078548ac47c668de79ff9880241968d43d0c
  languageName: node
  linkType: hard

"@types/fs-extra@npm:^11.0.4":
  version: 11.0.4
  resolution: "@types/fs-extra@npm:11.0.4"
  dependencies:
    "@types/jsonfile": "npm:*"
    "@types/node": "npm:*"
  checksum: 10/acc4c1eb0cde7b1f23f3fe6eb080a14832d8fa9dc1761aa444c5e2f0f6b6fa657ed46ebae32fb580a6700fc921b6165ce8ac3e3ba030c3dd15f10ad4dd4cae98
  languageName: node
  linkType: hard

"@types/glob@npm:^7.1.1":
  version: 7.2.0
  resolution: "@types/glob@npm:7.2.0"
  dependencies:
    "@types/minimatch": "npm:*"
    "@types/node": "npm:*"
  checksum: 10/6ae717fedfdfdad25f3d5a568323926c64f52ef35897bcac8aca8e19bc50c0bd84630bbd063e5d52078b2137d8e7d3c26eabebd1a2f03ff350fff8a91e79fc19
  languageName: node
  linkType: hard

"@types/http-cache-semantics@npm:^4.0.2":
  version: 4.0.4
  resolution: "@types/http-cache-semantics@npm:4.0.4"
  checksum: 10/a59566cff646025a5de396d6b3f44a39ab6a74f2ed8150692e0f31cc52f3661a68b04afe3166ebe0d566bd3259cb18522f46e949576d5204781cd6452b7fe0c5
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.12":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/jsonfile@npm:*":
  version: 6.1.4
  resolution: "@types/jsonfile@npm:6.1.4"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/309fda20eb5f1cf68f2df28931afdf189c5e7e6bec64ac783ce737bb98908d57f6f58757ad5da9be37b815645a6f914e2d4f3ac66c574b8fe1ba6616284d0e97
  languageName: node
  linkType: hard

"@types/lru-cache@npm:^5.1.0":
  version: 5.1.1
  resolution: "@types/lru-cache@npm:5.1.1"
  checksum: 10/0afadefc983306684a8ef95b6337a0d9e3f687e7e89e1f1f3f2e1ce3fbab5b018bb84cf277d781f871175a2c8f0176762b69e58b6f4296ee1b816cea94d5ef06
  languageName: node
  linkType: hard

"@types/minimatch@npm:*":
  version: 5.1.2
  resolution: "@types/minimatch@npm:5.1.2"
  checksum: 10/94db5060d20df2b80d77b74dd384df3115f01889b5b6c40fa2dfa27cfc03a68fb0ff7c1f2a0366070263eb2e9d6bfd8c87111d4bc3ae93c3f291297c1bf56c85
  languageName: node
  linkType: hard

"@types/mkdirp@npm:^0.5.2":
  version: 0.5.2
  resolution: "@types/mkdirp@npm:0.5.2"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/c3c2c244ec6961bf7a565d44b21dcb94368e01804c3a6783a2b8b11231fe496eca8d5b6f06f3b385b9ad2c0e0fc8ea10b3ddf66f4052214334df53eacad58e6e
  languageName: node
  linkType: hard

"@types/mocha@npm:^10.0.8":
  version: 10.0.10
  resolution: "@types/mocha@npm:10.0.10"
  checksum: 10/4e3b61ed5112add86891a5dd3ebdd087714f5e1784a63d47a96424c0825058fd07074e85e43573462f751636c92808fc18a5f3862fe45e649ea98fdc5a3ee2ea
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:^22.5.5":
  version: 22.15.30
  resolution: "@types/node@npm:22.15.30"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10/16d03f30df7851eea4f31d9a2470a53b24dc7d3909ed1782e2538e3aafe398451da17b83d6f730dfdcd0a843ccdbe841176ecfbde6bdb51963e8d11f58a10ade
  languageName: node
  linkType: hard

"@types/node@npm:18.15.13":
  version: 18.15.13
  resolution: "@types/node@npm:18.15.13"
  checksum: 10/b9bbe923573797ef7c5fd2641a6793489e25d9369c32aeadcaa5c7c175c85b42eb12d6fe173f6781ab6f42eaa1ebd9576a419eeaa2a1ec810094adb8adaa9a54
  languageName: node
  linkType: hard

"@types/node@npm:22.7.5":
  version: 22.7.5
  resolution: "@types/node@npm:22.7.5"
  dependencies:
    undici-types: "npm:~6.19.2"
  checksum: 10/e8ba102f8c1aa7623787d625389be68d64e54fcbb76d41f6c2c64e8cf4c9f4a2370e7ef5e5f1732f3c57529d3d26afdcb2edc0101c5e413a79081449825c57ac
  languageName: node
  linkType: hard

"@types/pbkdf2@npm:^3.0.0":
  version: 3.1.2
  resolution: "@types/pbkdf2@npm:3.1.2"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/bebe1e596cbbe5f7d2726a58859e61986c5a42459048e29cb7f2d4d764be6bbb0844572fd5d70ca8955a8a17e8b4ed80984fc4903e165d9efb8807a3fbb051aa
  languageName: node
  linkType: hard

"@types/prettier@npm:^2.1.1":
  version: 2.7.3
  resolution: "@types/prettier@npm:2.7.3"
  checksum: 10/cda84c19acc3bf327545b1ce71114a7d08efbd67b5030b9e8277b347fa57b05178045f70debe1d363ff7efdae62f237260713aafc2d7217e06fc99b048a88497
  languageName: node
  linkType: hard

"@types/qs@npm:^6.9.7":
  version: 6.14.0
  resolution: "@types/qs@npm:6.14.0"
  checksum: 10/1909205514d22b3cbc7c2314e2bd8056d5f05dfb21cf4377f0730ee5e338ea19957c41735d5e4806c746176563f50005bbab602d8358432e25d900bdf4970826
  languageName: node
  linkType: hard

"@types/resolve@npm:^0.0.8":
  version: 0.0.8
  resolution: "@types/resolve@npm:0.0.8"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/cfbfea900778e43fa5ce5dbca0d6e82509c0edd3c950e6349080242ba176ef34a4cd380e9391cd31994df04161f563798e068a2a8a9e577d9270950305b3bf9d
  languageName: node
  linkType: hard

"@types/secp256k1@npm:^4.0.1":
  version: 4.0.6
  resolution: "@types/secp256k1@npm:4.0.6"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/211f823be990b55612e604d620acf0dc3bc942d3836bdd8da604269effabc86d98161e5947487b4e4e128f9180fc1682daae2f89ea7a4d9648fdfe52fba365fc
  languageName: node
  linkType: hard

"@types/semver@npm:^7.5.0":
  version: 7.7.0
  resolution: "@types/semver@npm:7.7.0"
  checksum: 10/ee4514c6c852b1c38f951239db02f9edeea39f5310fad9396a00b51efa2a2d96b3dfca1ae84c88181ea5b7157c57d32d7ef94edacee36fbf975546396b85ba5b
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/eslint-plugin@npm:6.21.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.5.1"
    "@typescript-eslint/scope-manager": "npm:6.21.0"
    "@typescript-eslint/type-utils": "npm:6.21.0"
    "@typescript-eslint/utils": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
    debug: "npm:^4.3.4"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.4"
    natural-compare: "npm:^1.4.0"
    semver: "npm:^7.5.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependencies:
    "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/a57de0f630789330204cc1531f86cfc68b391cafb1ba67c8992133f1baa2a09d629df66e71260b040de4c9a3ff1252952037093c4128b0d56c4dbb37720b4c1d
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/parser@npm:6.21.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:6.21.0"
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/typescript-estree": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/4d51cdbc170e72275efc5ef5fce48a81ec431e4edde8374f4d0213d8d370a06823e1a61ae31d502a5f1b0d1f48fc4d29a1b1b5c2dcf809d66d3872ccf6e46ac7
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/scope-manager@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
  checksum: 10/fe91ac52ca8e09356a71dc1a2f2c326480f3cccfec6b2b6d9154c1a90651ab8ea270b07c67df5678956c3bbf0bbe7113ab68f68f21b20912ea528b1214197395
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/type-utils@npm:6.21.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:6.21.0"
    "@typescript-eslint/utils": "npm:6.21.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/d03fb3ee1caa71f3ce053505f1866268d7ed79ffb7fed18623f4a1253f5b8f2ffc92636d6fd08fcbaf5bd265a6de77bf192c53105131e4724643dfc910d705fc
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/types@npm:6.21.0"
  checksum: 10/e26da86d6f36ca5b6ef6322619f8ec55aabcd7d43c840c977ae13ae2c964c3091fc92eb33730d8be08927c9de38466c5323e78bfb270a9ff1d3611fe821046c5
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/typescript-estree@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/visitor-keys": "npm:6.21.0"
    debug: "npm:^4.3.4"
    globby: "npm:^11.1.0"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:9.0.3"
    semver: "npm:^7.5.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/b32fa35fca2a229e0f5f06793e5359ff9269f63e9705e858df95d55ca2cd7fdb5b3e75b284095a992c48c5fc46a1431a1a4b6747ede2dd08929dc1cbacc589b8
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/utils@npm:6.21.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@types/json-schema": "npm:^7.0.12"
    "@types/semver": "npm:^7.5.0"
    "@typescript-eslint/scope-manager": "npm:6.21.0"
    "@typescript-eslint/types": "npm:6.21.0"
    "@typescript-eslint/typescript-estree": "npm:6.21.0"
    semver: "npm:^7.5.4"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  checksum: 10/b404a2c55a425a79d054346ae123087d30c7ecf7ed7abcf680c47bf70c1de4fabadc63434f3f460b2fa63df76bc9e4a0b9fa2383bb8a9fcd62733fb5c4e4f3e3
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/visitor-keys@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": "npm:6.21.0"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10/30422cdc1e2ffad203df40351a031254b272f9c6f2b7e02e9bfa39e3fc2c7b1c6130333b0057412968deda17a3a68a578a78929a8139c6acef44d9d841dc72e1
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10/80d6910946f2b1552a2406650051c91bbd1f24a6bf854354203d84fe2714b3e8ce4618f49cc3410494173a1c1e8e9777372fe68dce74bd45faf0a7a1a6ccf448
  languageName: node
  linkType: hard

"@uniswap/lib@npm:^4.0.1-alpha":
  version: 4.0.1-alpha
  resolution: "@uniswap/lib@npm:4.0.1-alpha"
  checksum: 10/5a80159c1b6b3dc7eace55d4d348a170a8a32162e0f01bb965c5cf540f243b274c57e76e61aba76f62abac7fba83239b51b3f230c86b86ecd9e09f1b1a4ec868
  languageName: node
  linkType: hard

"@uniswap/v2-core@npm:^1.0.1":
  version: 1.0.1
  resolution: "@uniswap/v2-core@npm:1.0.1"
  checksum: 10/a98cbafba613c088be4b439138e0a68f1d79c1f26c3bbe6ba20088832be4d8fa4a3f22569982e5d05f01a1d51586b58d83d64206f8f029c5bfb41282fcb8d62d
  languageName: node
  linkType: hard

"@uniswap/v3-core@npm:^1.0.0, @uniswap/v3-core@npm:^1.0.1":
  version: 1.0.1
  resolution: "@uniswap/v3-core@npm:1.0.1"
  checksum: 10/704e865c9440f7eaa118673224a8ffde258aaa43e63605c77711cf70d562cca8a53648c528107e471a0f8183a7f7a01ed50fb01d658cf38cea0a055365f1aa21
  languageName: node
  linkType: hard

"@uniswap/v3-periphery@npm:^1.4.4":
  version: 1.4.4
  resolution: "@uniswap/v3-periphery@npm:1.4.4"
  dependencies:
    "@openzeppelin/contracts": "npm:3.4.2-solc-0.7"
    "@uniswap/lib": "npm:^4.0.1-alpha"
    "@uniswap/v2-core": "npm:^1.0.1"
    "@uniswap/v3-core": "npm:^1.0.0"
    base64-sol: "npm:1.0.1"
  checksum: 10/4045aa06c336c152786f750a51f504423bad4a9f7ff589a2fa080027d522ca3e5545f6d207f8be00f67b149f9fb10e33c8710a7c881045359347288ff3757b3e
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: 10/2d882941183c66aa665118bafdab82b7a177e9add5eb2776c33e960a4f3c89cff88a1b38aba13a456de01d0dd9d66a8bea7c903268b21ea91dd1097e1e2e8243
  languageName: node
  linkType: hard

"abbrev@npm:1.0.x":
  version: 1.0.9
  resolution: "abbrev@npm:1.0.9"
  checksum: 10/5ca5ac34c39d3ae15a90ce5570309e25c0e72d3947bdf95c10a1957f83609bf42831cb4b746d3d96b2a85a52b290832797b8a63b27449f47925b25ca86b78591
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10/ebd2c149dda6f543b66ce3779ea612151bb3aa9d0824f169773ee9876f1ca5a4e0adbcccc7eed048c04da7998e1825e2aa76fcca92d9e67dea50ac2b0a58dc2e
  languageName: node
  linkType: hard

"abitype@npm:1.0.0":
  version: 1.0.0
  resolution: "abitype@npm:1.0.0"
  peerDependencies:
    typescript: ">=5.0.4"
    zod: ^3 >=3.22.0
  peerDependenciesMeta:
    typescript:
      optional: true
    zod:
      optional: true
  checksum: 10/38c8d965c75c031854385f1c14da0410e271f1a8255332869a77a1ee836c4607420522c1f0077716c7ad7c4091f53c1b2681ed1d30b5161d1424fdb5a480f104
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10/d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10/871386764e1451c637bb8ab9f76f4995d408057e9909be6fb5ad68537ae3375d85e6a6f170b98989f44ab3ff6c74ad120bc2779a3d577606e7a0cd2b4efcaf77
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.4.1, acorn@npm:^8.9.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10/77f2de5051a631cf1729c090e5759148459cdb76b5f5c70f890503d629cf5052357b0ce783c0f976dd8a93c5150f59f6d18df1def3f502396a20f81282482fa4
  languageName: node
  linkType: hard

"adm-zip@npm:^0.4.16":
  version: 0.4.16
  resolution: "adm-zip@npm:0.4.16"
  checksum: 10/897003d21a445bfce251d5a328706035dc03af53cd4c66bb0a4558496939f89767ae5e7c67d10a5a9ad0146081a339bed3361405d6cca648a4378198573e9cad
  languageName: node
  linkType: hard

"aes-js@npm:3.0.0":
  version: 3.0.0
  resolution: "aes-js@npm:3.0.0"
  checksum: 10/1b3772e5ba74abdccb6c6b99bf7f50b49057b38c0db1612b46c7024414f16e65ba7f1643b2d6e38490b1870bdf3ba1b87b35e2c831fd3fdaeff015f08aad19d1
  languageName: node
  linkType: hard

"aes-js@npm:4.0.0-beta.5":
  version: 4.0.0-beta.5
  resolution: "aes-js@npm:4.0.0-beta.5"
  checksum: 10/8f745da2e8fb38e91297a8ec13c2febe3219f8383303cd4ed4660ca67190242ccfd5fdc2f0d1642fd1ea934818fb871cd4cc28d3f28e812e3dc6c3d0f1f97c24
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10/21fb903e0917e5cb16591b4d0ef6a028a54b83ac30cd1fca58dece3d4e0990512a8723f9f83130d88a41e2af8b1f7be1386fda3ea2d181bb1a62155e75e95e23
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 10/1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4, ajv@npm:^6.12.6":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10/48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"ajv@npm:^8.0.1":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10/ee3c62162c953e91986c838f004132b6a253d700f1e51253b99791e2dbfdb39161bc950ebdc2f156f8568035bb5ed8be7bd78289cd9ecbf3381fe8f5b82e3f33
  languageName: node
  linkType: hard

"amdefine@npm:>=0.0.4":
  version: 1.0.1
  resolution: "amdefine@npm:1.0.1"
  checksum: 10/517df65fc33d3ff14fe5c0057e041b03d603a2254dea7968b05dfbfa3041eb8430ea6729e305bc428c03fad03f162de91a4b256692d27d7b81d3ee691312cffe
  languageName: node
  linkType: hard

"ansi-align@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-align@npm:3.0.1"
  dependencies:
    string-width: "npm:^4.1.0"
  checksum: 10/4c7e8b6a10eaf18874ecee964b5db62ac86d0b9266ad4987b3a1efcb5d11a9e12c881ee40d14951833135a8966f10a3efe43f9c78286a6e632f53d85ad28b9c0
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.1, ansi-colors@npm:^4.1.3":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: 10/43d6e2fc7b1c6e4dc373de708ee76311ec2e0433e7e8bd3194e7ff123ea6a747428fc61afdcf5969da5be3a5f0fd054602bec56fc0ebe249ce2fcde6e649e3c2
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10/8661034456193ffeda0c15c8c564a9636b0c04094b7f78bd01517929c17c504090a60f7a75f949f5af91289c264d3e1001d91492c1bd58efc8e100500ce04de2
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: 10/d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"antlr4@npm:^4.13.1-patch-1":
  version: 4.13.2
  resolution: "antlr4@npm:4.13.2"
  checksum: 10/23ab4742ec937adaaf20d13228c8cca58638e1aafeb28919bdeb4860776a403d0c7eb85a3f07fadc27fc03f773eed6bcc82bd8369b9d0e258e6502ba514cf87e
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10/3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 10/969b491082f20cad166649fa4d2073ea9e974a4e5ac36247ca23d2e5a8b3cb12d60e9ff70a8acfe26d76566c71fd351ee5e6a9a6595157eb36f92b1fd64e1599
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10/c6a621343a553ff3779390bb5ee9c2263d6643ebcd7843227bdde6cc7adbed796eb5540ca98db19e3fd7b4714e1faa51551f8849b268bb62df27ddb15cbcd91e
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10/18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"array-back@npm:^3.0.1, array-back@npm:^3.1.0":
  version: 3.1.0
  resolution: "array-back@npm:3.1.0"
  checksum: 10/7205004fcd0f9edd926db921af901b083094608d5b265738d0290092f9822f73accb468e677db74c7c94ef432d39e5ed75a7b1786701e182efb25bbba9734209
  languageName: node
  linkType: hard

"array-back@npm:^4.0.1, array-back@npm:^4.0.2":
  version: 4.0.2
  resolution: "array-back@npm:4.0.2"
  checksum: 10/f30603270771eeb54e5aad5f54604c62b3577a18b6db212a7272b2b6c32049121b49431f656654790ed1469411e45f387e7627c0de8fd0515995cc40df9b9294
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10/5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"assertion-error@npm:^1.1.0":
  version: 1.1.0
  resolution: "assertion-error@npm:1.1.0"
  checksum: 10/fd9429d3a3d4fd61782eb3962ae76b6d08aa7383123fca0596020013b3ebd6647891a85b05ce821c47d1471ed1271f00b0545cf6a4326cf2fc91efcc3b0fbecf
  languageName: node
  linkType: hard

"ast-parents@npm:^0.0.1":
  version: 0.0.1
  resolution: "ast-parents@npm:0.0.1"
  checksum: 10/08eaa3b755529aad0708aad54ff09087b171334dcffa0774d3401e1dc54db1242bd5e76e599152705e813f768b9245a3c20777ed033c706d2093e358a91b12c2
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 10/876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"async@npm:1.x":
  version: 1.5.2
  resolution: "async@npm:1.5.2"
  checksum: 10/8afcdcee05168250926a3e7bd4dfaa74b681a74f634bae2af424fb716042461cbd20a375d9bc2534daa50a2d45286c9b174952fb239cee4ab8d6351a40c65327
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10/3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 10/463e2f8e43384f1afb54bc68485c436d7622acec08b6fad269b421cb1d29cebb5af751426793d0961ed243146fe4dc983402f6d5a51b720b277818dbf6f2e49e
  languageName: node
  linkType: hard

"axios@npm:^0.21.1":
  version: 0.21.4
  resolution: "axios@npm:0.21.4"
  dependencies:
    follow-redirects: "npm:^1.14.0"
  checksum: 10/da644592cb6f8f9f8c64fdabd7e1396d6769d7a4c1ea5f8ae8beb5c2eb90a823e3a574352b0b934ac62edc762c0f52647753dc54f7d07279127a7e5c4cd20272
  languageName: node
  linkType: hard

"axios@npm:^1.6.7":
  version: 1.9.0
  resolution: "axios@npm:1.9.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10/a2f90bba56820883879f32a237e2b9ff25c250365dcafd41cec41b3406a3df334a148f90010182dfdadb4b41dc59f6f0b3e8898ff41b666d1157b5f3f4523497
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base-x@npm:^3.0.2":
  version: 3.0.11
  resolution: "base-x@npm:3.0.11"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10/c2e3c443fd07cb9b9d3e179a9e9c581daa31881005841fe8d6a834e534505890fedf03465ccf14512da60e3f7be00fe66167806b159ba076d2c03952ae7460c4
  languageName: node
  linkType: hard

"base64-sol@npm:1.0.1":
  version: 1.0.1
  resolution: "base64-sol@npm:1.0.1"
  checksum: 10/be0f9e8cf3c744256913223fbae8187773f530cc096e98a77f49ef0bd6cedeb294d15a784e439419f7cb99f07bf85b08999169feafafa1a9e29c3affc0bc6d0a
  languageName: node
  linkType: hard

"bech32@npm:1.1.4":
  version: 1.1.4
  resolution: "bech32@npm:1.1.4"
  checksum: 10/63ff37c0ce43be914c685ce89700bba1589c319af0dac1ea04f51b33d0e5ecfd40d14c24f527350b94f0a4e236385373bb9122ec276410f354ddcdbf29ca13f4
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.3.0":
  version: 9.3.0
  resolution: "bignumber.js@npm:9.3.0"
  checksum: 10/60b79efcf7b56b925fca8eebd10d1f4b70aa2bf6eade7f5af0266f0092226dd2abcd9a3ee315ecb39459750d5a630ce3980b707e5d7bea32c97ffd378e8cc159
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10/bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"blakejs@npm:^1.1.0":
  version: 1.2.1
  resolution: "blakejs@npm:1.2.1"
  checksum: 10/0638b1bd058b21892633929c43005aa6a4cc4b2ac5b338a146c3c076622f1b360795bd7a4d1f077c9b01863ed2df0c1504a81c5b520d164179120434847e6cd7
  languageName: node
  linkType: hard

"bn.js@npm:4.11.6":
  version: 4.11.6
  resolution: "bn.js@npm:4.11.6"
  checksum: 10/22741b015c9fff60fce32fc9988331b298eb9b6db5bfb801babb23b846eaaf894e440e0d067b2b3ae4e46aab754e90972f8f333b31bf94a686bbcb054bfa7b14
  languageName: node
  linkType: hard

"bn.js@npm:^4.11.0, bn.js@npm:^4.11.8, bn.js@npm:^4.11.9":
  version: 4.12.2
  resolution: "bn.js@npm:4.12.2"
  checksum: 10/5803983405c087443e0e6c9bb5d0bc863d9f987d77e710f81b14c55616494f5a274e1650ee892531acb3529d52c0e0ea48aa12d2873dd80a75dde9d73a2ec518
  languageName: node
  linkType: hard

"bn.js@npm:^5.1.2, bn.js@npm:^5.2.0, bn.js@npm:^5.2.1":
  version: 5.2.2
  resolution: "bn.js@npm:5.2.2"
  checksum: 10/51ebb2df83b33e5d8581165206e260d5e9c873752954616e5bf3758952b84d7399a9c6d00852815a0aeefb1150a7f34451b62d4287342d457fa432eee869e83e
  languageName: node
  linkType: hard

"boxen@npm:^5.1.2":
  version: 5.1.2
  resolution: "boxen@npm:5.1.2"
  dependencies:
    ansi-align: "npm:^3.0.0"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.1.0"
    cli-boxes: "npm:^2.2.1"
    string-width: "npm:^4.2.2"
    type-fest: "npm:^0.20.2"
    widest-line: "npm:^3.1.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10/bc3d3d88d77dc8cabb0811844acdbd4805e8ca8011222345330817737042bf6f86d93eb74a3f7e0cab634e64ef69db03cf52b480761ed90a965de0c8ff1bea8c
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 10/8a05c9f3c4b46572dec6ef71012b1946db6cae8c7bb60ccd4b7dd5a84655db49fe043ecc6272e7ef1f69dc53d6730b9e2a3a03a8310509a3d797a618cbee52be
  languageName: node
  linkType: hard

"brotli-wasm@npm:^2.0.1":
  version: 2.0.1
  resolution: "brotli-wasm@npm:2.0.1"
  checksum: 10/39789548c9b81f735a7ab87a6caa150babbd85148a8528610d050449c9802afb78b9197de25a1f613c8e1f945d4d0200cb473327a89f4b65cb9ee2b715d64292
  languageName: node
  linkType: hard

"browser-stdout@npm:^1.3.1":
  version: 1.3.1
  resolution: "browser-stdout@npm:1.3.1"
  checksum: 10/ac70a84e346bb7afc5045ec6f22f6a681b15a4057447d4cc1c48a25c6dedb302a49a46dd4ddfb5cdd9c96e0c905a8539be1b98ae7bc440512152967009ec7015
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.2.0":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: "npm:^1.0.3"
    cipher-base: "npm:^1.0.0"
    create-hash: "npm:^1.1.0"
    evp_bytestokey: "npm:^1.0.3"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 10/2813058f74e083a00450b11ea9d5d1f072de7bf0133f5d122d4ff7b849bece56d52b9c51ad0db0fad21c0bc4e8272fd5196114bbe7b94a9b7feb0f9fbb33a3bf
  languageName: node
  linkType: hard

"bs58@npm:^4.0.0":
  version: 4.0.1
  resolution: "bs58@npm:4.0.1"
  dependencies:
    base-x: "npm:^3.0.2"
  checksum: 10/b3c5365bb9e0c561e1a82f1a2d809a1a692059fae016be233a6127ad2f50a6b986467c3a50669ce4c18929dcccb297c5909314dd347a25a68c21b68eb3e95ac2
  languageName: node
  linkType: hard

"bs58check@npm:^2.1.2":
  version: 2.1.2
  resolution: "bs58check@npm:2.1.2"
  dependencies:
    bs58: "npm:^4.0.0"
    create-hash: "npm:^1.1.0"
    safe-buffer: "npm:^5.1.2"
  checksum: 10/43bdf08a5dd04581b78f040bc4169480e17008da482ffe2a6507327bbc4fc5c28de0501f7faf22901cfe57fbca79cbb202ca529003fedb4cb8dccd265b38e54d
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10/0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: 10/4a63d48b5117c7eda896d81cd3582d9707329b07c97a14b0ece2edc6e64220ea7ea17c94b295e8c2cb7b9f8291e2b079f9096be8ac14be238420a43e06ec66e2
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10/a10abf2ba70c784471d6b4f58778c0beeb2b5d405148e66affa91f23a9f13d07603d0a0354667310ae1d6dc141474ffd44e2a074be0f6e2254edb8fc21445388
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^7.0.0":
  version: 7.0.0
  resolution: "cacheable-lookup@npm:7.0.0"
  checksum: 10/69ea78cd9f16ad38120372e71ba98b64acecd95bbcbcdad811f857dc192bad81ace021f8def012ce19178583db8d46afd1a00b3e8c88527e978e049edbc23252
  languageName: node
  linkType: hard

"cacheable-request@npm:^10.2.8":
  version: 10.2.14
  resolution: "cacheable-request@npm:10.2.14"
  dependencies:
    "@types/http-cache-semantics": "npm:^4.0.2"
    get-stream: "npm:^6.0.1"
    http-cache-semantics: "npm:^4.1.1"
    keyv: "npm:^4.5.3"
    mimic-response: "npm:^4.0.0"
    normalize-url: "npm:^8.0.0"
    responselike: "npm:^3.0.0"
  checksum: 10/102f454ac68eb66f99a709c5cf65e90ed89f1b9269752578d5a08590b3986c3ea47a5d9dff208fe7b65855a29da129a2f23321b88490106898e0ba70b807c912
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/00482c1f6aa7cfb30fb1dbeb13873edf81cfac7c29ed67a5957d60635a56b2a4a480f1016ddbdb3395cc37900d46037fb965043a51c5c789ffeab4fc535d18b5
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10/ef2b96e126ec0e58a7ff694db43f4d0d44f80e641370c21549ed911fecbdbc2df3ebc9bddad918d6bbdefeafb60bb3337902006d5176d72bcd2da74820991af7
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^6.0.0, camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10/8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"cbor@npm:^8.1.0":
  version: 8.1.0
  resolution: "cbor@npm:8.1.0"
  dependencies:
    nofilter: "npm:^3.1.0"
  checksum: 10/fc6c6d4f8d14def3a0f2ef111f4fc14b3b0bc91d22ed8fd0eb005095c4699c723a45721e515d713571148d0d965ceeb771f4ad422953cb4e9658b379991b52c9
  languageName: node
  linkType: hard

"cbor@npm:^9.0.0":
  version: 9.0.2
  resolution: "cbor@npm:9.0.2"
  dependencies:
    nofilter: "npm:^3.1.0"
  checksum: 10/a64f7d4dafed933adeafe7745e2ce9f39a2e669eba73db96de6bd1b39c2dbde4bdd51d0240beed179cc429a7dc8653c8d7c991c5addb9f4e0cee8cd167d87116
  languageName: node
  linkType: hard

"chai-as-promised@npm:^7.1.1":
  version: 7.1.2
  resolution: "chai-as-promised@npm:7.1.2"
  dependencies:
    check-error: "npm:^1.0.2"
  peerDependencies:
    chai: ">= 2.1.2 < 6"
  checksum: 10/be372540dad92ef85cde3954bc0e9b0b33e4e6454f3740b17bfb16e36eda638911619089c05a4e4f2bf6722563bf893bb78c2af59b318c23abb2199e5c20ca1f
  languageName: node
  linkType: hard

"chai@npm:4.5.0":
  version: 4.5.0
  resolution: "chai@npm:4.5.0"
  dependencies:
    assertion-error: "npm:^1.1.0"
    check-error: "npm:^1.0.3"
    deep-eql: "npm:^4.1.3"
    get-func-name: "npm:^2.0.2"
    loupe: "npm:^2.3.6"
    pathval: "npm:^1.1.1"
    type-detect: "npm:^4.1.0"
  checksum: 10/cde341aee15b0a51559c7cfc20788dcfb4d586a498cfb93b937bb568fd45c777b73b1461274be6092b6bf868adb4e3a63f3fec13c89f7d8fb194f84c6fa42d5f
  languageName: node
  linkType: hard

"chalk@npm:4.1.2, chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"chalk@npm:^2.4.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 10/3d1d103433166f6bfe82ac75724951b33769675252d8417317363ef9d54699b7c3b2d46671b772b893a8e50c3ece70c4b933c73c01e81bc60ea4df9b55afa303
  languageName: node
  linkType: hard

"charenc@npm:>= 0.0.1":
  version: 0.0.2
  resolution: "charenc@npm:0.0.2"
  checksum: 10/81dcadbe57e861d527faf6dd3855dc857395a1c4d6781f4847288ab23cffb7b3ee80d57c15bba7252ffe3e5e8019db767757ee7975663ad2ca0939bb8fcaf2e5
  languageName: node
  linkType: hard

"check-error@npm:^1.0.2, check-error@npm:^1.0.3":
  version: 1.0.3
  resolution: "check-error@npm:1.0.3"
  dependencies:
    get-func-name: "npm:^2.0.2"
  checksum: 10/e2131025cf059b21080f4813e55b3c480419256914601750b0fee3bd9b2b8315b531e551ef12560419b8b6d92a3636511322752b1ce905703239e7cc451b6399
  languageName: node
  linkType: hard

"chokidar@npm:^3.4.0, chokidar@npm:^3.5.2, chokidar@npm:^3.5.3":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10/c327fb07704443f8d15f7b4a7ce93b2f0bc0e6cea07ec28a7570aa22cd51fcf0379df589403976ea956c369f25aa82d84561947e227cd925902e1751371658df
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10/bf2a575ea5596000e88f5db95461a9d59ad2047e939d5a4aac59dd472d126be8f1c1ff3c7654b477cf532d18f42a97279ef80ee847972fd2a25410bf00b80b59
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 10/3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.6
  resolution: "cipher-base@npm:1.0.6"
  dependencies:
    inherits: "npm:^2.0.4"
    safe-buffer: "npm:^5.2.1"
  checksum: 10/faf232deff2351448ea23d265eb8723e035ebbb454baca45fb60c1bd71056ede8b153bef1b221e067f13e6b9288ebb83bb6ae2d5dd4cec285411f9fc22ec1f5b
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 10/2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-boxes@npm:^2.2.1":
  version: 2.2.1
  resolution: "cli-boxes@npm:2.2.1"
  checksum: 10/be79f8ec23a558b49e01311b39a1ea01243ecee30539c880cf14bf518a12e223ef40c57ead0cb44f509bffdffc5c129c746cd50d863ab879385370112af4f585
  languageName: node
  linkType: hard

"cli-table3@npm:^0.6.0, cli-table3@npm:^0.6.3":
  version: 0.6.5
  resolution: "cli-table3@npm:0.6.5"
  dependencies:
    "@colors/colors": "npm:1.5.0"
    string-width: "npm:^4.2.0"
  dependenciesMeta:
    "@colors/colors":
      optional: true
  checksum: 10/8dca71256f6f1367bab84c33add3f957367c7c43750a9828a4212ebd31b8df76bd7419d386e3391ac7419698a8540c25f1a474584028f35b170841cde2e055c5
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10/db858c49af9d59a32d603987e6fddaca2ce716cd4602ba5a2bb3a5af1351eebe82aba8dff3ef3e1b331f7fa9d40ca66e67bdf8e7c327ce0ea959747ead65c0ef
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: 10/ffa319025045f2973919d155f25e7c00d08836b6b33ea2d205418c59bd63a665d713c52d9737a9e0fe467fb194b40fbef1d849bae80d674568ee220a31ef3d10
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 10/09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10/2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"command-exists@npm:^1.2.8":
  version: 1.2.9
  resolution: "command-exists@npm:1.2.9"
  checksum: 10/46fb3c4d626ca5a9d274f8fe241230817496abc34d12911505370b7411999e183c11adff7078dd8a03ec4cf1391290facda40c6a4faac8203ae38c985eaedd63
  languageName: node
  linkType: hard

"command-line-args@npm:^5.1.1":
  version: 5.2.1
  resolution: "command-line-args@npm:5.2.1"
  dependencies:
    array-back: "npm:^3.1.0"
    find-replace: "npm:^3.0.0"
    lodash.camelcase: "npm:^4.3.0"
    typical: "npm:^4.0.0"
  checksum: 10/e6a42652ae8843fbb56e2fba1e85da00a16a0482896bb1849092e1bc70b8bf353d945e69732bf4ae98370ff84e8910ff4933af8f2f747806a6b2cb5074799fdb
  languageName: node
  linkType: hard

"command-line-usage@npm:^6.1.0":
  version: 6.1.3
  resolution: "command-line-usage@npm:6.1.3"
  dependencies:
    array-back: "npm:^4.0.2"
    chalk: "npm:^2.4.2"
    table-layout: "npm:^1.0.2"
    typical: "npm:^5.2.0"
  checksum: 10/902901582a543b26f55f90fc0f266c08a603a92bfadd8d07c66679f3d9eea2c074a039404126b0c4b65ff8452153c5f2010ea2f4ec14b70be0c77241f6d5bd53
  languageName: node
  linkType: hard

"commander@npm:^10.0.0":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 10/8799faa84a30da985802e661cc9856adfaee324d4b138413013ef7f087e8d7924b144c30a1f1405475f0909f467665cd9e1ce13270a2f41b141dab0b7a58f3fb
  languageName: node
  linkType: hard

"commander@npm:^8.1.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 10/6b7b5d334483ce24bd73c5dac2eab901a7dbb25fd983ea24a1eeac6e7166bb1967f641546e8abf1920afbde86a45fbfe5812fbc69d0dc451bb45ca416a12a3a3
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.11":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: "npm:^1.3.4"
    proto-list: "npm:~1.2.1"
  checksum: 10/83d22cabf709e7669f6870021c4d552e4fc02e9682702b726be94295f42ce76cfed00f70b2910ce3d6c9465d9758e191e28ad2e72ff4e3331768a90da6c1ef03
  languageName: node
  linkType: hard

"cookie@npm:^0.4.1":
  version: 0.4.2
  resolution: "cookie@npm:0.4.2"
  checksum: 10/2e1de9fdedca54881eab3c0477aeb067f281f3155d9cfee9d28dfb252210d09e85e9d175c0a60689661feb9e35e588515352f2456bc1f8e8db4267e05fd70137
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.0.0":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: "npm:^3.3.0"
    js-yaml: "npm:^4.1.0"
    parse-json: "npm:^5.2.0"
    path-type: "npm:^4.0.0"
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/91d082baca0f33b1c085bf010f9ded4af43cbedacba8821da0fb5667184d0a848addc52c31fadd080007f904a555319c238cf5f4c03e6d58ece2e4876b2e73d6
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.1.2, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: "npm:^1.0.1"
    inherits: "npm:^2.0.1"
    md5.js: "npm:^1.3.4"
    ripemd160: "npm:^2.0.1"
    sha.js: "npm:^2.4.0"
  checksum: 10/3cfef32043b47a8999602af9bcd74966db6971dd3eb828d1a479f3a44d7f58e38c1caf34aa21a01941cc8d9e1a841738a732f200f00ea155f8a8835133d2e7bc
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.4, create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: "npm:^1.0.3"
    create-hash: "npm:^1.1.0"
    inherits: "npm:^2.0.1"
    ripemd160: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 10/2b26769f87e99ef72150bf99d1439d69272b2e510e23a2b8daf4e93e2412f4842504237d726044fa797cb20ee0ec8bee78d414b11f2d7ca93299185c93df0dae
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: 10/a9a1503d4390d8b59ad86f4607de7870b39cad43d929813599a23714831e81c520bddf61bcdd1f8e30f05fd3a2b71ae8538e946eb2786dc65c2bbc520f692eff
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: "npm:^7.0.1"
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 10/e99911f0d31c20e990fd92d6fd001f4b01668a303221227cc5cb42ed155f086351b1b3bd2699b200e527ab13011b032801f8ce638e6f09f854bdf744095e604c
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/0d52657d7ae36eb130999dffff1168ec348687b48dd38e2ff59992ed916c88d328cf1d07ff4a4a10bc78de5e1c23f04b306d569e42f7a2293915c081e4dfee86
  languageName: node
  linkType: hard

"crypt@npm:>= 0.0.1":
  version: 0.0.2
  resolution: "crypt@npm:0.0.2"
  checksum: 10/2c72768de3d28278c7c9ffd81a298b26f87ecdfe94415084f339e6632f089b43fe039f2c93f612bcb5ffe447238373d93b2e8c90894cba6cfb0ac7a74616f8b9
  languageName: node
  linkType: hard

"death@npm:^1.1.0":
  version: 1.1.0
  resolution: "death@npm:1.1.0"
  checksum: 10/b6fc4d1b8fbfc84486a025d36c540795c5ae9368f580a31fc2740935d0a9afbd31a214b00650335e97756f4c1a3fae895adc45795aeb9ef00694968311ab844d
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.3.5":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/8e2709b2144f03c7950f8804d01ccb3786373df01e406a0f66928e47001cf2d336cbed9ee137261d4f90d68d8679468c755e3548ed83ddacdc82b194d2468afe
  languageName: node
  linkType: hard

"decamelize@npm:^4.0.0":
  version: 4.0.0
  resolution: "decamelize@npm:4.0.0"
  checksum: 10/b7d09b82652c39eead4d6678bb578e3bebd848add894b76d0f6b395bc45b2d692fb88d977e7cfb93c4ed6c119b05a1347cef261174916c2e75c0a8ca57da1809
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: "npm:^3.1.0"
  checksum: 10/d377cf47e02d805e283866c3f50d3d21578b779731e8c5072d6ce8c13cc31493db1c2f6784da9d1d5250822120cefa44f1deab112d5981015f2e17444b763812
  languageName: node
  linkType: hard

"deep-eql@npm:^4.0.1, deep-eql@npm:^4.1.3":
  version: 4.1.4
  resolution: "deep-eql@npm:4.1.4"
  dependencies:
    type-detect: "npm:^4.0.0"
  checksum: 10/f04f4d581f044a824a6322fe4f68fbee4d6780e93fc710cd9852cbc82bfc7010df00f0e05894b848abbe14dc3a25acac44f424e181ae64d12f2ab9d0a875a5ef
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0, deep-extend@npm:~0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 10/7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3, deep-is@npm:~0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10/ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"defer-to-connect@npm:^2.0.1":
  version: 2.0.1
  resolution: "defer-to-connect@npm:2.0.1"
  checksum: 10/8a9b50d2f25446c0bfefb55a48e90afd58f85b21bcf78e9207cd7b804354f6409032a1705c2491686e202e64fc05f147aa5aa45f9aa82627563f045937f5791b
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10/46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10/c0c8ff36079ce5ada64f46cc9d6fd47ebcf38241105b6e0c98f412e8ad91f084bcf906ff644cc3a4bd876ca27a62accb8b0fff72ea6ed1a414b89d8506f4a5ca
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: 10/ec09ec2101934ca5966355a229d77afcad5911c92e2a77413efda5455636c4cf2ce84057e2d7715227a2eeeda04255b849bd3ae3a4dd22eb22e86e76456df069
  languageName: node
  linkType: hard

"diff@npm:^5.2.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 10/01b7b440f83a997350a988e9d2f558366c0f90f15be19f4aa7f1bb3109a4e153dfc3b9fbf78e14ea725717017407eeaa2271e3896374a0181e8f52445740846d
  languageName: node
  linkType: hard

"difflib@npm:^0.2.4":
  version: 0.2.4
  resolution: "difflib@npm:0.2.4"
  dependencies:
    heap: "npm:>= 0.2.0"
  checksum: 10/35c09c9469f762b72703a1eee4bd7bae6227fac96cef4605cd00f0ab3773b547584aefd2c5224f85c5b1701f0e8cedebd45afbb853b01d1d44863b4720cfcd35
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10/fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10/b4b28f1df5c563f7d876e7461254a4597b8cabe915abe94d7c5d1633fed263fcf9a85e8d3836591fc2d040108e822b0d32758e5ec1fe31c590dc7e08086e3e48
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10/e68a16834f1a41cc2dfb01563bc150668ad675e6cd09191211467b5c0806b6ecd6ec438e021aa8e01cd0e72d2b70ef4302bec7cc0fe15b6955f85230b62dc8a9
  languageName: node
  linkType: hard

"ds-test@github:dapphub/ds-test#e282159d5170298eb2455a6c05280ab5a73a4ef0":
  version: 1.0.0
  resolution: "ds-test@git+ssh://**************/dapphub/ds-test.git#commit=e282159d5170298eb2455a6c05280ab5a73a4ef0"
  checksum: 10/a63cada107d8f2775934bc580f04cb6f6509f843cb41cbc3a617e77b2e628a86d7fd858f964e7e2d6f41c3797c0e16ec2d87a6cb4c6187c5b6c2bc969ccae4b3
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"elliptic@npm:6.5.4":
  version: 6.5.4
  resolution: "elliptic@npm:6.5.4"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10/2cd7ff4b69720dbb2ca1ca650b2cf889d1df60c96d4a99d331931e4fe21e45a7f3b8074e86618ca7e56366c4b6258007f234f9d61d9b0c87bbbc8ea990b99e94
  languageName: node
  linkType: hard

"elliptic@npm:6.6.1, elliptic@npm:^6.5.2, elliptic@npm:^6.5.7":
  version: 6.6.1
  resolution: "elliptic@npm:6.6.1"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10/dc678c9febd89a219c4008ba3a9abb82237be853d9fd171cd602c8fb5ec39927e65c6b5e7a1b2a4ea82ee8e0ded72275e7932bb2da04a5790c2638b818e4e1c5
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encode-utf8@npm:^1.0.2":
  version: 1.0.3
  resolution: "encode-utf8@npm:1.0.3"
  checksum: 10/0204c37cda21bf19bb8f87f7ec6c89a23d43488c2ef1e5cfa40b64ee9568e63e15dc323fa7f50a491e2c6d33843a6b409f6de09afbf6cf371cb8da596cc64b44
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enquirer@npm:^2.3.0, enquirer@npm:^2.3.6":
  version: 2.4.1
  resolution: "enquirer@npm:2.4.1"
  dependencies:
    ansi-colors: "npm:^4.1.1"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/b3726486cd98f0d458a851a03326a2a5dd4d84f37ff94ff2a2960c915e0fc865865da3b78f0877dc36ac5c1189069eca603e82ec63d5bc6b0dd9985bf6426d7a
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10/d547740aa29c34e753fb6fed2c5de81802438529c12b3673bd37b6bb1fe49b9b7abdc3c11e6062fe625d8a296b3cf769a80f878865e25e685f787763eede3ffb
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/86814bf8afbcd8966653f731415888019d4bc4aca6b6c354132a7a75bb87566751e320369654a101d23a91c87a85c79b178bcf40332839bd347aff437c4fb65f
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10/6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10/98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escodegen@npm:1.8.x":
  version: 1.8.1
  resolution: "escodegen@npm:1.8.1"
  dependencies:
    esprima: "npm:^2.7.1"
    estraverse: "npm:^1.9.1"
    esutils: "npm:^2.0.2"
    optionator: "npm:^0.8.1"
    source-map: "npm:~0.2.0"
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: ./bin/escodegen.js
    esgenerate: ./bin/esgenerate.js
  checksum: 10/f7c4f9639f4198848784548f268bb4bbd55f1a12344af79ea4a8978168c2009b0bfc1047dece1e0fdca4ff539fe9dffb0b4183ecab22ab91dea88328487da86a
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^9.1.0":
  version: 9.1.0
  resolution: "eslint-config-prettier@npm:9.1.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10/411e3b3b1c7aa04e3e0f20d561271b3b909014956c4dba51c878bf1a23dbb8c800a3be235c46c4732c70827276e540b6eed4636d9b09b444fd0a8e07f0fcd830
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/5c660fb905d5883ad018a6fea2b49f3cb5b1cbf2cd4bd08e98646e9864f9bc2c74c0839bed2d292e90a4a328833accc197c8f0baed89cbe8d605d6f918465491
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10/3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint@npm:^8.57.1":
  version: 8.57.1
  resolution: "eslint@npm:8.57.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.6.1"
    "@eslint/eslintrc": "npm:^2.1.4"
    "@eslint/js": "npm:8.57.1"
    "@humanwhocodes/config-array": "npm:^0.13.0"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@nodelib/fs.walk": "npm:^1.2.8"
    "@ungap/structured-clone": "npm:^1.2.0"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.2"
    debug: "npm:^4.3.2"
    doctrine: "npm:^3.0.0"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^7.2.2"
    eslint-visitor-keys: "npm:^3.4.3"
    espree: "npm:^9.6.1"
    esquery: "npm:^1.4.2"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^6.0.1"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    globals: "npm:^13.19.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    is-path-inside: "npm:^3.0.3"
    js-yaml: "npm:^4.1.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    levn: "npm:^0.4.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
    strip-ansi: "npm:^6.0.1"
    text-table: "npm:^0.2.0"
  bin:
    eslint: bin/eslint.js
  checksum: 10/5504fa24879afdd9f9929b2fbfc2ee9b9441a3d464efd9790fbda5f05738858530182029f13323add68d19fec749d3ab4a70320ded091ca4432b1e9cc4ed104c
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10/255ab260f0d711a54096bdeda93adff0eadf02a6f9b92f02b323e83a2b7fc258797919437ad331efec3930475feb0142c5ecaaf3cdab4befebd336d47d3f3134
  languageName: node
  linkType: hard

"esprima@npm:2.7.x, esprima@npm:^2.7.1":
  version: 2.7.3
  resolution: "esprima@npm:2.7.3"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10/7508285b882012deea8f68dff4b759f9a17e9317ad8c7449969feb1e2efc083fa4a0012139a4722f1e96da81ece0ac319756c8e79a01e5ddb4b36ae483464d3f
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10/f1d3c622ad992421362294f7acf866aa9409fbad4eb2e8fa230bd33944ce371d32279667b242d8b8907ec2b6ad7353a717f3c0e60e748873a34a7905174bc0eb
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10/c587fb8ec9ed83f2b1bc97cf2f6854cc30bf784a79d62ba08c6e358bf22280d69aee12827521cf38e69ae9761d23fb7fde593ce315610f85655c139d99b05e5a
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^1.9.1":
  version: 1.9.3
  resolution: "estraverse@npm:1.9.3"
  checksum: 10/682a7e2fda17fd3e892b78a8347d055f923465598f5d713354aefd53a3348b2a1a6ee8df41031d8f5ad9802cfd27c29caac84c2f58ce3b2df659d43d668c870b
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"ethereum-bloom-filters@npm:^1.0.6":
  version: 1.2.0
  resolution: "ethereum-bloom-filters@npm:1.2.0"
  dependencies:
    "@noble/hashes": "npm:^1.4.0"
  checksum: 10/86556762d0dff5d90e67fb5c76202b1258dc7de19f1dd537a339cf199094df0fc9f0f69a15bd2d6fe672a3ba4615b2493e53c72230b724d10c0d2daae7363936
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:0.1.3, ethereum-cryptography@npm:^0.1.3":
  version: 0.1.3
  resolution: "ethereum-cryptography@npm:0.1.3"
  dependencies:
    "@types/pbkdf2": "npm:^3.0.0"
    "@types/secp256k1": "npm:^4.0.1"
    blakejs: "npm:^1.1.0"
    browserify-aes: "npm:^1.2.0"
    bs58check: "npm:^2.1.2"
    create-hash: "npm:^1.2.0"
    create-hmac: "npm:^1.1.7"
    hash.js: "npm:^1.1.7"
    keccak: "npm:^3.0.0"
    pbkdf2: "npm:^3.0.17"
    randombytes: "npm:^2.1.0"
    safe-buffer: "npm:^5.1.2"
    scrypt-js: "npm:^3.0.0"
    secp256k1: "npm:^4.0.1"
    setimmediate: "npm:^1.0.5"
  checksum: 10/975e476782746acd97d5b37366801ae622a52fb31e5d83f600804be230a61ef7b9d289dcecd9c308fb441967caf3a6e3768dd7c8add6441fcc60c398175d5a96
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^1.0.3":
  version: 1.2.0
  resolution: "ethereum-cryptography@npm:1.2.0"
  dependencies:
    "@noble/hashes": "npm:1.2.0"
    "@noble/secp256k1": "npm:1.7.1"
    "@scure/bip32": "npm:1.1.5"
    "@scure/bip39": "npm:1.1.1"
  checksum: 10/e8b2ab91e0237ed83a6e6ab1aa2a61ee081dea137ac994c7daa935b0b620e866f70e2ac7eb2fb8db2dec044fe22283d2bf940598417e4dccd15a2b704a817a1b
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^2.0.0, ethereum-cryptography@npm:^2.1.2, ethereum-cryptography@npm:^2.1.3, ethereum-cryptography@npm:^2.2.1":
  version: 2.2.1
  resolution: "ethereum-cryptography@npm:2.2.1"
  dependencies:
    "@noble/curves": "npm:1.4.2"
    "@noble/hashes": "npm:1.4.0"
    "@scure/bip32": "npm:1.4.0"
    "@scure/bip39": "npm:1.3.0"
  checksum: 10/ab123bbfe843500ac2d645ce9edc4bc814962ffb598db6bf8bf01fbecac656e6c81ff4cf2472f1734844bbcbad2bf658d8b699cb7248d768e0f06ae13ecf43b8
  languageName: node
  linkType: hard

"ethereumjs-abi@npm:^0.6.8":
  version: 0.6.8
  resolution: "ethereumjs-abi@npm:0.6.8"
  dependencies:
    bn.js: "npm:^4.11.8"
    ethereumjs-util: "npm:^6.0.0"
  checksum: 10/d4633ca30048b53c0f900ba5d7d6013ca228822055fbd93f975befc41f5c3054e0fffc27562d78050f164170e546af66c20e9ca1d35e67ea861df07d59a65a91
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^6.0.0, ethereumjs-util@npm:^6.2.1":
  version: 6.2.1
  resolution: "ethereumjs-util@npm:6.2.1"
  dependencies:
    "@types/bn.js": "npm:^4.11.3"
    bn.js: "npm:^4.11.0"
    create-hash: "npm:^1.1.2"
    elliptic: "npm:^6.5.2"
    ethereum-cryptography: "npm:^0.1.3"
    ethjs-util: "npm:0.1.6"
    rlp: "npm:^2.2.3"
  checksum: 10/dedc8a623e21d1864b09c47f28851fc0fca6233cdefa4755a308507822ce75c893bbb2c3ba422109d1247986ec757941718f06574437e41b0d68604108b03fd0
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^7.1.4":
  version: 7.1.5
  resolution: "ethereumjs-util@npm:7.1.5"
  dependencies:
    "@types/bn.js": "npm:^5.1.0"
    bn.js: "npm:^5.1.2"
    create-hash: "npm:^1.1.2"
    ethereum-cryptography: "npm:^0.1.3"
    rlp: "npm:^2.2.4"
  checksum: 10/f28fc1ebb8f35bf9e418f76f51be737d94d603b912c3e014c4e87cd45ccd1b10bdfef764c8f152574b57e9faa260a18773cbc110f9e0a754d6b3730699e54dc9
  languageName: node
  linkType: hard

"ethers@npm:6.13.2":
  version: 6.13.2
  resolution: "ethers@npm:6.13.2"
  dependencies:
    "@adraffy/ens-normalize": "npm:1.10.1"
    "@noble/curves": "npm:1.2.0"
    "@noble/hashes": "npm:1.3.2"
    "@types/node": "npm:18.15.13"
    aes-js: "npm:4.0.0-beta.5"
    tslib: "npm:2.4.0"
    ws: "npm:8.17.1"
  checksum: 10/e611c2e2c5340982dfd1f004895f55abda11748a7edec9e6315226dec42d58aa61b827dd389ec904db5f9a244c475ae795e528da579251fdf62e914bde12809e
  languageName: node
  linkType: hard

"ethers@npm:^5.7.0":
  version: 5.8.0
  resolution: "ethers@npm:5.8.0"
  dependencies:
    "@ethersproject/abi": "npm:5.8.0"
    "@ethersproject/abstract-provider": "npm:5.8.0"
    "@ethersproject/abstract-signer": "npm:5.8.0"
    "@ethersproject/address": "npm:5.8.0"
    "@ethersproject/base64": "npm:5.8.0"
    "@ethersproject/basex": "npm:5.8.0"
    "@ethersproject/bignumber": "npm:5.8.0"
    "@ethersproject/bytes": "npm:5.8.0"
    "@ethersproject/constants": "npm:5.8.0"
    "@ethersproject/contracts": "npm:5.8.0"
    "@ethersproject/hash": "npm:5.8.0"
    "@ethersproject/hdnode": "npm:5.8.0"
    "@ethersproject/json-wallets": "npm:5.8.0"
    "@ethersproject/keccak256": "npm:5.8.0"
    "@ethersproject/logger": "npm:5.8.0"
    "@ethersproject/networks": "npm:5.8.0"
    "@ethersproject/pbkdf2": "npm:5.8.0"
    "@ethersproject/properties": "npm:5.8.0"
    "@ethersproject/providers": "npm:5.8.0"
    "@ethersproject/random": "npm:5.8.0"
    "@ethersproject/rlp": "npm:5.8.0"
    "@ethersproject/sha2": "npm:5.8.0"
    "@ethersproject/signing-key": "npm:5.8.0"
    "@ethersproject/solidity": "npm:5.8.0"
    "@ethersproject/strings": "npm:5.8.0"
    "@ethersproject/transactions": "npm:5.8.0"
    "@ethersproject/units": "npm:5.8.0"
    "@ethersproject/wallet": "npm:5.8.0"
    "@ethersproject/web": "npm:5.8.0"
    "@ethersproject/wordlists": "npm:5.8.0"
  checksum: 10/4a78952fe660ab9414bd2907d7db34f12b67c4c3f3cbfc2dfab5ea1862d70400b731ef847b708665d4f42f83dafacb2045f14f66980c34fac0418dbc3bfc016e
  languageName: node
  linkType: hard

"ethers@npm:^6.7.0":
  version: 6.14.3
  resolution: "ethers@npm:6.14.3"
  dependencies:
    "@adraffy/ens-normalize": "npm:1.10.1"
    "@noble/curves": "npm:1.2.0"
    "@noble/hashes": "npm:1.3.2"
    "@types/node": "npm:22.7.5"
    aes-js: "npm:4.0.0-beta.5"
    tslib: "npm:2.7.0"
    ws: "npm:8.17.1"
  checksum: 10/ce68b962f117fd651090bd8096fde708428ce23f0d044c365bc8cbf2e8f3cb70e1661ff0194364848ccac06d607d4a977f2a0c9e370b9712809c0ca6c36131f9
  languageName: node
  linkType: hard

"ethers@npm:~5.7.0":
  version: 5.7.2
  resolution: "ethers@npm:5.7.2"
  dependencies:
    "@ethersproject/abi": "npm:5.7.0"
    "@ethersproject/abstract-provider": "npm:5.7.0"
    "@ethersproject/abstract-signer": "npm:5.7.0"
    "@ethersproject/address": "npm:5.7.0"
    "@ethersproject/base64": "npm:5.7.0"
    "@ethersproject/basex": "npm:5.7.0"
    "@ethersproject/bignumber": "npm:5.7.0"
    "@ethersproject/bytes": "npm:5.7.0"
    "@ethersproject/constants": "npm:5.7.0"
    "@ethersproject/contracts": "npm:5.7.0"
    "@ethersproject/hash": "npm:5.7.0"
    "@ethersproject/hdnode": "npm:5.7.0"
    "@ethersproject/json-wallets": "npm:5.7.0"
    "@ethersproject/keccak256": "npm:5.7.0"
    "@ethersproject/logger": "npm:5.7.0"
    "@ethersproject/networks": "npm:5.7.1"
    "@ethersproject/pbkdf2": "npm:5.7.0"
    "@ethersproject/properties": "npm:5.7.0"
    "@ethersproject/providers": "npm:5.7.2"
    "@ethersproject/random": "npm:5.7.0"
    "@ethersproject/rlp": "npm:5.7.0"
    "@ethersproject/sha2": "npm:5.7.0"
    "@ethersproject/signing-key": "npm:5.7.0"
    "@ethersproject/solidity": "npm:5.7.0"
    "@ethersproject/strings": "npm:5.7.0"
    "@ethersproject/transactions": "npm:5.7.0"
    "@ethersproject/units": "npm:5.7.0"
    "@ethersproject/wallet": "npm:5.7.0"
    "@ethersproject/web": "npm:5.7.1"
    "@ethersproject/wordlists": "npm:5.7.0"
  checksum: 10/227dfa88a2547c799c0c3c9e92e5e246dd11342f4b495198b3ae7c942d5bf81d3970fcef3fbac974a9125d62939b2d94f3c0458464e702209b839a8e6e615028
  languageName: node
  linkType: hard

"ethjs-unit@npm:0.1.6":
  version: 0.1.6
  resolution: "ethjs-unit@npm:0.1.6"
  dependencies:
    bn.js: "npm:4.11.6"
    number-to-bn: "npm:1.7.0"
  checksum: 10/35086cb671806992ec36d5dd43ab67e68ad7a9237e42c0e963f9081c88e40147cda86c1a258b0a3180bf2b7bc1960e607c5bcaefdb2196e0f3564acf73276189
  languageName: node
  linkType: hard

"ethjs-util@npm:0.1.6, ethjs-util@npm:^0.1.6":
  version: 0.1.6
  resolution: "ethjs-util@npm:0.1.6"
  dependencies:
    is-hex-prefixed: "npm:1.0.0"
    strip-hex-prefix: "npm:1.0.0"
  checksum: 10/02e1d37f743a78742651a11be35461dfe8ed653f113d630435aada8036e1e199691c2cfffbbf1e800bfdeb14bb34c7ed69fab5d3c727058c1daf3effc6bf6f69
  languageName: node
  linkType: hard

"evm-bn@npm:^1.1.2":
  version: 1.1.2
  resolution: "evm-bn@npm:1.1.2"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.5.0"
    from-exponential: "npm:^1.1.1"
  peerDependencies:
    "@ethersproject/bignumber": 5.x
  checksum: 10/863c9f055b07767f916a76a231aa0110f1e19724cdc2c8fa60e68c16f8d6e6b0652ae3780a70628fe8e32144fa58e678e6f893c3c2caf25658aa8c435a3f5787
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: "npm:^1.3.4"
    node-gyp: "npm:latest"
    safe-buffer: "npm:^5.1.1"
  checksum: 10/ad4e1577f1a6b721c7800dcc7c733fe01f6c310732bb5bf2240245c2a5b45a38518b91d8be2c610611623160b9d1c0e91f1ce96d639f8b53e8894625cf20fa45
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10/ca2f01f1aa4dafd3f3917bd531ab5be08c6f5f4b2389d2e974f903de3cbeb50b9633374353516b6afd70905775e33aba11afab1232d3acf0aa2963b98a611c51
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.2.0":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10/9e57415bc69cd6efcc720b3b8fe9fdaf42dcfc06f86f0f45378b1fa512598a8aac48aa3928c8751d58e2f01bb4ba4f07e4f3d9bc0d57586d45f1bd1e872c6cde
  languageName: node
  linkType: hard

"fast-glob@npm:^3.0.3, fast-glob@npm:^3.2.9":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10/dcc6432b269762dd47381d8b8358bf964d8f4f60286ac6aa41c01ade70bda459ff2001b516690b96d5365f68a49242966112b5d5cc9cd82395fa8f9d017c90ad
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6, fast-levenshtein@npm:~2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10/eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10/43c87cd03926b072a241590e49eca0e2dfe1d347ddffd4b15307613b42b8eacce00a315cf3c7374736b5f343f27e27ec88726260eb03a758336d507d6fbaba0a
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10/75679dc226316341c4f2a6b618571f51eac96779906faecd8921b984e844d6ae42fabb2df69b1071327d398d5716693ea9c9c8941f64ac9e89ec2032ce59d730
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.5
  resolution: "fdir@npm:6.4.5"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10/8f5a2107fe0486f61af9a0666f2b7c62a229c738330e22ff8795bfbaabcf2294fb79460b73830b8824fc6eef91e21f676bac66ca982d5ee7e92ee9b68c07775f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: "npm:^3.0.4"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"find-replace@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-replace@npm:3.0.0"
  dependencies:
    array-back: "npm:^3.0.1"
  checksum: 10/6b04bcfd79027f5b84aa1dfe100e3295da989bdac4b4de6b277f4d063e78f5c9e92ebc8a1fec6dd3b448c924ba404ee051cc759e14a3ee3e825fa1361025df08
  languageName: node
  linkType: hard

"find-up@npm:^2.1.0":
  version: 2.1.0
  resolution: "find-up@npm:2.1.0"
  dependencies:
    locate-path: "npm:^2.0.0"
  checksum: 10/43284fe4da09f89011f08e3c32cd38401e786b19226ea440b75386c1b12a4cb738c94969808d53a84f564ede22f732c8409e3cfc3f7fb5b5c32378ad0bbf28bd
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.3"
    rimraf: "npm:^3.0.2"
  checksum: 10/02381c6ece5e9fa5b826c9bbea481d7fd77645d96e4b0b1395238124d581d10e56f17f723d897b6d133970f7a57f0fab9148cbbb67237a0a0ffe794ba60c0c70
  languageName: node
  linkType: hard

"flat@npm:^5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fmix@npm:^0.1.0":
  version: 0.1.0
  resolution: "fmix@npm:0.1.0"
  dependencies:
    imul: "npm:^1.0.0"
  checksum: 10/c465344d4f169eaf10d45c33949a1e7a633f09dba2ac7063ce8ae8be743df5979d708f7f24900163589f047f5194ac5fc2476177ce31175e8805adfa7b8fb7a4
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.12.1, follow-redirects@npm:^1.14.0, follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10/e3ab42d1097e90d28b913903841e6779eb969b62a64706a3eb983e894a5db000fbd89296f45f08885a0e54cd558ef62e81be1165da9be25a6c44920da10f424c
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10/427b33f997a98073c0424e5c07169264a62cda806d8d2ded159b5b903fdfc8f0a1457e06b5fc35506497acb3f1e353f025edee796300209ac6231e80edece835
  languageName: node
  linkType: hard

"forge-std@github:foundry-rs/forge-std#v1.9.2":
  version: 1.9.2
  resolution: "forge-std@git+ssh://**************/foundry-rs/forge-std.git#commit=1714bee72e286e73f76e320d110e0eaf5c4e649d"
  checksum: 10/e80f468eadf2a0801f33bbe64d5d3395d7b0d46325de0823a22ad8947493ee50de50cef9b141a354b0ba645f7c58086deb110b7260a9b6087d34a2a32ac1e932
  languageName: node
  linkType: hard

"form-data-encoder@npm:^2.1.2":
  version: 2.1.4
  resolution: "form-data-encoder@npm:2.1.4"
  checksum: 10/3778e7db3c21457296e6fdbc4200642a6c01e8be9297256e845ee275f9ddaecb5f49bfb0364690ad216898c114ec59bf85f01ec823a70670b8067273415d62f6
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.3
  resolution: "form-data@npm:4.0.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10/22f6e55e6f32a5797a500ed7ca5aa9d690c4de6e1b3308f25f0d83a27d08d91a265ab59a190db2305b15144f8f07df08e8117bad6a93fc93de1baa838bfcc0b5
  languageName: node
  linkType: hard

"fp-ts@npm:1.19.3":
  version: 1.19.3
  resolution: "fp-ts@npm:1.19.3"
  checksum: 10/3b3426f9a033b3e1b43f68da1baeb9d25b1a7cfeda0f55d4eadf0a1ab951898edc8b3453e4fec3113c140c98fdbf5fe8ab5232d349376ea7920e280af4e52050
  languageName: node
  linkType: hard

"fp-ts@npm:^1.0.0":
  version: 1.19.5
  resolution: "fp-ts@npm:1.19.5"
  checksum: 10/17aa04bbbba9096ac32efd4f192de6211687cab195c423d4072a904f1346c2d508243880685d6f4bb4be29e5f337a67cfa211645e491491683b6aaff23b5dd4a
  languageName: node
  linkType: hard

"from-exponential@npm:^1.1.1":
  version: 1.1.1
  resolution: "from-exponential@npm:1.1.1"
  checksum: 10/af2765bd4f78836153c46cf4aff51ba9f2d1ac6b7d9568d69901d2045ba5b80a47de4f2a693bcc2006a7516cef71f636a4e42596cdd77a0addcc739539eb0f48
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10/05ce2c3b59049bcb7b52001acd000e44b3c4af4ec1f8839f383ef41ec0048e3cfa7fd8a637b1bddfefad319145db89be91f4b7c1db2908205d38bf91e7d1d3b7
  languageName: node
  linkType: hard

"fs-extra@npm:^11.1.1, fs-extra@npm:^11.2.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10/c9fe7b23dded1efe7bbae528d685c3206477e20cc60e9aaceb3f024f9b9ff2ee1f62413c161cb88546cc564009ab516dec99e9781ba782d869bb37e4fe04a97f
  languageName: node
  linkType: hard

"fs-extra@npm:^7.0.0, fs-extra@npm:^7.0.1":
  version: 7.0.1
  resolution: "fs-extra@npm:7.0.1"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 10/3fc6e56ba2f07c00d452163f27f21a7076b72ef7da8a50fef004336d59ef4c34deda11d10ecd73fd8fbcf20e4f575f52857293090b3c9f8741d4e0598be30fea
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 10/6fb12449f5349be724a138b4a7b45fe6a317d2972054517f5971959c26fbd17c0e145731a11c7324460262baa33e0a799b183ceace98f7a372c95fbb6f20f5de
  languageName: node
  linkType: hard

"fs-extra@npm:^9.1.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: "npm:^1.0.0"
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10/08600da1b49552ed23dfac598c8fc909c66776dd130fea54fbcad22e330f7fcc13488bb995f6bc9ce5651aa35b65702faf616fe76370ee56f1aade55da982dca
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10/e703107c28e362d8d7b910bbcbfd371e640a3bb45ae157a362b5952c0030c0b6d4981140ec319b347bce7adc025dd7813da1ff908a945ac214d64f5402a51b96
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10/b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-func-name@npm:^2.0.1, get-func-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "get-func-name@npm:2.0.2"
  checksum: 10/3f62f4c23647de9d46e6f76d2b3eafe58933a9b3830c60669e4180d6c601ce1b4aa310ba8366143f55e52b139f992087a9f0647274e8745621fa2af7e0acf13b
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/6e9dd920ff054147b6f44cb98104330e87caafae051b6d37b13384a45ba15e71af33c3baeac7cb630a0aaa23142718dcf25b45cfdd86c184c5dcb4e56d953a10
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.1":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10/781266d29725f35c59f1d214aedc92b0ae855800a980800e2923b3fbc4e56b3cb6e462c42e09a1cf1a00c64e056a78fa407cbe06c7c92b7e5cd49b4b85c2a497
  languageName: node
  linkType: hard

"ghost-testrpc@npm:^0.0.2":
  version: 0.0.2
  resolution: "ghost-testrpc@npm:0.0.2"
  dependencies:
    chalk: "npm:^2.4.2"
    node-emoji: "npm:^1.10.0"
  bin:
    testrpc-sc: ./index.js
  checksum: 10/e52f1d7ad5ac84c8528b3884496270c65056264b37373c00631ca874674b3cfd7c45ae2fc787ba3ff75e63273188f29d155d995ce3e361244bd55a9c365e444f
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:7.1.7":
  version: 7.1.7
  resolution: "glob@npm:7.1.7"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.0.4"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/ff5aab0386e9cace92b0550d42085b71013c5ea382982dd7fdded998a559635f61413b8ba6fb7294eef289c83b52f4e64136f888300ac8afc4f3e5623182d6c8
  languageName: node
  linkType: hard

"glob@npm:7.2.0":
  version: 7.2.0
  resolution: "glob@npm:7.2.0"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.0.4"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/bc78b6ea0735b6e23d20678aba4ae6a4760e8c9527e3c4683ac25b14e70f55f9531245dcf25959b70cbc4aa3dcce1fc37ab65fd026a4cbd70aa3a44880bd396b
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"glob@npm:^5.0.15":
  version: 5.0.15
  resolution: "glob@npm:5.0.15"
  dependencies:
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:2 || 3"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/4a1f2401329d94b5c25c6ac16276aceccc52b865bd9b2b9198da21fc937d021bfd87463ae44de9a9e4794894a49bc619ebaf7e5b12182bcf97e2ceb68ae116d7
  languageName: node
  linkType: hard

"glob@npm:^7.0.0, glob@npm:^7.1.2, glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/59452a9202c81d4508a43b8af7082ca5c76452b9fcc4a9ab17655822e6ce9b21d4f8fbadabe4fe3faef448294cec249af305e2cd824b7e9aaf689240e5e96a7b
  languageName: node
  linkType: hard

"glob@npm:^8.0.3, glob@npm:^8.1.0":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^5.0.1"
    once: "npm:^1.3.0"
  checksum: 10/9aab1c75eb087c35dbc41d1f742e51d0507aa2b14c910d96fb8287107a10a22f4bbdce26fc0a3da4c69a20f7b26d62f1640b346a4f6e6becfff47f335bb1dc5e
  languageName: node
  linkType: hard

"global-modules@npm:^2.0.0":
  version: 2.0.0
  resolution: "global-modules@npm:2.0.0"
  dependencies:
    global-prefix: "npm:^3.0.0"
  checksum: 10/4aee73adf533fe82ead2ad15c8bfb6ea4fb29e16d2d067521ab39d3b45b8f834d71c47a807e4f8f696e79497c3946d4ccdcd708da6f3a4522d65b087b8852f64
  languageName: node
  linkType: hard

"global-prefix@npm:^3.0.0":
  version: 3.0.0
  resolution: "global-prefix@npm:3.0.0"
  dependencies:
    ini: "npm:^1.3.5"
    kind-of: "npm:^6.0.2"
    which: "npm:^1.3.1"
  checksum: 10/a405b9f83c7d88a49dc1c1e458d6585e258356810d3d0f41094265152a06a0f393b14d911f45616e35a4ce3894176a73be2984883575e778f55e90bf812d7337
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10/9f054fa38ff8de8fa356502eb9d2dae0c928217b8b5c8de1f09f5c9b6c8a96d8b9bd3afc49acbcd384a98a81fea713c859e1b09e214c60509517bb8fc2bc13c2
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: 10/62c5b1997d06674fc7191d3e01e324d3eda4d65ac9cc4e78329fa3b5c4fd42a0e1c8722822497a6964eee075255ce21ccf1eec2d83f92ef3f06653af4d0ee28e
  languageName: node
  linkType: hard

"globby@npm:^10.0.1":
  version: 10.0.2
  resolution: "globby@npm:10.0.2"
  dependencies:
    "@types/glob": "npm:^7.1.1"
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.0.3"
    glob: "npm:^7.1.3"
    ignore: "npm:^5.1.1"
    merge2: "npm:^1.2.3"
    slash: "npm:^3.0.0"
  checksum: 10/6974752014f0914b112957b4364b760af5f2fda4033ff29bedb830bbe278ff4c13ba64681741f3e62b1f12ea0f2d64bf02ac28534f9cbea4b90ed7e9cd6e954f
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10/288e95e310227bbe037076ea81b7c2598ccbc3122d87abc6dab39e1eec309aa14f0e366a98cdc45237ffcfcbad3db597778c0068217dcb1950fef6249104e1b1
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"got@npm:^12.1.0":
  version: 12.6.1
  resolution: "got@npm:12.6.1"
  dependencies:
    "@sindresorhus/is": "npm:^5.2.0"
    "@szmarczak/http-timer": "npm:^5.0.1"
    cacheable-lookup: "npm:^7.0.0"
    cacheable-request: "npm:^10.2.8"
    decompress-response: "npm:^6.0.0"
    form-data-encoder: "npm:^2.1.2"
    get-stream: "npm:^6.0.1"
    http2-wrapper: "npm:^2.1.10"
    lowercase-keys: "npm:^3.0.0"
    p-cancelable: "npm:^3.0.0"
    responselike: "npm:^3.0.0"
  checksum: 10/6c22f1449f4574d79a38e0eba0b753ce2f9030d61838a1ae1e25d3ff5b0db7916aa21023ac369c67d39d17f87bba9283a0b0cb88590de77926c968630aacae75
  languageName: node
  linkType: hard

"graceful-fs@npm:4.2.10":
  version: 4.2.10
  resolution: "graceful-fs@npm:4.2.10"
  checksum: 10/0c83c52b62c68a944dcfb9d66b0f9f10f7d6e3d081e8067b9bfdc9e5f3a8896584d576036f82915773189eec1eba599397fc620e75c03c0610fb3d67c6713c1a
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10/6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"handlebars@npm:^4.0.1":
  version: 4.7.8
  resolution: "handlebars@npm:4.7.8"
  dependencies:
    minimist: "npm:^1.2.5"
    neo-async: "npm:^2.6.2"
    source-map: "npm:^0.6.1"
    uglify-js: "npm:^3.1.4"
    wordwrap: "npm:^1.0.0"
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 10/bd528f4dd150adf67f3f857118ef0fa43ff79a153b1d943fa0a770f2599e38b25a7a0dbac1a3611a4ec86970fd2325a81310fb788b5c892308c9f8743bd02e11
  languageName: node
  linkType: hard

"hardhat-contract-sizer@npm:2.10.0":
  version: 2.10.0
  resolution: "hardhat-contract-sizer@npm:2.10.0"
  dependencies:
    chalk: "npm:^4.0.0"
    cli-table3: "npm:^0.6.0"
    strip-ansi: "npm:^6.0.0"
  peerDependencies:
    hardhat: ^2.0.0
  checksum: 10/2b3011fe5333c2f1dbcfa6c73dcb1c61da9e2e045775c24bb773fe5f3ac14e9923907fef0e61fc2897e82a61997ce74e73baadb7f69dfdeccc86ae878cf67792
  languageName: node
  linkType: hard

"hardhat-deploy-ethers@npm:^0.4.2":
  version: 0.4.2
  resolution: "hardhat-deploy-ethers@npm:0.4.2"
  peerDependencies:
    "@nomicfoundation/hardhat-ethers": ^3.0.2
    hardhat: ^2.16.0
    hardhat-deploy: ^0.12.0
  checksum: 10/22efb24ba44ae3c1d20c09781cf7ec72b4dfd6f069eb00ce1a461fe6cf8523260050d6572d26ad718279161b74b7205e4307aeb20e45f6cbbbdccaf7b3b8c10b
  languageName: node
  linkType: hard

"hardhat-deploy@npm:^0.12.4":
  version: 0.12.4
  resolution: "hardhat-deploy@npm:0.12.4"
  dependencies:
    "@ethersproject/abi": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/contracts": "npm:^5.7.0"
    "@ethersproject/providers": "npm:^5.7.2"
    "@ethersproject/solidity": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/wallet": "npm:^5.7.0"
    "@types/qs": "npm:^6.9.7"
    axios: "npm:^0.21.1"
    chalk: "npm:^4.1.2"
    chokidar: "npm:^3.5.2"
    debug: "npm:^4.3.2"
    enquirer: "npm:^2.3.6"
    ethers: "npm:^5.7.0"
    form-data: "npm:^4.0.0"
    fs-extra: "npm:^10.0.0"
    match-all: "npm:^1.2.6"
    murmur-128: "npm:^0.2.1"
    qs: "npm:^6.9.4"
    zksync-ethers: "npm:^5.0.0"
  checksum: 10/127feddc4f95eaa530e7fe77021f7634e23f7f182a8a2e6d51ef5c7254037b05862c54aaa79dd7e07cac627dbae6bff68f8439851ca048ccc571110704998f9d
  languageName: node
  linkType: hard

"hardhat-gas-reporter@npm:2.2.1":
  version: 2.2.1
  resolution: "hardhat-gas-reporter@npm:2.2.1"
  dependencies:
    "@ethersproject/abi": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/units": "npm:^5.7.0"
    "@solidity-parser/parser": "npm:^0.18.0"
    axios: "npm:^1.6.7"
    brotli-wasm: "npm:^2.0.1"
    chalk: "npm:4.1.2"
    cli-table3: "npm:^0.6.3"
    ethereum-cryptography: "npm:^2.1.3"
    glob: "npm:^10.3.10"
    jsonschema: "npm:^1.4.1"
    lodash: "npm:^4.17.21"
    markdown-table: "npm:2.0.0"
    sha1: "npm:^1.1.1"
    viem: "npm:2.7.14"
  peerDependencies:
    hardhat: ^2.16.0
  checksum: 10/af8bd86400035ab1ed574c5f782e7ee408fff75ea6cfcd56ab87bf80c2e911e9fbe943b528ad17844abad8ff8f63daa24aae391ccbf3e07a9970be66f949bd3d
  languageName: node
  linkType: hard

"hardhat-preprocessor@npm:^0.1.5":
  version: 0.1.5
  resolution: "hardhat-preprocessor@npm:0.1.5"
  dependencies:
    murmur-128: "npm:^0.2.1"
  peerDependencies:
    hardhat: ^2.0.5
  checksum: 10/67f0894a777b0f0e0e8fd8a13e2963d8762d5d93cf2b76319fd8cb00d2676b228c4ba5c8ff82c855256731b21a4f85270fb1d3c49859ebb3b37beeded2a22bbc
  languageName: node
  linkType: hard

"hardhat-test-suite-generator@npm:^2.0.0":
  version: 2.0.0
  resolution: "hardhat-test-suite-generator@npm:2.0.0"
  dependencies:
    "@typechain/hardhat": "npm:^8.0.0"
    "@types/prettier": "npm:^2.1.1"
    fs-extra: "npm:^11.1.1"
    hardhat: "npm:^2.16.1"
    lodash: "npm:^4.17.15"
    mkdirp: "npm:^3.0.1"
    prettier: "npm:^2.3.1"
    typechain: "npm:^8.2.0"
  peerDependencies:
    "@nomicfoundation/hardhat-chai-matchers": 2.x
    "@nomicfoundation/hardhat-network-helpers": 1.x
    "@nomicfoundation/hardhat-toolbox": 3.x
    "@typechain/hardhat": 8.x
    hardhat: 2.x
    lodash: 4.x
    typechain: 8.x
    typescript: ">=4.3.0"
  checksum: 10/4828f5248b723da3f763e259c432db63f527c4c8fed8368fe943704371177f002c201b5bb4a547489b2b59b1a47c5e1e03d1a097becc1727353ae6febc4745aa
  languageName: node
  linkType: hard

"hardhat@npm:2.22.10":
  version: 2.22.10
  resolution: "hardhat@npm:2.22.10"
  dependencies:
    "@ethersproject/abi": "npm:^5.1.2"
    "@metamask/eth-sig-util": "npm:^4.0.0"
    "@nomicfoundation/edr": "npm:^0.5.2"
    "@nomicfoundation/ethereumjs-common": "npm:4.0.4"
    "@nomicfoundation/ethereumjs-tx": "npm:5.0.4"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.4"
    "@nomicfoundation/solidity-analyzer": "npm:^0.1.0"
    "@sentry/node": "npm:^5.18.1"
    "@types/bn.js": "npm:^5.1.0"
    "@types/lru-cache": "npm:^5.1.0"
    adm-zip: "npm:^0.4.16"
    aggregate-error: "npm:^3.0.0"
    ansi-escapes: "npm:^4.3.0"
    boxen: "npm:^5.1.2"
    chalk: "npm:^2.4.2"
    chokidar: "npm:^3.4.0"
    ci-info: "npm:^2.0.0"
    debug: "npm:^4.1.1"
    enquirer: "npm:^2.3.0"
    env-paths: "npm:^2.2.0"
    ethereum-cryptography: "npm:^1.0.3"
    ethereumjs-abi: "npm:^0.6.8"
    find-up: "npm:^2.1.0"
    fp-ts: "npm:1.19.3"
    fs-extra: "npm:^7.0.1"
    glob: "npm:7.2.0"
    immutable: "npm:^4.0.0-rc.12"
    io-ts: "npm:1.10.4"
    keccak: "npm:^3.0.2"
    lodash: "npm:^4.17.11"
    mnemonist: "npm:^0.38.0"
    mocha: "npm:^10.0.0"
    p-map: "npm:^4.0.0"
    raw-body: "npm:^2.4.1"
    resolve: "npm:1.17.0"
    semver: "npm:^6.3.0"
    solc: "npm:0.8.26"
    source-map-support: "npm:^0.5.13"
    stacktrace-parser: "npm:^0.1.10"
    tsort: "npm:0.0.1"
    undici: "npm:^5.14.0"
    uuid: "npm:^8.3.2"
    ws: "npm:^7.4.6"
  peerDependencies:
    ts-node: "*"
    typescript: "*"
  peerDependenciesMeta:
    ts-node:
      optional: true
    typescript:
      optional: true
  bin:
    hardhat: internal/cli/bootstrap.js
  checksum: 10/fe16adc72b2e03ce18dd87f2b19407cfe9378884b7062299c214c049368ed089891eb115c17f7d7e44a4e1bca9a8876aa6384cea3e9d401d5379535f706dbbf3
  languageName: node
  linkType: hard

"hardhat@npm:^2.16.1":
  version: 2.24.2
  resolution: "hardhat@npm:2.24.2"
  dependencies:
    "@ethereumjs/util": "npm:^9.1.0"
    "@ethersproject/abi": "npm:^5.1.2"
    "@nomicfoundation/edr": "npm:^0.11.0"
    "@nomicfoundation/solidity-analyzer": "npm:^0.1.0"
    "@sentry/node": "npm:^5.18.1"
    "@types/bn.js": "npm:^5.1.0"
    "@types/lru-cache": "npm:^5.1.0"
    adm-zip: "npm:^0.4.16"
    aggregate-error: "npm:^3.0.0"
    ansi-escapes: "npm:^4.3.0"
    boxen: "npm:^5.1.2"
    chokidar: "npm:^4.0.0"
    ci-info: "npm:^2.0.0"
    debug: "npm:^4.1.1"
    enquirer: "npm:^2.3.0"
    env-paths: "npm:^2.2.0"
    ethereum-cryptography: "npm:^1.0.3"
    find-up: "npm:^5.0.0"
    fp-ts: "npm:1.19.3"
    fs-extra: "npm:^7.0.1"
    immutable: "npm:^4.0.0-rc.12"
    io-ts: "npm:1.10.4"
    json-stream-stringify: "npm:^3.1.4"
    keccak: "npm:^3.0.2"
    lodash: "npm:^4.17.11"
    micro-eth-signer: "npm:^0.14.0"
    mnemonist: "npm:^0.38.0"
    mocha: "npm:^10.0.0"
    p-map: "npm:^4.0.0"
    picocolors: "npm:^1.1.0"
    raw-body: "npm:^2.4.1"
    resolve: "npm:1.17.0"
    semver: "npm:^6.3.0"
    solc: "npm:0.8.26"
    source-map-support: "npm:^0.5.13"
    stacktrace-parser: "npm:^0.1.10"
    tinyglobby: "npm:^0.2.6"
    tsort: "npm:0.0.1"
    undici: "npm:^5.14.0"
    uuid: "npm:^8.3.2"
    ws: "npm:^7.4.6"
  peerDependencies:
    ts-node: "*"
    typescript: "*"
  peerDependenciesMeta:
    ts-node:
      optional: true
    typescript:
      optional: true
  bin:
    hardhat: internal/cli/bootstrap.js
  checksum: 10/b506f3db755da032fde6167eaf4641e9792dcc1cf1bc36fc11e6ab4f6648f43b73bf6c741940c9f7c4b4df3e0298fed4dce43a110d7f961b069c0ed1e7b9f826
  languageName: node
  linkType: hard

"has-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-flag@npm:1.0.0"
  checksum: 10/ce3f8ae978e70f16e4bbe17d3f0f6d6c0a3dd3b62a23f97c91d0fda9ed8e305e13baf95cc5bee4463b9f25ac9f5255de113165c5fb285e01b8065b2ac079b301
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10/4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0":
  version: 3.1.0
  resolution: "hash-base@npm:3.1.0"
  dependencies:
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.6.0"
    safe-buffer: "npm:^5.2.0"
  checksum: 10/26b7e97ac3de13cb23fc3145e7e3450b0530274a9562144fc2bf5c1e2983afd0e09ed7cc3b20974ba66039fad316db463da80eb452e7373e780cbee9a0d2f2dc
  languageName: node
  linkType: hard

"hash.js@npm:1.1.7, hash.js@npm:^1.0.0, hash.js@npm:^1.0.3, hash.js@npm:^1.1.7":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: "npm:^2.0.3"
    minimalistic-assert: "npm:^1.0.1"
  checksum: 10/0c89ee4006606a40f92df5cc3c263342e7fea68110f3e9ef032bd2083650430505db01b6b7926953489517d4027535e4fdc7f970412893d3031c361d3ec8f4b3
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10/d09b2243da4e23f53336e8de3093e5c43d2c39f8d0d18817abfa32ce3e9355391b2edb4bb5edc376aea5d4b0b59d6a0482aab4c52bc02ef95751e4b818e847f1
  languageName: node
  linkType: hard

"heap@npm:>= 0.2.0":
  version: 0.2.7
  resolution: "heap@npm:0.2.7"
  checksum: 10/6374f6510af79bf47f2cfcee265bf608e6ed2b2694875974d1cb5654ddc98af05347dcf3a42ee9a7de318b576022d6f4d00fe06fa65a4a65c4c60638375eabfe
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: "npm:^1.0.3"
    minimalistic-assert: "npm:^1.0.0"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10/0298a1445b8029a69b713d918ecaa84a1d9f614f5857e0c6e1ca517abfa1357216987b2ee08cc6cc73ba82a6c6ddf2ff11b9717a653530ef03be599d4699b836
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10/4efd2dfcfeea9d5e88c84af450b9980be8a43c2c8179508b1c57c7b4421c855f3e8efe92fa53e0b3f4a43c85824ada930eabbc306d1b3beab750b6dcc5187693
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10/0e7f76ee8ff8a33e58a3281a469815b893c41357378f408be8f6d4aa7d1efafb0da064625518e7078381b6a92325949b119dc38fcb30bdbc4e3a35f78c44c439
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"http2-wrapper@npm:^2.1.10":
  version: 2.2.1
  resolution: "http2-wrapper@npm:2.2.1"
  dependencies:
    quick-lru: "npm:^5.1.1"
    resolve-alpn: "npm:^1.2.0"
  checksum: 10/e7a5ac6548318e83fc0399cd832cdff6bbf902b165d211cad47a56ee732922e0aa1107246dd884b12532a1c4649d27c4d44f2480911c65202e93c90bde8fa29d
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10/f0dce7bdcac5e8eaa0be3c7368bb8836ed010fb5b6349ffb412b172a203efe8f807d9a6681319105ea1b6901e1972c7b5ea899672a7b9aad58309f766dcbe0df
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10/6d3a2dac6e5d1fb126d25645c25c3a1209f70cceecc68b8ef51ae0da3cdc078c151fade7524a30b12a3094926336831fca09c666ef55b37e2c69638b5d6bd2e3
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"ignore@npm:^5.1.1, ignore@npm:^5.2.0, ignore@npm:^5.2.4":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10/cceb6a457000f8f6a50e1196429750d782afce5680dd878aa4221bd79972d68b3a55b4b1458fc682be978f4d3c6a249046aa0880637367216444ab7b014cfc98
  languageName: node
  linkType: hard

"immer@npm:10.0.2":
  version: 10.0.2
  resolution: "immer@npm:10.0.2"
  checksum: 10/5fcddbbc036428bb3db1af66d6f6c3aaf9dfb21ab3e476894f45e3b60e35fb64af67ffab9e626770ab0154d5ca83895038a0af7c25513144e19cba1ab19ec4ef
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0-rc.12":
  version: 4.3.7
  resolution: "immutable@npm:4.3.7"
  checksum: 10/37d963c5050f03ae5f3714ba7a43d469aa482051087f4c65d673d1501c309ea231d87480c792e19fa85e2eaf965f76af5d0aa92726505f3cfe4af91619dfb80b
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10/a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imul@npm:^1.0.0":
  version: 1.0.1
  resolution: "imul@npm:1.0.1"
  checksum: 10/6c2af3d5f09e2135e14d565a2c108412b825b221eb2c881f9130467f2adccf7ae201773ae8bcf1be169e2d090567a1fdfa9cf20d3b7da7b9cecb95b920ff3e52
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10/cd3f5cbc9ca2d624c6a1f53f12e6b341659aba0e2d3254ae2b4464aaea8b4294cdb09616abbc59458f980531f2429784ed6a420d48d245bcad0811980c9efae9
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10/d2ebd65441a38c8336c223d1b80b921b9fa737e37ea466fd7e253cb000c64ae1f17fa59e68130ef5bda92cfd8d36b83d37dab0eb0a4558bcfec8e8cdfd2dcb67
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10/cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"ini@npm:^1.3.4, ini@npm:^1.3.5, ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10/314ae176e8d4deb3def56106da8002b462221c174ddb7ce0c49ee72c8cd1f9044f7b10cc555a7d8850982c3b9ca96fc212122749f5234bc2b6fb05fb942ed566
  languageName: node
  linkType: hard

"interpret@npm:^1.0.0":
  version: 1.4.0
  resolution: "interpret@npm:1.4.0"
  checksum: 10/5beec568d3f60543d0f61f2c5969d44dffcb1a372fe5abcdb8013968114d4e4aaac06bc971a4c9f5bd52d150881d8ebad72a8c60686b1361f5f0522f39c0e1a3
  languageName: node
  linkType: hard

"io-ts@npm:1.10.4":
  version: 1.10.4
  resolution: "io-ts@npm:1.10.4"
  dependencies:
    fp-ts: "npm:^1.0.0"
  checksum: 10/d68cb0928b37485cf631c923628dd189784d3dbbcb2d681d86f5c64b9b0321aa33bd2ff271381ac54a279aec5935ff7a743264c858b5172e83b6a9f0cbafc7d1
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10/73ced84fa35e59e2c57da2d01e12cd01479f381d7f122ce41dcbb713f09dbfc651315832cd2bf8accba7681a69e4d6f1e03941d94dd10040d415086360e7005e
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10/078e51b4f956c2c5fd2b26bb2672c3ccf7e1faff38e0ebdba45612265f4e3d9fc3127a1fa8370bbf09eab61339203c3d3b7af5662cbf8be4030f8fac37745b0e
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10/452b2c2fb7f889cbbf7e54609ef92cf6c24637c568acc7e63d166812a0fb365ae8a504c333a29add8bdb1686704068caa7f4e4b639b650dde4f00a038b8941fb
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-hex-prefixed@npm:1.0.0":
  version: 1.0.0
  resolution: "is-hex-prefixed@npm:1.0.0"
  checksum: 10/5ac58e6e528fb029cc43140f6eeb380fad23d0041cc23154b87f7c9a1b728bcf05909974e47248fd0b7fcc11ba33cf7e58d64804883056fabd23e2b898be41de
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: 10/abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: 10/cec9100678b0a9fe0248a81743041ed990c2d4c99f893d935545cfbc42876cbe86d207f3b895700c690ad2fa520e568c44afc1605044b535a7820c1d40e38daa
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: 10/a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isows@npm:1.0.3":
  version: 1.0.3
  resolution: "isows@npm:1.0.3"
  peerDependencies:
    ws: "*"
  checksum: 10/9cacd5cf59f67deb51e825580cd445ab1725ecb05a67c704050383fb772856f3cd5e7da8ad08f5a3bd2823680d77d099459d0c6a7037972a74d6429af61af440
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"javascript-natural-sort@npm:0.7.1":
  version: 0.7.1
  resolution: "javascript-natural-sort@npm:0.7.1"
  checksum: 10/7bf6eab67871865d347f09a95aa770f9206c1ab0226bcda6fdd9edec340bf41111a7f82abac30556aa16a21cfa3b2b1ca4a362c8b73dd5ce15220e5d31f49d79
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0, js-sha3@npm:^0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: 10/a49ac6d3a6bfd7091472a28ab82a94c7fb8544cc584ee1906486536ba1cb4073a166f8c7bb2b0565eade23c5b3a7b8f7816231e0309ab5c549b737632377a20c
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10/af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-yaml@npm:3.x":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/9e22d80b4d0105b9899135365f746d47466ed53ef4223c529b3c0f7a39907743fdbd3c4379f94f1106f02755b5e90b2faaf84801a891135544e1ea475d1a1379
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10/d2096abdcdec56969764b40ffc91d4a23408aa2f351b4d1c13f736f25476643238c43fdbaf38a191c26b1b78fd856d965f5d4d0dde7b89459cd94025190cdf13
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10/20bd37a142eca5d1794f354db8f1c9aeb54d85e1f5c247b371de05d23a9751ecd7bd3a9c4fc5298ea6fa09a100dafb4190fa5c98c6610b75952c3487f3ce7967
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10/5f3a99009ed5f2a5a67d06e2f298cc97bc86d462034173308156f15b43a6e850be8511dc204b9b94566305da2947f7d90289657237d210351a39059ff9d666cf
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10/7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10/02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10/12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"json-stream-stringify@npm:^3.1.4":
  version: 3.1.6
  resolution: "json-stream-stringify@npm:3.1.6"
  checksum: 10/d52919465b4a31d7a0b5720ca0e6268f757fc1515486d5c77cfb75f7a9e4b58e13a73a2f811d6d322b9a101750d3961b48a68ee9d9b299ac3846ef2921a62a81
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 10/59169a081e4eeb6f9559ae1f938f656191c000e0512aa6df9f3c8b2437a4ab1823819c6b9fd1818a4e39593ccfd72e9a051fdd3e2d1e340ed913679e888ded8c
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10/1db67b853ff0de3534085d630691d3247de53a2ed1390ba0ddff681ea43e9b3e30ecbdb65c5e9aab49435e44059c23dbd6fee8ee619419ba37465bb0dd7135da
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10/17796f0ab1be8479827d3683433f97ebe0a1c6932c3360fa40348eac36904d69269aab26f8b16da311882d94b42e9208e8b28e490bf926364f3ac9bff134c226
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10/03014769e7dc77d4cf05fa0b534907270b60890085dd5e4d60a382ff09328580651da0b8b4cdf44d91e4c8ae64d91791d965f05707beff000ed494a38b6fec85
  languageName: node
  linkType: hard

"jsonschema@npm:^1.2.4, jsonschema@npm:^1.4.1":
  version: 1.5.0
  resolution: "jsonschema@npm:1.5.0"
  checksum: 10/46bf49b388ba922073bcb3c8d5e90af9d29fc8303dc866fd440182c88d6b4fd2807679fd39cdefb4113156d104ea47da9c0ff4bbcb0032c9fa29461cb1a92182
  languageName: node
  linkType: hard

"keccak@npm:^3.0.0, keccak@npm:^3.0.2":
  version: 3.0.4
  resolution: "keccak@npm:3.0.4"
  dependencies:
    node-addon-api: "npm:^2.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10/45478bb0a57e44d0108646499b8360914b0fbc8b0e088f1076659cb34faaa9eb829c40f6dd9dadb3460bb86cc33153c41fed37fe5ce09465a60e71e78c23fa55
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10/5873d303fb36aad875b7538798867da2ae5c9e328d67194b0162a3659a627d22f742fc9c4ae95cd1704132a24b00cae5041fc00c0f6ef937dc17080dc4dbb962
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10/0c0ecaf00a5c6173d25059c7db2113850b5457016dfa1d0e3ef26da4704fbb186b4938d7611246d86f0ddf1bccf26828daa5877b1f232a65e7373d0122a83e7f
  languageName: node
  linkType: hard

"latest-version@npm:^7.0.0":
  version: 7.0.0
  resolution: "latest-version@npm:7.0.0"
  dependencies:
    package-json: "npm:^8.1.0"
  checksum: 10/1f0deba00d5a34394cce4463c938811f51bbb539b131674f4bb2062c63f2cc3b80bccd56ecade3bd5932d04a34cf0a5a8a2ccc4ec9e5e6b285a9a7b3e27d0d66
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10/2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"levn@npm:~0.3.0":
  version: 0.3.0
  resolution: "levn@npm:0.3.0"
  dependencies:
    prelude-ls: "npm:~1.1.2"
    type-check: "npm:~0.3.2"
  checksum: 10/e1c3e75b5c430d9aa4c32c83c8a611e4ca53608ca78e3ea3bf6bbd9d017e4776d05d86e27df7901baebd3afa732abede9f26f715b8c1be19e95505c7a3a7b589
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10/0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"locate-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "locate-path@npm:2.0.0"
  dependencies:
    p-locate: "npm:^2.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 10/02d581edbbbb0fa292e28d96b7de36b5b62c2fa8b5a7e82638ebb33afa74284acf022d3b1e9ae10e3ffb7658fbc49163fcd5e76e7d1baaa7801c3e05a81da755
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10/72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: 10/c301cc379310441dc73cd6cebeb91fb254bea74e6ad3027f9346fc43b4174385153df420ffa521654e502fd34c40ef69ca4e7d40ee7129a99e06f306032bfc65
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 10/957ed243f84ba6791d4992d5c222ffffca339a3b79dbe81d2eaf0c90504160b500641c5a0f56e27630030b18b8e971ea10b44f928a977d5ced3c8948841b555f
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: 10/82fc58a83a1555f8df34ca9a2cd300995ff94018ac12cc47c349655f0ae1d4d92ba346db4c19bbfc90510764e0c00ddcc985a358bdcd4b3b965abf8f2a48a214
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash.truncate@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.truncate@npm:4.4.2"
  checksum: 10/7a495616121449e5d2288c606b1025d42ab9979e8c93ba885e5c5802ffd4f1ebad4428c793ccc12f73e73237e85a9f5b67dd6415757546fbd5a4653ba83e25ac
  languageName: node
  linkType: hard

"lodash@npm:4.17.21, lodash@npm:^4.17.11, lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: "npm:^4.1.0"
    is-unicode-supported: "npm:^0.1.0"
  checksum: 10/fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"loupe@npm:^2.3.6":
  version: 2.3.7
  resolution: "loupe@npm:2.3.7"
  dependencies:
    get-func-name: "npm:^2.0.1"
  checksum: 10/635c8f0914c2ce7ecfe4e239fbaf0ce1d2c00e4246fafcc4ed000bfdb1b8f89d05db1a220054175cca631ebf3894872a26fffba0124477fcb562f78762848fb1
  languageName: node
  linkType: hard

"lowercase-keys@npm:^3.0.0":
  version: 3.0.0
  resolution: "lowercase-keys@npm:3.0.0"
  checksum: 10/67a3f81409af969bc0c4ca0e76cd7d16adb1e25aa1c197229587eaf8671275c8c067cd421795dbca4c81be0098e4c426a086a05e30de8a9c587b7a13c0c7ccc5
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"lru_map@npm:^0.3.3":
  version: 0.3.3
  resolution: "lru_map@npm:0.3.3"
  checksum: 10/50f6597924a7763ab0b31192e5e9965f08ca64a0044254138e74a65aecab95047d540f73739cff489866f4310e0202c11c10fdf18b10b236472160baaa68bbb1
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: 10/b86e5e0e25f7f777b77fabd8e2cbf15737972869d852a22b7e73c17623928fccb826d8e46b9951501d3f20e51ad74ba8c59ed584f610526a48f8ccf88aaec402
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"markdown-table@npm:2.0.0":
  version: 2.0.0
  resolution: "markdown-table@npm:2.0.0"
  dependencies:
    repeat-string: "npm:^1.0.0"
  checksum: 10/8018cd1a1733ffda916a0548438e50f3d21b6c6b71fb23696b33c0b5922a8cc46035eb4b204a59c6054f063076f934461ae094599656a63f87c1c3a80bd3c229
  languageName: node
  linkType: hard

"match-all@npm:^1.2.6":
  version: 1.2.7
  resolution: "match-all@npm:1.2.7"
  checksum: 10/2ef409a1c1172b4aab79fe750820b6cb07aea3c1364d545127784eb8521230776e2b344b5fce061e516bfdce875cd85a2468cd91e87048b33e74f5c3a699a3e3
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 10/098494d885684bcc4f92294b18ba61b7bd353c23147fbc4688c75b45cb8590f5a95fd4584d742415dcc52487f7a1ef6ea611cfa1543b0dc4492fe026357f3f0c
  languageName: node
  linkType: hard

"memorystream@npm:^0.3.1":
  version: 0.3.1
  resolution: "memorystream@npm:0.3.1"
  checksum: 10/2e34a1e35e6eb2e342f788f75f96c16f115b81ff6dd39e6c2f48c78b464dbf5b1a4c6ebfae4c573bd0f8dbe8c57d72bb357c60523be184655260d25855c03902
  languageName: node
  linkType: hard

"merge2@npm:^1.2.3, merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10/7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micro-eth-signer@npm:^0.14.0":
  version: 0.14.0
  resolution: "micro-eth-signer@npm:0.14.0"
  dependencies:
    "@noble/curves": "npm:~1.8.1"
    "@noble/hashes": "npm:~1.7.1"
    micro-packed: "npm:~0.7.2"
  checksum: 10/de9fb0262253c22f280dc6fae18b61950ac2bf0e086d9ca60e3dd150f64b922ca9073e7566ebfc71be773507f3979ebdccee8bc9bb1162697b7e0eeec1dbd691
  languageName: node
  linkType: hard

"micro-ftch@npm:^0.3.1":
  version: 0.3.1
  resolution: "micro-ftch@npm:0.3.1"
  checksum: 10/a7ab07d25e28ec4ae492ce4542ea9b06eee85538742b3b1263b247366ee8872f2c5ce9c8651138b2f1d22c8212f691a7b8b5384fe86ead5aff1852e211f1c035
  languageName: node
  linkType: hard

"micro-packed@npm:~0.7.2":
  version: 0.7.3
  resolution: "micro-packed@npm:0.7.3"
  dependencies:
    "@scure/base": "npm:~1.2.5"
  checksum: 10/956c89cd0753e82566e13f67406e5983ae9cb7bcbe539238c5e0dcc605974f91d454b819dd3cf63acec7d67e63ef17afde45b451eaa00a38de31c6024a75cee5
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10/6bf2a01672e7965eb9941d1f02044fad2bd12486b5553dc1116ff24c09a8723157601dc992e74c911d896175918448762df3b3fd0a6b61037dd1a9766ddfbf58
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10/54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10/89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 10/7e719047612411fe071332a7498cf0448bbe43c485c0d780046c76633a771b223ff49bd00267be122cedebb897037fdb527df72335d0d0f74724604ca70b37ad
  languageName: node
  linkType: hard

"mimic-response@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-response@npm:4.0.0"
  checksum: 10/33b804cc961efe206efdb1fca6a22540decdcfce6c14eb5c0c50e5ae9022267ab22ce8f5568b1f7247ba67500fe20d523d81e0e9f009b321ccd9d472e78d1850
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: 10/cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 10/6e8a0422b30039406efd4c440829ea8f988845db02a3299f372fceba56ffa94994a9c0f2fd70c17f9969eedfbd72f34b5070ead9656a34d3f71c0bd72583a0ed
  languageName: node
  linkType: hard

"minimatch@npm:2 || 3, minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:9.0.3":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/c81b47d28153e77521877649f4bab48348d10938df9e8147a58111fe00ef89559a2938de9f6632910c4f7bf7bb5cd81191a546167e58d357f0cfb1e18cecc1c5
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1, minimatch@npm:^5.1.6":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/126b36485b821daf96d33b5c821dac600cc1ab36c87e7a532594f9b1652b1fa89a1eebcaad4dff17c764dce1a7ac1531327f190fed5f97d8f6e5f889c116c429
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.3, minimist@npm:^1.2.5, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10/908491b6cc15a6c440ba5b22780a0ba89b9810e1aea684e253e43c4e3b8d56ec1dcdd7ea96dde119c29df59c936cde16062159eae4225c691e19c70b432b6e6f
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/7ddfebdbb87d9866e7b5f7eead5a9e3d9d507992af932a11d275551f60006cf7d9178e66d586dbb910894f3e3458d27c0ddf93c76e94d49d0a54a541ddc1263d
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10/c075bed1594f68dcc8c35122333520112daefd4d070e5d0a228bd4cf5580e9eed3981b96c0ae1d62488e204e80fd27b2b9d0068ca9a5ef3993e9565faf63ca41
  languageName: node
  linkType: hard

"mkdirp@npm:0.5.x, mkdirp@npm:^0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10/0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10/d71b8dcd4b5af2fe13ecf3bd24070263489404fe216488c5ba7e38ece1f54daf219e72a833a3a2dc404331e870e9f44963a33399589490956bff003a3404d3b2
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"mnemonist@npm:^0.38.0":
  version: 0.38.5
  resolution: "mnemonist@npm:0.38.5"
  dependencies:
    obliterator: "npm:^2.0.0"
  checksum: 10/2df34862567376acb8c2411d546ba9f109229acb2b7fe7593df6fe62194d98f124cf7ff7b2d6f457a3f0410d4d8b44389022ac853d5e5448a2603c4b12f733bf
  languageName: node
  linkType: hard

"mocha@npm:^10.0.0, mocha@npm:^10.2.0, mocha@npm:^10.7.3":
  version: 10.8.2
  resolution: "mocha@npm:10.8.2"
  dependencies:
    ansi-colors: "npm:^4.1.3"
    browser-stdout: "npm:^1.3.1"
    chokidar: "npm:^3.5.3"
    debug: "npm:^4.3.5"
    diff: "npm:^5.2.0"
    escape-string-regexp: "npm:^4.0.0"
    find-up: "npm:^5.0.0"
    glob: "npm:^8.1.0"
    he: "npm:^1.2.0"
    js-yaml: "npm:^4.1.0"
    log-symbols: "npm:^4.1.0"
    minimatch: "npm:^5.1.6"
    ms: "npm:^2.1.3"
    serialize-javascript: "npm:^6.0.2"
    strip-json-comments: "npm:^3.1.1"
    supports-color: "npm:^8.1.1"
    workerpool: "npm:^6.5.1"
    yargs: "npm:^16.2.0"
    yargs-parser: "npm:^20.2.9"
    yargs-unparser: "npm:^2.0.0"
  bin:
    _mocha: bin/_mocha
    mocha: bin/mocha.js
  checksum: 10/903bbffcb195ef9d36b27db54e3462c5486de1397289e0953735b3530397a139336c452bcf5188c663496c660d2285bbb6c7213290d36d536ad647b6145cb917
  languageName: node
  linkType: hard

"moonbags-contracts-hyperevm@workspace:.":
  version: 0.0.0-use.local
  resolution: "moonbags-contracts-hyperevm@workspace:."
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@nomicfoundation/hardhat-chai-matchers": "npm:^2.0.7"
    "@nomicfoundation/hardhat-ethers": "npm:^3.0.8"
    "@nomicfoundation/hardhat-foundry": "npm:^1.1.2"
    "@nomicfoundation/hardhat-ignition": "npm:^0.15.5"
    "@nomicfoundation/hardhat-ignition-ethers": "npm:^0.15.5"
    "@nomicfoundation/hardhat-network-helpers": "npm:^1.0.11"
    "@nomicfoundation/hardhat-toolbox": "npm:^5.0.0"
    "@nomicfoundation/hardhat-verify": "npm:^2.0.10"
    "@nomicfoundation/ignition-core": "npm:^0.15.5"
    "@nomiclabs/hardhat-ethers": "npm:^2.2.3"
    "@openzeppelin/contracts": "npm:^5.3.0"
    "@openzeppelin/contracts-upgradeable": "npm:^5.3.0"
    "@primitivefi/hardhat-dodoc": "npm:^0.2.3"
    "@trivago/prettier-plugin-sort-imports": "npm:^4.3.0"
    "@typechain/ethers-v6": "npm:^0.5.1"
    "@typechain/hardhat": "npm:^9.1.0"
    "@types/chai": "npm:^4.3.19"
    "@types/fs-extra": "npm:^11.0.4"
    "@types/mocha": "npm:^10.0.8"
    "@types/node": "npm:^22.5.5"
    "@typescript-eslint/eslint-plugin": "npm:^6.21.0"
    "@typescript-eslint/parser": "npm:^6.21.0"
    "@uniswap/v3-core": "npm:^1.0.1"
    "@uniswap/v3-periphery": "npm:^1.4.4"
    bignumber.js: "npm:^9.3.0"
    chai: "npm:4.5.0"
    chalk: "npm:4.1.2"
    cross-env: "npm:^7.0.3"
    dotenv: "npm:^16.4.5"
    ds-test: "github:dapphub/ds-test#e282159d5170298eb2455a6c05280ab5a73a4ef0"
    eslint: "npm:^8.57.1"
    eslint-config-prettier: "npm:^9.1.0"
    ethers: "npm:6.13.2"
    evm-bn: "npm:^1.1.2"
    forge-std: "github:foundry-rs/forge-std#v1.9.2"
    fs-extra: "npm:^11.2.0"
    hardhat: "npm:2.22.10"
    hardhat-contract-sizer: "npm:2.10.0"
    hardhat-deploy: "npm:^0.12.4"
    hardhat-deploy-ethers: "npm:^0.4.2"
    hardhat-gas-reporter: "npm:2.2.1"
    hardhat-preprocessor: "npm:^0.1.5"
    hardhat-test-suite-generator: "npm:^2.0.0"
    lodash: "npm:^4.17.21"
    mocha: "npm:^10.7.3"
    prettier: "npm:^3.3.3"
    shx: "npm:0.3.4"
    solhint: "npm:^5.0.3"
    solidity-coverage: "npm:0.8.13"
    ts-generator: "npm:^0.1.1"
    ts-node: "npm:^10.9.2"
    typechain: "npm:^8.3.2"
    typescript: "npm:^5.6.2"
  languageName: unknown
  linkType: soft

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"murmur-128@npm:^0.2.1":
  version: 0.2.1
  resolution: "murmur-128@npm:0.2.1"
  dependencies:
    encode-utf8: "npm:^1.0.2"
    fmix: "npm:^0.1.0"
    imul: "npm:^1.0.0"
  checksum: 10/0ec68c6d2176f1361699585ea54562ed3fe7a9260841cd58e39fdab2e2da5bc856ee9c9df3c5ae02d1cf9cd14432c24c8b70f80e64a69ab3b3484808539b5e83
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"ndjson@npm:2.0.0":
  version: 2.0.0
  resolution: "ndjson@npm:2.0.0"
  dependencies:
    json-stringify-safe: "npm:^5.0.1"
    minimist: "npm:^1.2.5"
    readable-stream: "npm:^3.6.0"
    split2: "npm:^3.0.0"
    through2: "npm:^4.0.0"
  bin:
    ndjson: cli.js
  checksum: 10/f847a51a2275b8a6a1bfdb24095183836b71c3085670161678c9922bc59644f04e53ced385e549a5565fdc44c28e206bd3f2199d12525028f843a86b680c4446
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10/1a7948fea86f2b33ec766bc899c88796a51ba76a4afc9026764aedc6e7cde692a09067031e4a1bf6db4f978ccd99e7f5b6c03fe47ad9865c3d4f99050d67e002
  languageName: node
  linkType: hard

"node-addon-api@npm:^2.0.0":
  version: 2.0.2
  resolution: "node-addon-api@npm:2.0.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/e4ce4daac5b2fefa6b94491b86979a9c12d9cceba571d2c6df1eb5859f9da68e5dc198f128798e1785a88aafee6e11f4992dcccd4bf86bec90973927d158bd60
  languageName: node
  linkType: hard

"node-addon-api@npm:^5.0.0":
  version: 5.1.0
  resolution: "node-addon-api@npm:5.1.0"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/595f59ffb4630564f587c502119cbd980d302e482781021f3b479f5fc7e41cf8f2f7280fdc2795f32d148e4f3259bd15043c52d4a3442796aa6f1ae97b959636
  languageName: node
  linkType: hard

"node-emoji@npm:^1.10.0":
  version: 1.11.0
  resolution: "node-emoji@npm:1.11.0"
  dependencies:
    lodash: "npm:^4.17.21"
  checksum: 10/1d7ae9bcb0f23d7cdfcac5c3a90a6fd6ec584e6f7c70ff073f6122bfbed6c06284da7334092500d24e14162f5c4016e5dcd3355753cbd5b7e60de560a973248d
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.2.0":
  version: 4.8.4
  resolution: "node-gyp-build@npm:4.8.4"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 10/6a7d62289d1afc419fc8fc9bd00aa4e554369e50ca0acbc215cb91446148b75ff7e2a3b53c2c5b2c09a39d416d69f3d3237937860373104b5fe429bf30ad9ac5
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/806fd8e3adc9157e17bf0d4a2c899cf6b98a0bbe9f453f630094ce791866271f6cddcaf2133e6513715d934fcba2014d287c7053d5d7934937b3a34d5a3d84ad
  languageName: node
  linkType: hard

"nofilter@npm:^3.1.0":
  version: 3.1.0
  resolution: "nofilter@npm:3.1.0"
  checksum: 10/f63d87231dfda4b783db17d75b15aac948f78e65f4f1043096ef441147f6667ff74cd4b3f57ada5dbe240be282d3e9838558ac863a66cb04ef25fff7b2b4be4e
  languageName: node
  linkType: hard

"nopt@npm:3.x":
  version: 3.0.6
  resolution: "nopt@npm:3.0.6"
  dependencies:
    abbrev: "npm:1"
  bin:
    nopt: ./bin/nopt.js
  checksum: 10/2f582a44f7a4e495f21b6668008eda47f6e9c50c27efc00494aa67360791c9240da537661371786afc5d5712f353d3debb863a7201b536fe35fb393ceadc8a23
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/26ab456c51a96f02a9e5aa8d1b80ef3219f2070f3f3528a040e32fb735b1e651e17bdf0f1476988d3a46d498f35c65ed662d122f340d38ce4a7e71dd7b20c4bc
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10/88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-url@npm:^8.0.0":
  version: 8.0.1
  resolution: "normalize-url@npm:8.0.1"
  checksum: 10/ae392037584fc5935b663ae4af475351930a1fc39e107956cfac44f42d5127eec2d77d9b7b12ded4696ca78103bafac5b6206a0ea8673c7bffecbe13544fcc5a
  languageName: node
  linkType: hard

"number-to-bn@npm:1.7.0":
  version: 1.7.0
  resolution: "number-to-bn@npm:1.7.0"
  dependencies:
    bn.js: "npm:4.11.6"
    strip-hex-prefix: "npm:1.0.0"
  checksum: 10/702e8f00b6b90abd23f711056005179c3bd5ce3b063c47d468250f63ab3b9b4b82e27bff3b4642a9e71e06c717d5ed359873501746df0a64c3db1fa6d704e704
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10/aa13b1190ad3e366f6c83ad8a16ed37a19ed57d267385aa4bfdccda833d7b90465c057ff6c55d035a6b2e52c1a2295582b294217a0a3a1ae7abdd6877ef781fb
  languageName: node
  linkType: hard

"obliterator@npm:^2.0.0":
  version: 2.0.5
  resolution: "obliterator@npm:2.0.5"
  checksum: 10/3f10254a97bc30702ed9cef19cd338efb5859e3f653d619265086d62f0af86b8894c67faf57e69deb3de18d52c1c08c5f9c753a4125762dbe148478c5560c59e
  languageName: node
  linkType: hard

"once@npm:1.x, once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10/cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"optionator@npm:^0.8.1":
  version: 0.8.3
  resolution: "optionator@npm:0.8.3"
  dependencies:
    deep-is: "npm:~0.1.3"
    fast-levenshtein: "npm:~2.0.6"
    levn: "npm:~0.3.0"
    prelude-ls: "npm:~1.1.2"
    type-check: "npm:~0.3.2"
    word-wrap: "npm:~1.2.3"
  checksum: 10/6fa3c841b520f10aec45563962922215180e8cfbc59fde3ecd4ba2644ad66ca96bd19ad0e853f22fefcb7fc10e7612a5215b412cc66c5588f9a3138b38f6b5ff
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10/a8398559c60aef88d7f353a4f98dcdff6090a4e70f874c827302bf1213d9106a1c4d5fcb68dacb1feb3c30a04c4102f41047aa55d4c576b863d6fc876e001af6
  languageName: node
  linkType: hard

"ordinal@npm:^1.0.3":
  version: 1.0.3
  resolution: "ordinal@npm:1.0.3"
  checksum: 10/6761c5b7606b6c4b0c22b4097dab4fe7ffcddacc49238eedf9c0ced877f5d4e4ad3f4fd43fefa1cc3f167cc54c7149267441b2ae85b81ccf13f45cf4b7947164
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 10/5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"p-cancelable@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-cancelable@npm:3.0.0"
  checksum: 10/a5eab7cf5ac5de83222a014eccdbfde65ecfb22005ee9bc242041f0b4441e07fac7629432c82f48868aa0f8413fe0df6c6067c16f76bf9217cd8dc651923c93d
  languageName: node
  linkType: hard

"p-limit@npm:^1.1.0":
  version: 1.3.0
  resolution: "p-limit@npm:1.3.0"
  dependencies:
    p-try: "npm:^1.0.0"
  checksum: 10/eb9d9bc378d48ab1998d2a2b2962a99eddd3e3726c82d3258ecc1a475f22907968edea4fec2736586d100366a001c6bb449a2abe6cd65e252e9597394f01e789
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10/7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^2.0.0":
  version: 2.0.0
  resolution: "p-locate@npm:2.0.0"
  dependencies:
    p-limit: "npm:^1.1.0"
  checksum: 10/e2dceb9b49b96d5513d90f715780f6f4972f46987dc32a0e18bc6c3fc74a1a5d73ec5f81b1398af5e58b99ea1ad03fd41e9181c01fa81b4af2833958696e3081
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10/1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10/7ba4a2b1e24c05e1fc14bbaea0fc6d85cf005ae7e9c9425d4575550f37e2e584b1af97bcde78eacd7559208f20995988d52881334db16cf77bc1bcf68e48ed7c
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"p-try@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-try@npm:1.0.0"
  checksum: 10/20d9735f57258158df50249f172c77fe800d31e80f11a3413ac9e68ccbe6b11798acb3f48f2df8cea7ba2b56b753ce695a4fe2a2987c3c7691c44226b6d82b6f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"package-json@npm:^8.1.0":
  version: 8.1.1
  resolution: "package-json@npm:8.1.1"
  dependencies:
    got: "npm:^12.1.0"
    registry-auth-token: "npm:^5.0.1"
    registry-url: "npm:^6.0.0"
    semver: "npm:^7.3.7"
  checksum: 10/d97ce9539e1ed4aacaf7c2cb754f16afc10937fa250bd09b4d61181d2e36a30cf8a4cff2f8f831f0826b0ac01a355f26204c7e57ca0e450da6ccec3e34fc889a
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10/6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10/62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 10/96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10/060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.6, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10/49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10/5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pathval@npm:^1.1.1":
  version: 1.1.1
  resolution: "pathval@npm:1.1.1"
  checksum: 10/b50a4751068aa3a5428f5a0b480deecedc6f537666a3630a0c2ae2d5e7c0f4bf0ee77b48404441ec1220bef0c91625e6030b3d3cf5a32ab0d9764018d1d9dbb6
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.0.17":
  version: 3.1.2
  resolution: "pbkdf2@npm:3.1.2"
  dependencies:
    create-hash: "npm:^1.1.2"
    create-hmac: "npm:^1.1.4"
    ripemd160: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 10/40bdf30df1c9bb1ae41ec50c11e480cf0d36484b7c7933bf55e4451d1d0e3f09589df70935c56e7fccc5702779a0d7b842d012be8c08a187b44eb24d55bb9460
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10/ce617b8da36797d09c0baacb96ca8a44460452c89362d7cb8f70ca46b4158ba8bc3606912de7c818eb4a939f7f9015cef3c766ec8a0c6bfc725fdc078e39c717
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 10/8b97cbf9dc6d4c1320cc238a2db0fc67547f9dc77011729ff353faf34f1936ea1a4d7f3c63b2f4980b253be77bcc72ea1e9e76ee3fd53cce2aafb6a8854d07ec
  languageName: node
  linkType: hard

"pluralize@npm:^8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 10/17877fdfdb7ddb3639ce257ad73a7c51a30a966091e40f56ea9f2f545b5727ce548d4928f8cb3ce38e7dc0c5150407d318af6a4ed0ea5265d378473b4c2c61ec
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10/0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"prelude-ls@npm:~1.1.2":
  version: 1.1.2
  resolution: "prelude-ls@npm:1.1.2"
  checksum: 10/946a9f60d3477ca6b7d4c5e8e452ad1b98dc8aaa992cea939a6b926ac16cc4129d7217c79271dc808b5814b1537ad0af37f29a942e2eafbb92cfc5a1c87c38cb
  languageName: node
  linkType: hard

"prettier@npm:^2.1.2, prettier@npm:^2.3.1, prettier@npm:^2.8.3":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 10/00cdb6ab0281f98306cd1847425c24cbaaa48a5ff03633945ab4c701901b8e96ad558eb0777364ffc312f437af9b5a07d0f45346266e8245beaf6247b9c62b24
  languageName: node
  linkType: hard

"prettier@npm:^3.3.3":
  version: 3.5.3
  resolution: "prettier@npm:3.5.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10/7050c08f674d9e49fbd9a4c008291d0715471f64e94cc5e4b01729affce221dfc6875c8de7e66b728c64abc9352eefb7eaae071b5f79d30081be207b53774b78
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"prompts@npm:^2.4.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10/c52536521a4d21eff4f2f2aa4572446cad227464066365a7167e52ccf8d9839c099f9afec1aba0eed3d5a2514b3e79e0b3e7a1dc326b9acde6b75d27ed74b1a9
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 10/9cc3b46d613fa0d637033b225db1bc98e914c3c05864f7adc9bee728192e353125ef2e49f71129a413f6333951756000b0e54f299d921f02d3e9e370cc994100
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10/f0bb4a87cfd18f77bc2fba23ae49c3b378fb35143af16cc478171c623eebe181678f09439707ad80081d340d1593cd54a33a0113f3ccb3f4bc9451488780ee23
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"qs@npm:^6.9.4":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 10/a60e49bbd51c935a8a4759e7505677b122e23bf392d6535b8fc31c1e447acba2c901235ecb192764013cd2781723dc1f61978b5fdd93cc31d7043d31cdc01974
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10/72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: 10/a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10/4efd1ad3d88db77c2d16588dc54c2b52fd2461e70fe5724611f38d283857094fe09040fa2c9776366803c3152cf133171b452ef717592b65631ce5dc3a2bdafc
  languageName: node
  linkType: hard

"raw-body@npm:^2.4.1":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10/863b5171e140546a4d99f349b720abac4410338e23df5e409cfcc3752538c9caf947ce382c89129ba976f71894bd38b5806c774edac35ebf168d02aa1ac11a95
  languageName: node
  linkType: hard

"rc@npm:1.2.8":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 10/5c4d72ae7eec44357171585938c85ce066da8ca79146b5635baf3d55d74584c92575fa4e2c9eac03efbed3b46a0b2e7c30634c012b4b4fa40d654353d3c163eb
  languageName: node
  linkType: hard

"readable-stream@npm:3, readable-stream@npm:^3.0.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10/d9e3e53193adcdb79d8f10f2a1f6989bd4389f5936c6f8b870e77570853561c362bee69feca2bbb7b32368ce96a85504aa4cedf7cf80f36e6a9de30d64244048
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 10/7b817c265940dba90bb9c94d82920d76c3a35ea2d67f9f9d8bd936adcfe02d50c802b14be3dd2e725e002dddbe2cc1c7a0edfb1bc3a365c9dfd5a61e612eea1e
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10/196b30ef6ccf9b6e18c4e1724b7334f72a093d011a99f3b5920470f0b3406a51770867b3e1ae9711f227ef7a7065982f6ee2ce316746b2cb42c88efe44297fe7
  languageName: node
  linkType: hard

"rechoir@npm:^0.6.2":
  version: 0.6.2
  resolution: "rechoir@npm:0.6.2"
  dependencies:
    resolve: "npm:^1.1.6"
  checksum: 10/fe76bf9c21875ac16e235defedd7cbd34f333c02a92546142b7911a0f7c7059d2e16f441fe6fb9ae203f459c05a31b2bcf26202896d89e390eda7514d5d2702b
  languageName: node
  linkType: hard

"recursive-readdir@npm:^2.2.2":
  version: 2.2.3
  resolution: "recursive-readdir@npm:2.2.3"
  dependencies:
    minimatch: "npm:^3.0.5"
  checksum: 10/19298852b0b87810aed5f2c81a73bfaaeb9ade7c9bf363f350fc1443f2cc3df66ecade5e102dfbb153fcd9df20342c301848e11e149e5f78759c1d55aa2c9c39
  languageName: node
  linkType: hard

"reduce-flatten@npm:^2.0.0":
  version: 2.0.0
  resolution: "reduce-flatten@npm:2.0.0"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"registry-auth-token@npm:^5.0.1":
  version: 5.1.0
  resolution: "registry-auth-token@npm:5.1.0"
  dependencies:
    "@pnpm/npm-conf": "npm:^2.1.0"
  checksum: 10/620c897167e2e0e9308b9cdd0288f70d651d9ec554348c39a96d398bb91d444e8cb4b3c0dc1e19d4a8f1c10ade85163baf606e5c09959baa31179bdfb1f7434e
  languageName: node
  linkType: hard

"registry-url@npm:^6.0.0":
  version: 6.0.1
  resolution: "registry-url@npm:6.0.1"
  dependencies:
    rc: "npm:1.2.8"
  checksum: 10/33712aa1b489aab7aba2191c1cdadfdd71f5bf166d4792d81744a6be332c160bd7d9273af8269d8a01284b9562f14a5b31b7abcf7ad9306c44887ecff51c89ab
  languageName: node
  linkType: hard

"repeat-string@npm:^1.0.0":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 10/1b809fc6db97decdc68f5b12c4d1a671c8e3f65ec4a40c238bc5200e44e85bcc52a54f78268ab9c29fcf5fe4f1343e805420056d1f30fa9a9ee4c2d93e3cc6c0
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10/a72468e2589270d91f06c7d36ec97a88db53ae5d6fe3787fadc943f0b0276b10347f89b363b2a82285f650bdcc135ad4a257c61bdd4d00d6df1fa24875b0ddaf
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10/839a3a890102a658f4cb3e7b2aa13a1f80a3a976b512020c3d1efc418491c48a886b6e481ea56afc6c4cb5eef678f23b2a4e70575e7534eccadf5e30ed2e56eb
  languageName: node
  linkType: hard

"resolve-alpn@npm:^1.2.0":
  version: 1.2.1
  resolution: "resolve-alpn@npm:1.2.1"
  checksum: 10/744e87888f0b6fa0b256ab454ca0b9c0b80808715e2ef1f3672773665c92a941f6181194e30ccae4a8cd0adbe0d955d3f133102636d2ee0cca0119fec0bc9aec
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10/91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"resolve@npm:1.1.x":
  version: 1.1.7
  resolution: "resolve@npm:1.1.7"
  checksum: 10/0a4ff8a102b1d059321caf77563cb2c495979c734f9dc400a70e3ceaaafe76a72bbcc625f9361756348d7b6af6d3cd2815cfbe3109be655a2b18e62d1cdadfc5
  languageName: node
  linkType: hard

"resolve@npm:1.17.0":
  version: 1.17.0
  resolution: "resolve@npm:1.17.0"
  dependencies:
    path-parse: "npm:^1.0.6"
  checksum: 10/74141da8c56192fd46f6aa887864f8fd74c1755425174526610cb775177278bb414c6f6feb3051ccd73d774d2ae124c6c97e463e30d7ffd9a87f7da202b851dd
  languageName: node
  linkType: hard

"resolve@npm:^1.1.6, resolve@npm:^1.8.1":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/0a398b44da5c05e6e421d70108822c327675febb880eebe905587628de401854c61d5df02866ff34fc4cb1173a51c9f0e84a94702738df3611a62e2acdc68181
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A1.1.x#optional!builtin<compat/resolve>":
  version: 1.1.7
  resolution: "resolve@patch:resolve@npm%3A1.1.7#optional!builtin<compat/resolve>::version=1.1.7&hash=3bafbf"
  checksum: 10/dc5c99fb47807d3771be3135ac6bdb892186973d0895ab17838f0b85bb575e03111214aa16cb68b6416df3c1dd658081a066dd7a9af6e668c28b0025080b615c
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A1.17.0#optional!builtin<compat/resolve>":
  version: 1.17.0
  resolution: "resolve@patch:resolve@npm%3A1.17.0#optional!builtin<compat/resolve>::version=1.17.0&hash=c3c19d"
  dependencies:
    path-parse: "npm:^1.0.6"
  checksum: 10/02e87fe9233d169fdc5220572c7b8933c9e23323aaecfd5b8d0b106a7f09dc676dd4d380e66c72b1369489292bcb337b13aad28b480a1bde5a5c040ff16758ea
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.1.6#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.8.1#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/d4d878bfe3702d215ea23e75e0e9caf99468e3db76f5ca100d27ebdc527366fee3877e54bce7d47cc72ca8952fc2782a070d238bfa79a550eeb0082384c3b81a
  languageName: node
  linkType: hard

"responselike@npm:^3.0.0":
  version: 3.0.0
  resolution: "responselike@npm:3.0.0"
  dependencies:
    lowercase-keys: "npm:^3.0.0"
  checksum: 10/e0cc9be30df4f415d6d83cdede3c5c887cd4a73e7cc1708bcaab1d50a28d15acb68460ac5b02bcc55a42f3d493729c8856427dcf6e57e6e128ad05cba4cfb95e
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10/af47851b547e8a8dc89af144fceee17b80d5beaf5e6f57ed086432d79943434ff67ca526e92275be6f54b6189f6920a24eace75c2657eed32d02c400312b21ec
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10/063ffaccaaaca2cfd0ef3beafb12d6a03dd7ff1260d752d62a6077b5dfff6ae81bea571f655bb6b589d366930ec1bdd285d40d560c0dae9b12f125e54eb743d5
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
  checksum: 10/006accc40578ee2beae382757c4ce2908a826b27e2b079efdcd2959ee544ddf210b7b5d7d5e80467807604244e7388427330f5c6d4cd61e6edaddc5773ccc393
  languageName: node
  linkType: hard

"rlp@npm:^2.2.3, rlp@npm:^2.2.4":
  version: 2.2.7
  resolution: "rlp@npm:2.2.7"
  dependencies:
    bn.js: "npm:^5.2.0"
  bin:
    rlp: bin/rlp
  checksum: 10/cf1919a2dc99f336191b3363b76299db567c192b7ee3c6f5c722728c34f65577883c9c88eeb7a1bfcbc26693c8a4f1fb0662e79ee86f0c98dd258d6987303498
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10/cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.0, safe-buffer@npm:^5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10/32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"sc-istanbul@npm:^0.4.5":
  version: 0.4.6
  resolution: "sc-istanbul@npm:0.4.6"
  dependencies:
    abbrev: "npm:1.0.x"
    async: "npm:1.x"
    escodegen: "npm:1.8.x"
    esprima: "npm:2.7.x"
    glob: "npm:^5.0.15"
    handlebars: "npm:^4.0.1"
    js-yaml: "npm:3.x"
    mkdirp: "npm:0.5.x"
    nopt: "npm:3.x"
    once: "npm:1.x"
    resolve: "npm:1.1.x"
    supports-color: "npm:^3.1.0"
    which: "npm:^1.1.1"
    wordwrap: "npm:^1.0.0"
  bin:
    istanbul: lib/cli.js
  checksum: 10/69acccb8ef3af117a71a57a4a1767ce845e62d1d6ff3d6fd2b5e0dc02746772c352bebee67fd0d0bb805a864bd4753741b118690955955bf34c990c3db36c0f8
  languageName: node
  linkType: hard

"scrypt-js@npm:3.0.1, scrypt-js@npm:^3.0.0":
  version: 3.0.1
  resolution: "scrypt-js@npm:3.0.1"
  checksum: 10/2f8aa72b7f76a6f9c446bbec5670f80d47497bccce98474203d89b5667717223eeb04a50492ae685ed7adc5a060fc2d8f9fd988f8f7ebdaf3341967f3aeff116
  languageName: node
  linkType: hard

"secp256k1@npm:^4.0.1":
  version: 4.0.4
  resolution: "secp256k1@npm:4.0.4"
  dependencies:
    elliptic: "npm:^6.5.7"
    node-addon-api: "npm:^5.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
  checksum: 10/45000f348c853df7c1e2b67c48efb062ae78c0620ab1a5cfb02fa20d3aad39c641f4e7a18b3de3b54a7c0cc1e0addeb8ecd9d88bc332e92df17a92b60c36122a
  languageName: node
  linkType: hard

"semver@npm:^5.5.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10/fca14418a174d4b4ef1fecb32c5941e3412d52a4d3d85165924ce3a47fbc7073372c26faf7484ceb4bbc2bde25880c6b97e492473dc7e9708fdfb1c6a02d546e
  languageName: node
  linkType: hard

"semver@npm:^6.3.0":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10/1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.5.2, semver@npm:^7.5.4":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10/7a24cffcaa13f53c09ce55e05efe25cd41328730b2308678624f8b9f5fc3093fc4d189f47950f0b811ff8f3c3039c24a2c36717ba7961615c682045bf03e1dda
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: 10/445a420a6fa2eaee4b70cbd884d538e259ab278200a2ededd73253ada17d5d48e91fb1f4cd224a236ab62ea7ba0a70c6af29fc93b4f3d3078bf7da1c031fde58
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10/76e3f5d7f4b581b6100ff819761f04a984fa3f3990e72a6554b57188ded53efce2d3d6c0932c10f810b7c59414f85e2ab3c11521877d1dea1ce0b56dc906f485
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10/fde1630422502fbbc19e6844346778f99d449986b2f9cdcceb8326730d2f3d9964dbcb03c02aaadaefffecd0f2c063315ebea8b3ad895914bf1afc1747fc172e
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.8":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  bin:
    sha.js: ./bin.js
  checksum: 10/d833bfa3e0a67579a6ce6e1bc95571f05246e0a441dd8c76e3057972f2a3e098465687a4369b07e83a0375a88703577f71b5b2e966809e67ebc340dbedb478c7
  languageName: node
  linkType: hard

"sha1@npm:^1.1.1":
  version: 1.1.1
  resolution: "sha1@npm:1.1.1"
  dependencies:
    charenc: "npm:>= 0.0.1"
    crypt: "npm:>= 0.0.1"
  checksum: 10/da9f47e949988e2f595ef19733fd1dc736866ef6de4e421a55c13b444c03ae532e528b7350ae6ea55d9fb053be61d4648ec2cd5250d46cfdbdf4f6b4e763713d
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shelljs@npm:^0.8.3, shelljs@npm:^0.8.5":
  version: 0.8.5
  resolution: "shelljs@npm:0.8.5"
  dependencies:
    glob: "npm:^7.0.0"
    interpret: "npm:^1.0.0"
    rechoir: "npm:^0.6.2"
  bin:
    shjs: bin/shjs
  checksum: 10/f2178274b97b44332bbe9ddb78161137054f55ecf701c7a99db9552cb5478fe279ad5f5131d8a7c2f0730e01ccf0c629d01094143f0541962ce1a3d0243d23f7
  languageName: node
  linkType: hard

"shx@npm:0.3.4":
  version: 0.3.4
  resolution: "shx@npm:0.3.4"
  dependencies:
    minimist: "npm:^1.2.3"
    shelljs: "npm:^0.8.5"
  bin:
    shx: lib/cli.js
  checksum: 10/5271b60f7e322540799102ad6935121f10c857a995a1321357a7bffd1628674a4758a602153dc5cc126d8dc94d3489586587d3dee54dc3cac0222ca08a78e33a
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10/603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10/5771861f77feefe44f6195ed077a9e4f389acc188f895f570d56445e251b861754b547ea9ef73ecee4e01fdada6568bfe9020d2ec2dfc5571e9fa1bbc4a10615
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10/a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10/7d53b9db292c6262f326b6ff3bc1611db84ece36c2c7dc0e937954c13c73185b0406c56589e2bb8d071d6fee468e14c39fb5d203ee39be66b7b8174f179afaba
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10/aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10/94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 10/4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/ab3af97aeb162f32c80e176c717ccf16a11a6ebb4656a62b94c0f96495ea2a1f4a8206c04b54438558485d83d0c5f61920c07a1a5d3963892a589b40cc6107dd
  languageName: node
  linkType: hard

"solc@npm:0.8.26":
  version: 0.8.26
  resolution: "solc@npm:0.8.26"
  dependencies:
    command-exists: "npm:^1.2.8"
    commander: "npm:^8.1.0"
    follow-redirects: "npm:^1.12.1"
    js-sha3: "npm:0.8.0"
    memorystream: "npm:^0.3.1"
    semver: "npm:^5.5.0"
    tmp: "npm:0.0.33"
  bin:
    solcjs: solc.js
  checksum: 10/30ef9c2687f727eb5bdd685c77b1a0b354e7d6ba7a080cfcdce5a89f25a1399ff7949fecef47768088d825588da230da0044b46f056fc36f3959c0e3d3c9a82b
  languageName: node
  linkType: hard

"solhint@npm:^5.0.3":
  version: 5.1.0
  resolution: "solhint@npm:5.1.0"
  dependencies:
    "@solidity-parser/parser": "npm:^0.20.0"
    ajv: "npm:^6.12.6"
    antlr4: "npm:^4.13.1-patch-1"
    ast-parents: "npm:^0.0.1"
    chalk: "npm:^4.1.2"
    commander: "npm:^10.0.0"
    cosmiconfig: "npm:^8.0.0"
    fast-diff: "npm:^1.2.0"
    glob: "npm:^8.0.3"
    ignore: "npm:^5.2.4"
    js-yaml: "npm:^4.1.0"
    latest-version: "npm:^7.0.0"
    lodash: "npm:^4.17.21"
    pluralize: "npm:^8.0.0"
    prettier: "npm:^2.8.3"
    semver: "npm:^7.5.2"
    strip-ansi: "npm:^6.0.1"
    table: "npm:^6.8.1"
    text-table: "npm:^0.2.0"
  dependenciesMeta:
    prettier:
      optional: true
  bin:
    solhint: solhint.js
  checksum: 10/f2cae41687cbeb5589c72f35350949a560c68d6f851626fbb34bb0606c364dc52fd554125e7d1e071753e20341953a188edd94c23e8529be20bba9f00bfbc5c8
  languageName: node
  linkType: hard

"solidity-coverage@npm:0.8.13":
  version: 0.8.13
  resolution: "solidity-coverage@npm:0.8.13"
  dependencies:
    "@ethersproject/abi": "npm:^5.0.9"
    "@solidity-parser/parser": "npm:^0.18.0"
    chalk: "npm:^2.4.2"
    death: "npm:^1.1.0"
    difflib: "npm:^0.2.4"
    fs-extra: "npm:^8.1.0"
    ghost-testrpc: "npm:^0.0.2"
    global-modules: "npm:^2.0.0"
    globby: "npm:^10.0.1"
    jsonschema: "npm:^1.2.4"
    lodash: "npm:^4.17.21"
    mocha: "npm:^10.2.0"
    node-emoji: "npm:^1.10.0"
    pify: "npm:^4.0.1"
    recursive-readdir: "npm:^2.2.2"
    sc-istanbul: "npm:^0.4.5"
    semver: "npm:^7.3.4"
    shelljs: "npm:^0.8.3"
    web3-utils: "npm:^1.3.6"
  peerDependencies:
    hardhat: ^2.11.0
  bin:
    solidity-coverage: plugins/bin.js
  checksum: 10/c92e4c1356155e1445241fe0a004c14656517c29e7fec42cc76737dd91e555891861cd716bd5ebb08fbf004f479d2c1ea45bd6dca9350c1ead2ae72c1271f37a
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.13":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10/8317e12d84019b31e34b86d483dd41d6f832f389f7417faf8fc5c75a66a12d9686e47f589a0554a868b8482f037e23df9d040d29387eb16fa14cb85f091ba207
  languageName: node
  linkType: hard

"source-map@npm:^0.5.0":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 10/9b4ac749ec5b5831cad1f8cc4c19c4298ebc7474b24a0acf293e2f040f03f8eeccb3d01f12aa0f90cf46d555c887e03912b83a042c627f419bda5152d89c5269
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10/59ef7462f1c29d502b3057e822cdbdae0b0e565302c4dd1a95e11e793d8d9d62006cdc10e0fd99163ca33ff2071360cf50ee13f90440806e7ed57d81cba2f7ff
  languageName: node
  linkType: hard

"source-map@npm:~0.2.0":
  version: 0.2.0
  resolution: "source-map@npm:0.2.0"
  dependencies:
    amdefine: "npm:>=0.0.4"
  checksum: 10/616b67d874a4bce443d285db07f8e4c6b1a1e60df17ea4e4d357c8173bd4b165c97386ee0675ef67afb9a9f1bdbd511368544febc4d92c8d8d1ebda57c4e7efb
  languageName: node
  linkType: hard

"split2@npm:^3.0.0":
  version: 3.2.2
  resolution: "split2@npm:3.2.2"
  dependencies:
    readable-stream: "npm:^3.0.0"
  checksum: 10/a426e1e6718e2f7e50f102d5ec3525063d885e3d9cec021a81175fd3497fdb8b867a89c99e70bef4daeef4f2f5e544f7b92df8c1a30b4254e10a9cfdcc3dae87
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10/c34828732ab8509c2741e5fd1af6b767c3daf2c642f267788f933a65b1614943c282e74c4284f4fa749c264b18ee016a0d37a3e5b73aee446da46277d3a85daa
  languageName: node
  linkType: hard

"squirrelly@npm:^8.0.8":
  version: 8.0.8
  resolution: "squirrelly@npm:8.0.8"
  checksum: 10/b07f7456b9f7709cdfd5b04d9756304838384fd7f932fb76fab0d1489a85158fd3e78cc22cb2c93e8b92156003a2f5f12fd614c202b72d538f293a5dc25f505b
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.11
  resolution: "stacktrace-parser@npm:0.1.11"
  dependencies:
    type-fest: "npm:^0.7.1"
  checksum: 10/1120cf716606ec6a8e25cc9b6ada79d7b91e6a599bba1a6664e6badc8b5f37987d7df7d9ad0344f717a042781fd8e1e999de08614a5afea451b68902421036b5
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10/18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"string-format@npm:^2.0.0":
  version: 2.0.0
  resolution: "string-format@npm:2.0.0"
  checksum: 10/8889014e926f69aaa8d117551a84a97cd7932484f5b0ab5b5b760eb0761e5722dee6112893ea742efac5adeb1b08dfedb77d9a91192dcd683a331e06c5148a87
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.0.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.2, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10/54d23f4a6acae0e93f999a585e673be9e561b65cd4cca37714af1e893ab8cd8dfa52a9e4f58f48f87b4a44918d3a9254326cb80ed194bf2e4c226e2b21767e56
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-hex-prefix@npm:1.0.0":
  version: 1.0.0
  resolution: "strip-hex-prefix@npm:1.0.0"
  dependencies:
    is-hex-prefixed: "npm:1.0.0"
  checksum: 10/4cafe7caee1d281d3694d14920fd5d3c11adf09371cef7e2ccedd5b83efd9e9bd2219b5d6ce6e809df6e0f437dc9d30db1192116580875698aad164a6d6b285b
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 10/1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"supports-color@npm:^3.1.0":
  version: 3.2.3
  resolution: "supports-color@npm:3.2.3"
  dependencies:
    has-flag: "npm:^1.0.0"
  checksum: 10/476a70d263a1f7ac11c26c10dfc58f0d9439edf198005b95f0e358ea8182d06b492d96320f16a841e4e968c7189044dd8c3f3037bd533480d15c7cc00e17c5d8
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10/5f505c6fa3c6e05873b43af096ddeb22159831597649881aeb8572d6fe3b81e798cc10840d0c9735e0026b250368851b7f77b65e84f4e4daa820a4f69947f55b
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/157b534df88e39c5518c5e78c35580c1eca848d7dbaf31bbe06cdfc048e22c7ff1a9d046ae17b25691128f631a51d9ec373c1b740c12ae4f0de6e292037e4282
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10/a9dc19ae2220c952bd2231d08ddeecb1b0328b61e72071ff4000c8384e145cc07c1c0bdb3b5a1cb06e186a7b2790f1dee793418b332f6ddf320de25d9125be7e
  languageName: node
  linkType: hard

"table-layout@npm:^1.0.2":
  version: 1.0.2
  resolution: "table-layout@npm:1.0.2"
  dependencies:
    array-back: "npm:^4.0.1"
    deep-extend: "npm:~0.6.0"
    typical: "npm:^5.2.0"
    wordwrapjs: "npm:^4.0.0"
  checksum: 10/5dd12bc64ddf246f774fc51b45398dd8da900b7bb246595c84007ea292c15936264701660b80704be17da5d4066a9a250549418c40a2b635a0916c9294b103af
  languageName: node
  linkType: hard

"table@npm:^6.8.0, table@npm:^6.8.1":
  version: 6.9.0
  resolution: "table@npm:6.9.0"
  dependencies:
    ajv: "npm:^8.0.1"
    lodash.truncate: "npm:^4.4.2"
    slice-ansi: "npm:^4.0.0"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/976da6d89841566e39628d1ba107ffab126964c9390a0a877a7c54ebb08820bf388d28fe9f8dcf354b538f19634a572a506c38a3762081640013a149cc862af9
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 10/4383b5baaeffa9bb4cda2ac33a4aa2e6d1f8aaf811848bf73513a9b88fd76372dc461f6fd6d2e9cb5100f48b473be32c6f95bd983509b7d92bb4d92c10747452
  languageName: node
  linkType: hard

"through2@npm:^4.0.0":
  version: 4.0.2
  resolution: "through2@npm:4.0.2"
  dependencies:
    readable-stream: "npm:3"
  checksum: 10/72c246233d9a989bbebeb6b698ef0b7b9064cb1c47930f79b25d87b6c867e075432811f69b7b2ac8da00ca308191c507bdab913944be8019ac43b036ce88f6ba
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.6":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10/3d306d319718b7cc9d79fb3f29d8655237aa6a1f280860a217f93417039d0614891aee6fc47c5db315f4fcc6ac8d55eb8e23e2de73b2c51a431b42456d9e5764
  languageName: node
  linkType: hard

"tmp@npm:0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: "npm:~1.0.2"
  checksum: 10/09c0abfd165cff29b32be42bc35e80b8c64727d97dedde6550022e88fa9fd39a084660415ed8e3ebaa2aca1ee142f86df8b31d4196d4f81c774a3a20fd4b6abf
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: 10/be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10/952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.0.1":
  version: 1.4.3
  resolution: "ts-api-utils@npm:1.4.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 10/713c51e7392323305bd4867422ba130fbf70873ef6edbf80ea6d7e9c8f41eeeb13e40e8e7fe7cd321d74e4864777329797077268c9f570464303a1723f1eed39
  languageName: node
  linkType: hard

"ts-command-line-args@npm:^2.2.0":
  version: 2.5.1
  resolution: "ts-command-line-args@npm:2.5.1"
  dependencies:
    chalk: "npm:^4.1.0"
    command-line-args: "npm:^5.1.1"
    command-line-usage: "npm:^6.1.0"
    string-format: "npm:^2.0.0"
  bin:
    write-markdown: dist/write-markdown.js
  checksum: 10/dd1b1fcd7aea599a909f037903bd4903c25e44e034dac8e1a2c049f34992c6cb4c9c692023c92d0dbd0f6183c3bd1bfff2181fee57099b6c5f296d38038224bf
  languageName: node
  linkType: hard

"ts-essentials@npm:^1.0.0":
  version: 1.0.4
  resolution: "ts-essentials@npm:1.0.4"
  checksum: 10/2e19bbe51203707ca732dcc6c3f238b2cf22bb9213d26ae0246c02325fb3e5f17c32505ac79c1bd538b7951a798155b07422e263a95cb295070a48233e45a1b5
  languageName: node
  linkType: hard

"ts-essentials@npm:^7.0.1":
  version: 7.0.3
  resolution: "ts-essentials@npm:7.0.3"
  peerDependencies:
    typescript: ">=3.7.0"
  checksum: 10/021b4263ddd58897171f3f5c467b5c872f76ba2ea07dfc11fa9667ba8d62ccb7f390db3e581139dcc6da94c3ff6306921f574acdb2b94cbc9d7da3e859e24665
  languageName: node
  linkType: hard

"ts-generator@npm:^0.1.1":
  version: 0.1.1
  resolution: "ts-generator@npm:0.1.1"
  dependencies:
    "@types/mkdirp": "npm:^0.5.2"
    "@types/prettier": "npm:^2.1.1"
    "@types/resolve": "npm:^0.0.8"
    chalk: "npm:^2.4.1"
    glob: "npm:^7.1.2"
    mkdirp: "npm:^0.5.1"
    prettier: "npm:^2.1.2"
    resolve: "npm:^1.8.1"
    ts-essentials: "npm:^1.0.0"
  bin:
    ts-generator: dist/cli/run.js
  checksum: 10/6a055d774f935f7e79dc686d53f4c3b3a1eda1bf8d6d5c601e6e757b8287532a26e00005310d94126c5a27aaaf8b72aeacdc68deec3eff589e789f277e2ef5f6
  languageName: node
  linkType: hard

"ts-node@npm:^10.9.2":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": "npm:^0.8.0"
    "@tsconfig/node10": "npm:^1.0.7"
    "@tsconfig/node12": "npm:^1.0.7"
    "@tsconfig/node14": "npm:^1.0.0"
    "@tsconfig/node16": "npm:^1.0.2"
    acorn: "npm:^8.4.1"
    acorn-walk: "npm:^8.1.1"
    arg: "npm:^4.1.0"
    create-require: "npm:^1.1.0"
    diff: "npm:^4.0.1"
    make-error: "npm:^1.1.1"
    v8-compile-cache-lib: "npm:^3.0.1"
    yn: "npm:3.1.1"
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 10/a91a15b3c9f76ac462f006fa88b6bfa528130dcfb849dd7ef7f9d640832ab681e235b8a2bc58ecde42f72851cc1d5d4e22c901b0c11aa51001ea1d395074b794
  languageName: node
  linkType: hard

"tslib@npm:2.4.0":
  version: 2.4.0
  resolution: "tslib@npm:2.4.0"
  checksum: 10/d8379e68b36caf082c1905ec25d17df8261e1d68ddc1abfd6c91158a064f6e4402039ae7c02cf4c81d12e3a2a2c7cd8ea2f57b233eb80136a2e3e7279daf2911
  languageName: node
  linkType: hard

"tslib@npm:2.7.0":
  version: 2.7.0
  resolution: "tslib@npm:2.7.0"
  checksum: 10/9a5b47ddac65874fa011c20ff76db69f97cf90c78cff5934799ab8894a5342db2d17b4e7613a087046bc1d133d21547ddff87ac558abeec31ffa929c88b7fce6
  languageName: node
  linkType: hard

"tslib@npm:^1.9.3":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10/7dbf34e6f55c6492637adb81b555af5e3b4f9cc6b998fb440dac82d3b42bdc91560a35a5fb75e20e24a076c651438234da6743d139e4feabf0783f3cdfe1dddb
  languageName: node
  linkType: hard

"tsort@npm:0.0.1":
  version: 0.0.1
  resolution: "tsort@npm:0.0.1"
  checksum: 10/5f15ca0e91142a72d2acb6e9798a0297b754ce402c8f8bbb63457ee17f062272f3ccdf39f4c3155f0568337cb3b5422410b40cfeed72fe75fbb9a71f016cdcf9
  languageName: node
  linkType: hard

"tweetnacl-util@npm:^0.15.1":
  version: 0.15.1
  resolution: "tweetnacl-util@npm:0.15.1"
  checksum: 10/ae6aa8a52cdd21a95103a4cc10657d6a2040b36c7a6da7b9d3ab811c6750a2d5db77e8c36969e75fdee11f511aa2b91c552496c6e8e989b6e490e54aca2864fc
  languageName: node
  linkType: hard

"tweetnacl@npm:^1.0.3":
  version: 1.0.3
  resolution: "tweetnacl@npm:1.0.3"
  checksum: 10/ca122c2f86631f3c0f6d28efb44af2a301d4a557a62a3e2460286b08e97567b258c2212e4ad1cfa22bd6a57edcdc54ba76ebe946847450ab0999e6d48ccae332
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10/14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"type-check@npm:~0.3.2":
  version: 0.3.2
  resolution: "type-check@npm:0.3.2"
  dependencies:
    prelude-ls: "npm:~1.1.2"
  checksum: 10/11dec0b50d7c3fd2e630b4b074ba36918ed2b1efbc87dfbd40ba9429d49c58d12dad5c415ece69fcf358fa083f33466fc370f23ab91aa63295c45d38b3a60dda
  languageName: node
  linkType: hard

"type-detect@npm:^4.0.0, type-detect@npm:^4.1.0":
  version: 4.1.0
  resolution: "type-detect@npm:4.1.0"
  checksum: 10/e363bf0352427a79301f26a7795a27718624c49c576965076624eb5495d87515030b207217845f7018093adcbe169b2d119bb9b7f1a31a92bfbb1ab9639ca8dd
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 10/8907e16284b2d6cfa4f4817e93520121941baba36b39219ea36acfe64c86b9dbc10c9941af450bd60832c8f43464974d51c0957f9858bc66b952b66b6914cbb9
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10/f4254070d9c3d83a6e573bcb95173008d73474ceadbbf620dd32d273940ca18734dff39c2b2480282df9afe5d1675ebed5499a00d791758748ea81f61a38961f
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 10/0699b6011bb3f7fac5fd5385e2e09432cde08fa89283f24084f29db00ec69a5445cd3aa976438ec74fc552a9a96f4a04ed390b5cb62eb7483aa4b6e5b935e059
  languageName: node
  linkType: hard

"typechain@npm:^8.2.0, typechain@npm:^8.3.2":
  version: 8.3.2
  resolution: "typechain@npm:8.3.2"
  dependencies:
    "@types/prettier": "npm:^2.1.1"
    debug: "npm:^4.3.1"
    fs-extra: "npm:^7.0.0"
    glob: "npm:7.1.7"
    js-sha3: "npm:^0.8.0"
    lodash: "npm:^4.17.15"
    mkdirp: "npm:^1.0.4"
    prettier: "npm:^2.3.1"
    ts-command-line-args: "npm:^2.2.0"
    ts-essentials: "npm:^7.0.1"
  peerDependencies:
    typescript: ">=4.3.0"
  bin:
    typechain: dist/cli/cli.js
  checksum: 10/d6dad2f70bb3914c56bac6758ba2a761a1592a8258aa9f84360fda410c27bfade0b2f49faa366df94ac3c2f567d40b3db17f4a32903ef52bc7f9a020545eeb7f
  languageName: node
  linkType: hard

"typescript@npm:^5.6.2":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/65c40944c51b513b0172c6710ee62e951b70af6f75d5a5da745cb7fab132c09ae27ffdf7838996e3ed603bb015dadd099006658046941bd0ba30340cc563ae92
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.6.2#optional!builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=8c6c40"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/98470634034ec37fd9ea61cc82dcf9a27950d0117a4646146b767d085a2ec14b137aae9642a83d1c62732d7fdcdac19bb6288b0bb468a72f7a06ae4e1d2c72c9
  languageName: node
  linkType: hard

"typical@npm:^4.0.0":
  version: 4.0.0
  resolution: "typical@npm:4.0.0"
  checksum: 10/aefe2c24b025cda22534ae2594df4a1df5db05b5fe3692890fd51db741ca4f18937a149f968b8d56d9a7b0756e7cd8843b1907bea21987ff4a06619c54d5a575
  languageName: node
  linkType: hard

"typical@npm:^5.2.0":
  version: 5.2.0
  resolution: "typical@npm:5.2.0"
  checksum: 10/fd8e4197cb2e021ca6d11fea0018ee219c29bf4160ab613492f74c0e21806003d1cd92a15088b111778a7b5c6432e4e28321899785a86980b390b87c4010efe5
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.19.3
  resolution: "uglify-js@npm:3.19.3"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 10/6b9639c1985d24580b01bb0ab68e78de310d38eeba7db45bec7850ab4093d8ee464d80ccfaceda9c68d1c366efbee28573b52f95e69ac792354c145acd380b11
  languageName: node
  linkType: hard

"undici-types@npm:~6.19.2":
  version: 6.19.8
  resolution: "undici-types@npm:6.19.8"
  checksum: 10/cf0b48ed4fc99baf56584afa91aaffa5010c268b8842f62e02f752df209e3dea138b372a60a963b3b2576ed932f32329ce7ddb9cb5f27a6c83040d8cd74b7a70
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10/ec8f41aa4359d50f9b59fa61fe3efce3477cc681908c8f84354d8567bb3701fafdddf36ef6bff307024d3feb42c837cf6f670314ba37fc8145e219560e473d14
  languageName: node
  linkType: hard

"undici@npm:^5.14.0":
  version: 5.29.0
  resolution: "undici@npm:5.29.0"
  dependencies:
    "@fastify/busboy": "npm:^2.0.0"
  checksum: 10/0ceca8924a32acdcc0cfb8dd2d368c217840970aa3f5e314fc169608474be6341c5b8e50cad7bd257dbe3b4e432bc5d0a0d000f83644b54fa11a48735ec52b93
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 10/40cdc60f6e61070fe658ca36016a8f4ec216b29bf04a55dce14e3710cc84c7448538ef4dad3728d0bfe29975ccd7bfb5f414c45e7b78883567fb31b246f02dff
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10/ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10/4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10/b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"utf8@npm:3.0.0":
  version: 3.0.0
  resolution: "utf8@npm:3.0.0"
  checksum: 10/31d19c4faacbb65b09ebc1c21c32b20bdb0919c6f6773cee5001b99bb83f8e503e7233c08fc71ebb34f7cfebd95cec3243b81d90176097aa2f286cccb4ce866e
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10/474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 10/9a5f7aa1d6f56dd1e8d5f2478f855f25c645e64e26e347a98e98d95781d5ed20062d6cca2eecb58ba7c84bc3910be95c0451ef4161906abaab44f9cb68ffbdd1
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 10/88d3423a52b6aaf1836be779cab12f7016d47ad8430dffba6edf766695e6d90ad4adaa3d8eeb512cc05924f3e246c4a4ca51e089dccf4402caa536b5e5be8961
  languageName: node
  linkType: hard

"viem@npm:2.7.14":
  version: 2.7.14
  resolution: "viem@npm:2.7.14"
  dependencies:
    "@adraffy/ens-normalize": "npm:1.10.0"
    "@noble/curves": "npm:1.2.0"
    "@noble/hashes": "npm:1.3.2"
    "@scure/bip32": "npm:1.3.2"
    "@scure/bip39": "npm:1.2.1"
    abitype: "npm:1.0.0"
    isows: "npm:1.0.3"
    ws: "npm:8.13.0"
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/fda804cdbf0bace0368d0ae6342749f19639675631d8c41f0810692de1af3c61a8ea2548aceebeef7bada9c547ff58b2e45a0225b77a4bfbf0e1ef371dbd3acf
  languageName: node
  linkType: hard

"web3-utils@npm:^1.3.6":
  version: 1.10.4
  resolution: "web3-utils@npm:1.10.4"
  dependencies:
    "@ethereumjs/util": "npm:^8.1.0"
    bn.js: "npm:^5.2.1"
    ethereum-bloom-filters: "npm:^1.0.6"
    ethereum-cryptography: "npm:^2.1.2"
    ethjs-unit: "npm:0.1.6"
    number-to-bn: "npm:1.7.0"
    randombytes: "npm:^2.1.0"
    utf8: "npm:3.0.0"
  checksum: 10/3e586b638cdae9fa45b7698e8a511ae2cbf60e219a900351ae38d384beaaf67424ac6e1d9c5098c3fb8f2ff3cc65a70d977a20bdce3dad542cb50deb666ea2a3
  languageName: node
  linkType: hard

"which@npm:^1.1.1, which@npm:^1.3.1":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    which: ./bin/which
  checksum: 10/549dcf1752f3ee7fbb64f5af2eead4b9a2f482108b7de3e85c781d6c26d8cf6a52d37cfbe0642a155fa6470483fe892661a859c03157f24c669cf115f3bbab5e
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"widest-line@npm:^3.1.0":
  version: 3.1.0
  resolution: "widest-line@npm:3.1.0"
  dependencies:
    string-width: "npm:^4.0.0"
  checksum: 10/03db6c9d0af9329c37d74378ff1d91972b12553c7d72a6f4e8525fe61563fa7adb0b9d6e8d546b7e059688712ea874edd5ded475999abdeedf708de9849310e0
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5, word-wrap@npm:~1.2.3":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10/1ec6f6089f205f83037be10d0c4b34c9183b0b63fca0834a5b3cee55dd321429d73d40bb44c8fc8471b5203d6e8f8275717f49a8ff4b2b0ab41d7e1b563e0854
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 10/497d40beb2bdb08e6d38754faa17ce20b0bf1306327f80cb777927edb23f461ee1f6bc659b3c3c93f26b08e1cf4b46acc5bae8fda1f0be3b5ab9a1a0211034cd
  languageName: node
  linkType: hard

"wordwrapjs@npm:^4.0.0":
  version: 4.0.1
  resolution: "wordwrapjs@npm:4.0.1"
  dependencies:
    reduce-flatten: "npm:^2.0.0"
    typical: "npm:^5.2.0"
  checksum: 10/4182c48c9d3eab0932fb9f9f202e3f1d4d28ff6db3fd2e1654ec8606677d8e0ab80110f0f8e2e236ee2b52631cbc5fccf3097e9287e3ace20cbc1613a784befc
  languageName: node
  linkType: hard

"workerpool@npm:^6.5.1":
  version: 6.5.1
  resolution: "workerpool@npm:6.5.1"
  checksum: 10/b1b00139fe62f2ebec556a2af8085bf6e7502ad26cf2a4dcb34fb4408b2e68aa12c88b0a50cb463b24f2806d60fa491fc0da933b56ec3b53646aeec0025d14cb
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10/159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:7.4.6":
  version: 7.4.6
  resolution: "ws@npm:7.4.6"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/150e3f917b7cde568d833a5ea6ccc4132e59c38d04218afcf2b6c7b845752bd011a9e0dc1303c8694d3c402a0bdec5893661a390b71ff88f0fc81a4e4e66b09c
  languageName: node
  linkType: hard

"ws@npm:8.13.0":
  version: 8.13.0
  resolution: "ws@npm:8.13.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/1769532b6fdab9ff659f0b17810e7501831d34ecca23fd179ee64091dd93a51f42c59f6c7bb4c7a384b6c229aca8076fb312aa35626257c18081511ef62a161d
  languageName: node
  linkType: hard

"ws@npm:8.17.1":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/4264ae92c0b3e59c7e309001e93079b26937aab181835fb7af79f906b22cd33b6196d96556dafb4e985742dd401e99139572242e9847661fdbc96556b9e6902d
  languageName: node
  linkType: hard

"ws@npm:8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/70dfe53f23ff4368d46e4c0b1d4ca734db2c4149c6f68bc62cb16fc21f753c47b35fcc6e582f3bdfba0eaeb1c488cddab3c2255755a5c3eecb251431e42b3ff6
  languageName: node
  linkType: hard

"ws@npm:^7.4.6":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10/9c796b84ba80ffc2c2adcdfc9c8e9a219ba99caa435c9a8d45f9ac593bba325563b3f83edc5eb067cc6d21b9a6bf2c930adf76dd40af5f58a5ca6859e81858f0
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10/5f1b5f95e3775de4514edbb142398a2c37849ccfaf04a015be5d75521e9629d3be29bd4432d23c57f37e5b61ade592fb0197022e9993f81a06a5afbdcda9346d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2, yargs-parser@npm:^20.2.9":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 10/0188f430a0f496551d09df6719a9132a3469e47fe2747208b1dd0ab2bb0c512a95d0b081628bbca5400fb20dbf2fabe63d22badb346cecadffdd948b049f3fcc
  languageName: node
  linkType: hard

"yargs-unparser@npm:^2.0.0":
  version: 2.0.0
  resolution: "yargs-unparser@npm:2.0.0"
  dependencies:
    camelcase: "npm:^6.0.0"
    decamelize: "npm:^4.0.0"
    flat: "npm:^5.0.2"
    is-plain-obj: "npm:^2.1.0"
  checksum: 10/68f9a542c6927c3768c2f16c28f71b19008710abd6b8f8efbac6dcce26bbb68ab6503bed1d5994bdbc2df9a5c87c161110c1dfe04c6a3fe5c6ad1b0e15d9a8a3
  languageName: node
  linkType: hard

"yargs@npm:^16.2.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: "npm:^7.0.2"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.0"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^20.2.2"
  checksum: 10/807fa21211d2117135d557f95fcd3c3d390530cda2eca0c840f1d95f0f40209dcfeb5ec18c785a1f3425896e623e3b2681e8bb7b6600060eda1c3f4804e7957e
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 10/2c487b0e149e746ef48cda9f8bad10fc83693cd69d7f9dcd8be4214e985de33a29c9e24f3c0d6bcf2288427040a8947406ab27f7af67ee9456e6b84854f02dd6
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10/f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zksync-ethers@npm:^5.0.0":
  version: 5.10.0
  resolution: "zksync-ethers@npm:5.10.0"
  dependencies:
    ethers: "npm:~5.7.0"
  peerDependencies:
    ethers: ~5.7.0
  checksum: 10/826719e2e40731e1104cf8a0c16c758526de6ca9e907d0483eb5bd80b635f02e3cce012115b75d68976a8dd746d63d4f83d576cc3bddc18a02a49d2bc023347f
  languageName: node
  linkType: hard
