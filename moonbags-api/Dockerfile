# Stage 1: Builder
FROM node:20.18.0-alpine AS builder
WORKDIR /app
COPY . .
RUN apk add g++ make py3-pip
RUN yarn install
RUN yarn build

# Stage 2: Final Image
FROM node:20.18.0-alpine

WORKDIR /app
# Copy dependencies from builder (ensure node_modules/.bin is included)
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/node_modules/.bin ./node_modules/.bin
COPY --from=builder /app/dist ./dist
COPY package.json yarn.lock ./

# Create output directory
RUN mkdir output-specs
