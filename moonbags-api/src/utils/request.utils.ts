import { Request } from 'express';

export const getIpAddress = (req: Request) => {
  // Validate req object and headers
  if (!req || !req.headers) {
    console.log('Throttle tracker - Invalid request object');
    return 'unknown';
  }

  const forwardedFor = req.headers['x-forwarded-for'] as string;
  console.log('Throttle tracker - forwardedFor:', forwardedFor);

  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim();
  }
  console.log(
    'Throttle tracker - req.ip:',
    req.ips.length ? req.ips[0] : req.ip,
  );
  return req.ips.length ? req.ips[0] : req.ip;
};
