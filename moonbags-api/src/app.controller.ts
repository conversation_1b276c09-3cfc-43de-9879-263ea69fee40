import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import Redis from 'ioredis';
import { RedisService } from '@liaoliaots/nestjs-redis';

@Controller()
export class AppController {
  private readonly redis: Redis | null;
  constructor(
    private readonly appService: AppService,
    private readonly redisService: RedisService,
  ) {
    this.redis = this.redisService.getOrThrow();
  }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health-check')
  async healthCheck() {
    await this.redis.set('HEALTH_CHECK', 'ok');
    const redisStatus = await this.redis.get('HEALTH_CHECK');

    return {
      redisStatus,
    };
  }
}
