import { ExecutionContext, Injectable } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';
import { getIpAddress } from 'src/utils/request.utils';
import { Request } from 'express';

const WHITELIST_IPS = ['127.0.0.1'];

@Injectable()
export class AppThrottlerGuard extends ThrottlerGuard {
  protected async getTracker(req: Request): Promise<string> {
    return getIpAddress(req);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();

    if (!req || !req.headers) {
      return true;
    }

    const ip = await this.getTracker(req);

    if (WHITELIST_IPS.includes(ip)) {
      return true;
    }

    return super.canActivate(context);
  }
}
