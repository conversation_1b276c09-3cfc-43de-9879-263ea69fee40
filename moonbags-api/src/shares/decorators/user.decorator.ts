import {
  createParamDecorator,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtPayload } from '@shares/dtos/jwt-payload.dto';
import { jwtDecode } from 'jwt-decode';

export const AuthUser = createParamDecorator(
  (data: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    if (!request.user) {
      throw new UnauthorizedException();
    }
    return request.user;
  },
);

export const UserAddress = createParamDecorator(
  (data: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    try {
      const token = request.headers.authorization;
      const payload: JwtPayload = jwtDecode(token);

      return payload?.address;
    } catch (e) {
      console.log({ accessTokenError: e });
      throw new UnauthorizedException();
    }
  },
);
