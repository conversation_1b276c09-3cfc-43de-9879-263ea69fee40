import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as _ from 'lodash';
import { Consumer, Kafka, KafkaMessage, Producer } from 'kafkajs';
import { callWithChunk, sleep } from './utils/common.utils';
import { randomUUID } from 'crypto';

const perf = require('execution-time')();

const BATCH_SIZE = 50;

@Injectable()
export class BaseConsumer implements OnApplicationBootstrap {
  private kafka: Kafka;
  private producer: Producer;
  private consumer: Consumer;
  protected logger: Logger;

  constructor(private readonly configService: ConfigService) {
    this.logger = new Logger(BaseConsumer.name);
    this.kafka = new Kafka({
      clientId: this.configService.getOrThrow<string>('kafka.clientId'),
      brokers: this.configService.getOrThrow<string[]>('kafka.brokers'),
    });
    this.producer = this.kafka.producer({
      allowAutoTopicCreation: true,
    });
    this.consumer = this.kafka.consumer({
      groupId: this.configService.get<string>('kafka.groupId'),
    });
  }
  async onApplicationBootstrap() {
    await Promise.all([this.producer.connect(), this.consumer.connect()]);
  }

  subscribeTopic(): string | RegExp {
    throw new Error('Not implemented subscribeTopic');
  }

  async handleBatch(messages: KafkaMessage[]) {
    throw new Error('Not implemented handle');
  }

  async run() {
    try {
      const topic = this.subscribeTopic();
      await this.consumer.subscribe({ topic, fromBeginning: true });
      await this.consumer.run({
        autoCommitInterval: 10000,
        autoCommitThreshold: 100,
        eachBatch: async ({ batch, resolveOffset, heartbeat }) => {
          this.logger.log(
            `>>> Received new batch messages from topic: ${batch.topic}/partition:${batch.partition} with ${batch.messages.length} messages ********`,
          );
          try {
            await callWithChunk(
              batch.messages,
              async (batchMessages) => {
                const uuid = randomUUID();
                perf.start(uuid);

                await this.handleBatch(batchMessages);
                const results = perf.stop(uuid);

                console.log(
                  `Collected (uuid: ${uuid}) ${batchMessages.length} messages took ${results.preciseWords}`,
                );

                batchMessages.forEach((message) =>
                  resolveOffset(message.offset),
                );
                await heartbeat();

                await sleep(10);
              },
              BATCH_SIZE,
            );
          } catch (e) {
            console.log(`[ERROR] Error occurred while consuming message: `, e);
            throw e;
          }
        },
      });
    } catch (error) {
      await this.consumer.disconnect();
      await sleep(3000);
      throw error;
    }
  }
}
