import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  UseGuards,
  Delete,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CommentService } from './comment.service';
import {
  CommentQueryDto,
  CreateCommentDto,
  CreateReplyDto,
  ParentCommentResponseDto,
  ReplyResponseDto,
  UserLikesResponseDto,
} from './dtos/comment.dto';
import { ParseObjectIdPipe } from '@shares/pipes/parse-object-id.pipe';
import { UserAddress } from '@shares/decorators/user.decorator';
import { JwtAuthGuard } from '@modules/shared/auth/guards/auth.guard';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

const CACHE_TTL = 1000; //1 second

@ApiTags('Comments')
@Controller('comment')
export class CommentController {
  constructor(private readonly commentService: CommentService) {}

  @Get('coin/:coinAddress')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get comments by coin address' })
  @ApiResponse({
    status: 200,
    description: 'Return list of comments with replies',
    type: ParentCommentResponseDto,
  })
  async getComments(
    @Param('coinAddress') coinAddress: string,
    @Query() query: CommentQueryDto,
  ): Promise<ParentCommentResponseDto> {
    return this.commentService.findParentComments(coinAddress, query);
  }

  @Get(':parentCommentId/replies')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get replies for a comment' })
  @ApiResponse({
    status: 200,
    description: 'Return list of replies',
    type: ReplyResponseDto,
  })
  async getReplies(
    @Param('parentCommentId', ParseObjectIdPipe) parentCommentId: string,
    @Query() query: CommentQueryDto,
  ): Promise<ReplyResponseDto> {
    return this.commentService.findReplies(parentCommentId, query);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Post('coin/:coinAddress')
  @ApiOperation({ summary: 'Create comment' })
  async createComment(
    @Param('coinAddress') coinAddress: string,
    @Body() comment: CreateCommentDto,
    @UserAddress() address: string,
  ) {
    return this.commentService.createParentComment(
      coinAddress,
      comment,
      address,
    );
  }

  @ApiBearerAuth()
  @Post('reply/:commentId')
  @ApiOperation({ summary: 'Create reply to a comment' })
  @UseGuards(JwtAuthGuard)
  async createReply(
    @Param('commentId', ParseObjectIdPipe) commentId: string,
    @Body() reply: CreateReplyDto,
    @UserAddress() address: string,
  ) {
    return this.commentService.createReply(commentId, reply, address);
  }

  @ApiBearerAuth()
  @Post(':id/like')
  @ApiOperation({ summary: 'Like a comment' })
  @UseGuards(JwtAuthGuard)
  like(
    @Param('id', ParseObjectIdPipe) id: string,
    @UserAddress() address: string,
  ) {
    return this.commentService.like(id, address);
  }

  @ApiBearerAuth()
  @Delete(':id/like')
  @ApiOperation({ summary: 'Unlike a comment' })
  @UseGuards(JwtAuthGuard)
  unlike(
    @Param('id', ParseObjectIdPipe) id: string,
    @UserAddress() address: string,
  ) {
    return this.commentService.unlike(id, address);
  }

  @ApiBearerAuth()
  @Get(':coinAddress/likes')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({ summary: 'Get user likes for a coin address' })
  @ApiResponse({
    status: 200,
    description: 'Return list of comment IDs liked by user',
    type: UserLikesResponseDto,
  })
  @UseGuards(JwtAuthGuard)
  async getUserLikes(
    @Param('coinAddress') coinAddress: string,
    @UserAddress() address: string,
  ) {
    return this.commentService.getUserLikes(coinAddress, address);
  }
}
