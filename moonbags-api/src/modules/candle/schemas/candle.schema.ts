import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Decimal128, Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export type CandleDocument = Candle & Document;

@Schema({ timestamps: true })
export class Candle extends Document {
  @Prop({ required: true })
  tokenAddress: string;

  @Prop({ required: true })
  resolution: number;

  @Prop({ required: true })
  timestamp: number;

  @Prop({ type: Object, required: false })
  price: {
    open: Decimal128;
    high: Decimal128;
    low: Decimal128;
    close: Decimal128;
  };

  @Prop({ type: Object, required: false })
  priceUsd: {
    open: Decimal128;
    high: Decimal128;
    low: Decimal128;
    close: Decimal128;
  };

  @Prop({ type: Object, required: false })
  marketCap: {
    open: Decimal128;
    high: Decimal128;
    low: Decimal128;
    close: Decimal128;
  };

  @Prop({ type: Object, required: false })
  marketCapUsd: {
    open: Decimal128;
    high: Decimal128;
    low: Decimal128;
    close: Decimal128;
  };

  @Prop({ required: false })
  volumeBase: string;

  @Prop({ required: false })
  volumeQuote: string;

  @Prop({ required: false })
  volumeUsd: string;
}

export const CandleSchema = SchemaFactory.createForClass(Candle);
CandleSchema.plugin(mongoosePaginate);
CandleSchema.index(
  { tokenAddress: 1, timestamp: -1, resolution: 1 },
  { unique: true, background: true },
);
CandleSchema.index({ timestamp: -1, resolution: 1 }, { background: true });
