import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Expose, Type } from 'class-transformer';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { BigNumber } from 'bignumber.js';

type TQueryBy = 'price' | 'priceUsd' | 'marketCap' | 'marketCapUsd';

export class GetCandlesQueryDto {
  @ApiProperty()
  @Type(() => Number)
  @IsNotEmpty()
  limit: number;

  @ApiProperty()
  @Type(() => Number)
  @IsNotEmpty()
  to: number;

  @ApiProperty()
  @IsNotEmpty()
  tokenAddress: string;

  @ApiProperty()
  @Type(() => Number)
  @IsNotEmpty()
  resolution: number;

  @ApiPropertyOptional({
    enum: ['price', 'priceUsd', 'marketCap', 'marketCapUsd'],
    default: 'price',
  })
  @IsEnum(['price', 'priceUsd', 'marketCap', 'marketCapUsd'], {
    message:
      'queryBy must be one of the following: price, priceUsd, marketCap, marketCapUsd',
  })
  @IsNotEmpty()
  queryBy: TQueryBy;
}

export class CandleDto {
  @ApiProperty({
    example: 'Eda12dqdgNM8TBV7BYLMhdmX3NAzRQP78HcvZcxgvNP3',
    description: 'Token address',
  })
  @Expose()
  tokenAddress: string;

  @ApiProperty({
    example: 3600,
    description: 'Time resolution in seconds',
  })
  @Expose()
  resolution: number;

  @ApiProperty({
    example: 1739415600,
    description: 'Timestamp',
  })
  @Expose()
  timestamp: number;

  @ApiProperty({
    type: String,
    description: 'Trading volume base on base token',
  })
  @Type(() => String)
  @Expose()
  volumeBase: string;

  @ApiProperty({
    type: String,
    description: 'Trading volume base on quote token',
  })
  @Type(() => String)
  @Expose()
  volumeQuote: string;

  @ApiProperty({
    type: String,
    description: 'Trading volume base on usd',
  })
  @Type(() => String)
  @Expose()
  volumeUsd: string;

  @ApiProperty({
    type: String,
    description: 'Open price',
  })
  @Type(() => String)
  @Transform(({ value }) => new BigNumber(value || '0').toString())
  @Expose()
  open: string;

  @ApiProperty({
    type: String,
    description: 'High price',
  })
  @Type(() => String)
  @Transform(({ value }) => new BigNumber(value || '0').toString())
  @Expose()
  high: string;

  @ApiProperty({
    type: String,
    description: 'Low price',
  })
  @Type(() => String)
  @Expose()
  @Transform(({ value }) => new BigNumber(value || '0').toString())
  low: string;

  @ApiProperty({
    type: String,
    description: 'Close price',
  })
  @Type(() => String)
  @Transform(({ value }) => new BigNumber(value || '0').toString())
  @Expose()
  close: string;
}

export class ResponseCandlesDto {
  @ApiProperty({
    type: [CandleDto],
    description: 'Candles',
  })
  @Type(() => CandleDto)
  @Expose()
  candles: CandleDto[];

  @ApiProperty({
    type: Number,
    description: 'Timestamp offset',
  })
  @Expose()
  timestampOffset: number;
}
