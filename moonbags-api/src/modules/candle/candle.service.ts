import { Injectable } from '@nestjs/common';
import { CandleRepository } from './repositories/candle.repository';
import { standardizeCandleTime } from './candle.utils';
import { Candles, CandleRecord } from './candle.interface';
import {
  RESOLUTION_15MINUTES,
  RESOLUTION_HOUR,
  RESOLUTION_MINUTE,
  RESOLUTION_SECOND,
} from './candle.const';
import { maxBN, minBN, plusBN, toStringBN } from 'src/utils/number.utils';

@Injectable()
export class CandleService {
  private extraResolutions = [
    RESOLUTION_MINUTE,
    RESOLUTION_15MINUTES,
    RESOLUTION_HOUR,
  ];
  constructor(private readonly candleRepository: CandleRepository) {}

  async getCandlesByStrResolution(
    limit: number,
    to: number,
    tokenAddress: string,
    resolution: number,
    queryBy: string,
  ) {
    return this.getCandles(limit, to, tokenAddress, resolution, queryBy);
  }

  async getCandles(
    limit: number,
    to: number,
    tokenAddress: string,
    resolution: number,
    queryBy: string,
  ): Promise<Candles[]> {
    const toMs = to * 1000;
    const resolutionMs = resolution * 1000;
    const rows: CandleRecord[] = await this.getListCandleInDatabase(
      limit,
      toMs,
      tokenAddress,
      resolutionMs,
      queryBy,
    );

    const candles: Candles[] = [];

    // // format candle data to resolutionMs user query
    if (rows.length > 0) {
      let currentCandle = this.createCandleFromEntity(
        rows.shift(),
        resolutionMs,
      );
      for (const row of rows) {
        const candleTime = standardizeCandleTime(row.timestamp, resolutionMs);
        if (candleTime === currentCandle.timestamp) {
          currentCandle.low = minBN(currentCandle.low, row.low);
          currentCandle.high = maxBN(currentCandle.high, row.high);
          currentCandle.close = toStringBN(row.close);
          currentCandle.open = toStringBN(row.open);

          currentCandle.openUsd = toStringBN(row.open);
          currentCandle.lowUsd = minBN(currentCandle.lowUsd, row.low);
          currentCandle.highUsd = maxBN(currentCandle.highUsd, row.high);
          currentCandle.closeUsd = toStringBN(row.close);
          currentCandle.volumeUsd = plusBN(
            currentCandle.volumeUsd,
            row.volumeUsd,
          );
          currentCandle.volumeBase = plusBN(
            currentCandle.volumeBase,
            row.volumeBase,
          );
          currentCandle.volumeQuote = plusBN(
            currentCandle.volumeQuote,
            row.volumeQuote,
          );
        } else {
          candles.push(currentCandle);
          currentCandle = this.createCandleFromEntity(row, resolutionMs);
        }
      }
      candles.push(currentCandle);
    }
    return candles;
  }

  async getListCandleInDatabase(
    limit: number,
    to: number,
    tokenAddress: string,
    resolution: number,
    queryBy: string,
  ) {
    let queryResolution = RESOLUTION_SECOND;
    for (const supportedResolution of this.extraResolutions) {
      if (+resolution >= supportedResolution) {
        queryResolution = supportedResolution;
      }
    }
    const candleData = await this.candleRepository.aggregate([
      {
        $match: {
          tokenAddress,
          timestamp: { $lt: to },
          resolution: queryResolution,
        },
      },
      {
        $sort: { timestamp: -1, _id: -1 },
      },
      {
        $limit: limit,
      },
      {
        $project: {
          open: `$${queryBy}.open`,
          high: `$${queryBy}.high`,
          low: `$${queryBy}.low`,
          close: `$${queryBy}.close`,
          timestamp: 1,
          resolution: 1,
          tokenAddress: 1,
          volumeBase: 1,
          volumeQuote: 1,
          volumeUsd: 1,
        },
      },
    ]);

    return candleData;
  }

  async getPreviousCandleInDatabase(tokenAddress: string, time: number) {
    return this.candleRepository.getPreviousCandle(tokenAddress, time);
  }

  private createCandleFromEntity(
    entity: CandleRecord,
    resolution: number,
  ): CandleRecord {
    return {
      tokenAddress: entity.tokenAddress,
      resolution,
      timestamp: entity.timestamp,
      open: entity.open,
      close: entity.close,
      low: entity.low,
      high: entity.high,
      openUsd: entity.open,
      closeUsd: entity.close,
      lowUsd: entity.low,
      highUsd: entity.high,
      volumeBase: entity.volumeBase,
      volumeQuote: entity.volumeQuote,
      volumeUsd: entity.volumeUsd,
    };
  }

  async getVolumeUsdIn24hByTokenAddress(tokenAddress: string): Promise<string> {
    const resolution = RESOLUTION_MINUTE;
    const to = standardizeCandleTime(Date.now(), resolution);
    const from = to - 24 * 60 * 60 * 1000 + 60 * 1000;

    const candles = await this.candleRepository.aggregate([
      {
        $match: {
          tokenAddress,
          timestamp: { $gte: from, $lte: to },
          resolution,
        },
      },
    ]);

    const volumeUsd = candles.reduce((acc, candle) => {
      return plusBN(acc, candle.volumeUsd);
    }, '0');
    return volumeUsd;
  }

  async getVolumeUsdIn24h(tokenAddresses: string[]): Promise<string[]> {
    return Promise.all(
      tokenAddresses.map((tokenAddress) =>
        this.getVolumeUsdIn24hByTokenAddress(tokenAddress),
      ),
    );
  }

  async getVolumeUsdIn24hAllCoins(): Promise<string> {
    const resolution = RESOLUTION_MINUTE;
    const to = standardizeCandleTime(Date.now(), resolution);
    const from = to - 24 * 60 * 60 * 1000 + 60 * 1000;

    const candles = await this.candleRepository.aggregate([
      {
        $match: {
          timestamp: { $gte: from, $lte: to },
          resolution,
        },
      },
    ]);

    const volumeUsd = candles.reduce((acc, candle) => {
      return plusBN(acc, candle.volumeUsd);
    }, '0');
    return volumeUsd;
  }
}
