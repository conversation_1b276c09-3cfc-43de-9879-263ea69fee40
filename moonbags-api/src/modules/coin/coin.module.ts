import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SharedModule } from '@modules/shared/shared.module';
import { CoinService } from './coin.service';
import { Coin, CoinSchema } from './schemas/coin.schema';
import { CoinController } from './coin.controller';
import { CoinRepository } from './repositories/coin.repository';
import { UserRepository } from '@modules/shared/auth/repositories/user.repository';
import { AuthModule } from '@modules/shared/auth/auth.module';
import { User, UserSchema } from '@modules/shared/auth/schemas/user.schema';
import { JwtService } from '@nestjs/jwt';
import { CandleModule } from '@modules/candle/candle.module';
import { StakingModule } from '@modules/staking/staking.module';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeature([
      { name: Coin.name, schema: CoinSchema },
      { name: User.name, schema: UserSchema },
    ]),
    AuthModule,
    CandleModule,
    forwardRef(() => StakingModule),
  ],
  controllers: [CoinController],
  providers: [
    CoinRepository,
    CoinService,
    CoinRepository,
    UserRepository,
    JwtService,
  ],
  exports: [CoinRepository, CoinService],
})
export class CoinModule {}
