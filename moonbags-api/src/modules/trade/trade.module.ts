import { AuthModule } from '@modules/shared/auth/auth.module';
import { UserRepository } from '@modules/shared/auth/repositories/user.repository';
import { User, UserSchema } from '@modules/shared/auth/schemas/user.schema';
import { SharedModule } from '@modules/shared/shared.module';
import { Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { TradeRepository } from './repositories/trade.repository';
import { Trade, TradeSchema } from './schemas/trade.schema';
import { TradeController } from './trade.controller';
import { TradeService } from './trade.service';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeature([
      { name: Trade.name, schema: TradeSchema },
      { name: User.name, schema: UserSchema },
    ]),
    AuthModule,
  ],
  controllers: [TradeController],
  providers: [TradeRepository, TradeService, UserRepository, JwtService],
})
export class TradeModule {}
