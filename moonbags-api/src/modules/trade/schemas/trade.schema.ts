import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Decimal128 } from 'bson';

export enum TradeType {
  BUY = 'BUY',
  SELL = 'SELL',
}

export type TradeDocument = Trade & Document;

@Schema({ timestamps: true })
export class Trade extends Document {
  @Prop({ required: true })
  slot: number;

  @Prop({ required: true })
  hash: string;

  @Prop({ required: true })
  tokenAddress: string;

  @Prop({ required: true })
  index: number;

  @Prop({ required: true })
  maker: string;

  @Prop({ enum: TradeType, required: true })
  tradeType: TradeType;

  @Prop({ required: true })
  baseAmount: Decimal128;

  @Prop({ required: true })
  quoteAmount: Decimal128;

  @Prop({ required: true })
  price: Decimal128;

  @Prop({ required: true })
  priceUsd: Decimal128;

  @Prop({ required: true })
  volume: Decimal128;

  @Prop({ required: true })
  volumeUsd: Decimal128;

  @Prop({ required: true })
  timestamp: number;

  @Prop({ required: true })
  virtualHypeReserves: Decimal128;

  @Prop({ required: true })
  virtualTokenReserves: Decimal128;

  @Prop({ required: true })
  realHypeReserves: Decimal128;

  @Prop({ required: true })
  realTokenReserves: Decimal128;

  @Prop({ required: false })
  tokenSymbol: string;
}

export const TradeSchema = SchemaFactory.createForClass(Trade);

TradeSchema.plugin(mongoosePaginate);
TradeSchema.index({ tokenAddress: 1, timestamp: -1 }, { background: true });
TradeSchema.index({ timestamp: -1, _id: 1 }, { background: true });
