import { ApiProperty } from '@nestjs/swagger';
import { PaginatedResponseDto } from '@shares/dtos/paginated-response.dto';
import { Transform, Expose, Type } from 'class-transformer';
import { toStringBN } from 'src/utils/number.utils';

export class TokenInfoDto {
  @Expose()
  @ApiProperty()
  tokenAddress: string;

  @Expose()
  @ApiProperty()
  symbol: string;

  @Expose()
  @ApiProperty()
  logoUri: string;
}

export class CoinLockResponseDto {
  @Expose()
  @ApiProperty()
  contractId: string;

  @Expose()
  @ApiProperty()
  locker: string;

  @Expose()
  @ApiProperty()
  recipient: string;

  @Expose()
  @ApiProperty({ type: TokenInfoDto })
  @Type(() => TokenInfoDto)
  tokenInfo: TokenInfoDto;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  amount: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  fee: string;

  @Expose()
  @ApiProperty()
  startTime: number;

  @Expose()
  @ApiProperty()
  endTime: number;

  @Expose()
  @ApiProperty()
  closed: boolean;

  @Expose()
  @ApiProperty()
  createdAt: Date;

  @Expose()
  @ApiProperty()
  updatedAt: Date;
}

export class TotalLockedAmountResponseDto {
  @Expose()
  @ApiProperty({
    type: String,
  })
  tokenAddress: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  totalLockedAmount: string;
}

export class CoinLockListResponseDto extends PaginatedResponseDto<CoinLockResponseDto> {
  @Expose()
  @ApiProperty({ type: [CoinLockResponseDto] })
  @Type(() => CoinLockResponseDto)
  docs: CoinLockResponseDto[];
}
