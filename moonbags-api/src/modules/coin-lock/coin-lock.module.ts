import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SharedModule } from '@modules/shared/shared.module';
import { CoinLockController } from './coin-lock.controller';
import { CoinLockService } from './coin-lock.service';
import { CoinLockRepository } from './repositories/coin-lock.repository';
import { CoinLock, CoinLockSchema } from './schemas/coin-lock.schema';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeature([
      { name: CoinLock.name, schema: CoinLockSchema },
    ]),
  ],
  controllers: [CoinLockController],
  providers: [CoinLockService, CoinLockRepository],
  exports: [CoinLockService, CoinLockRepository],
})
export class CoinLockModule {}
