import {
  Controller,
  Get,
  Param,
  UseGuards,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
  ApiParam,
} from '@nestjs/swagger';
import { CoinLockService } from './coin-lock.service';
import { JwtAuthGuard } from '@modules/shared/auth/guards/auth.guard';
import { PaginationDto } from '@shares/dtos/pagination.dto';
import { UserAddress } from '@shares/decorators/user.decorator';
import {
  CoinLockListResponseDto,
  CoinLockResponseDto,
  TotalLockedAmountResponseDto,
} from './dtos/coin-lock.dto';
import { plainToInstance } from 'class-transformer';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

const CACHE_TTL = 1000; //1 second

@ApiTags('Coin Lock')
@Controller('coin-lock')
export class CoinLockController {
  constructor(private readonly coinLockService: CoinLockService) {}

  @Get('')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get list of coin locks for the authenticated user',
  })
  async getLockerList(
    @UserAddress() address: string,
    @Query() query: PaginationDto,
  ) {
    return this.coinLockService.findByLocker(address, query);
  }

  @Get('address/:tokenAddress')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({
    summary: 'Get total locked amount by token address',
  })
  @ApiParam({ name: 'tokenAddress', description: 'Token address' })
  async getTotalLockedAmount(@Param('tokenAddress') tokenAddress: string) {
    const totalLockedAmount =
      await this.coinLockService.getTotalLockedAmountByTokenAddress(
        tokenAddress,
      );

    return plainToInstance(
      TotalLockedAmountResponseDto,
      {
        tokenAddress,
        totalLockedAmount,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  @Get('address/:tokenAddress/list')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiOperation({
    summary: 'Get list coin locked by token address',
  })
  @ApiParam({ name: 'tokenAddress', description: 'Token address' })
  async getLockedList(
    @Param('tokenAddress') tokenAddress: string,
    @Query() query: PaginationDto,
  ) {
    const lockedList = await this.coinLockService.getLockedListByTokenAddress(
      tokenAddress,
      query,
    );

    return plainToInstance(CoinLockListResponseDto, lockedList, {
      excludeExtraneousValues: true,
    });
  }

  @Get(':contractId')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(CACHE_TTL)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get details of a specific coin lock for the authenticated user',
  })
  @ApiParam({ name: 'contractId', description: 'Contract lock ID' })
  async getLockerDetail(
    @UserAddress() address: string,
    @Param('contractId') contractId: string,
  ) {
    const coinLock = this.coinLockService.findOneByLockerAndContractId(
      address,
      contractId,
    );

    return plainToInstance(CoinLockResponseDto, coinLock, {
      excludeExtraneousValues: true,
    });
  }
}
