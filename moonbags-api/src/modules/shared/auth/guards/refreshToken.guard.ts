import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class RefreshTokenGuard extends AuthGuard('jwt-refresh') {
  constructor(
    private readonly jwtService: JwtService,
    private configService: ConfigService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const refreshToken = request.headers.authorization.split('Bearer ')[1];
    if (!refreshToken) {
      throw new UnauthorizedException('Refresh token is missing');
    }
    try {
      const payload = await this.jwtService.verify(refreshToken, {
        secret: this.configService.get('auth.refreshTokenSecret'),
      });
      request['user'] = payload;
      return request;
    } catch (error) {
      console.error('Error verifying token:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }
}
