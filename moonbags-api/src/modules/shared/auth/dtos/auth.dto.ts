import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { JwtPayload } from '@shares/dtos/jwt-payload.dto';
import { User } from '../schemas/user.schema';

export class LoginRequestDto {
  @Expose()
  @ApiProperty({
    example:
      '4/0AfJohXkgOVdwSMIbEvD9AjKvn3c8T6GYFHMnfnIiD9F_XYxo71pUDPYjCRbZnzQYtcvZ4g',
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  signMessage: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  signature: string;
}

export class LoginResponseDto {
  accessToken: string;
  refreshToken: string;
  user: User | JwtPayload;
}

export class RefreshTokenResponseDto {
  @ApiProperty()
  @Expose()
  accessToken: string;

  @ApiProperty()
  @Expose()
  refreshToken: string;

  @ApiProperty()
  @Expose()
  expTime: number;
}
