import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import {
  ExtractJwt,
  Strategy,
  StrategyOptionsWithoutRequest,
  StrategyOptionsWithRequest,
} from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { JwtPayload } from '@shares/dtos/jwt-payload.dto';

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  constructor(private readonly configService: ConfigService) {
    const options: StrategyOptionsWithRequest | StrategyOptionsWithoutRequest =
      {
        jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
        ignoreExpiration: false,
        secretOrKey: configService.get('auth.refreshTokenSecret'),
      };
    super(options);
  }

  async validate(payload: JwtPayload) {
    return {
      address: payload.address,
    };
  }
}
