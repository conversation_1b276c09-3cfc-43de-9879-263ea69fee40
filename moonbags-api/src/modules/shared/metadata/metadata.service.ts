import { RedisService } from '@liaoliaots/nestjs-redis';
import { BadRequestException, Injectable } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class MetadataService {
  private readonly redis: Redis | null;

  constructor(private readonly redisService: RedisService) {
    this.redis = this.redisService.getOrThrow();
  }

  async getHypeTokenUsd(): Promise<{ priceUsd: string }> {
    const hypePriceUsd = await this.redis.get('moonbags:hype:price_usd');

    if (!hypePriceUsd) {
      throw new BadRequestException('Hype price not found');
    }

    return { priceUsd: hypePriceUsd };
  }
}
