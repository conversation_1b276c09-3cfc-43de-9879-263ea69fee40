import { Injectable, Logger } from '@nestjs/common';
import { Emitter } from '@socket.io/redis-emitter';
import { createClient } from 'redis';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class EmitterService {
  public io;
  private logger: Logger;

  constructor(private readonly configService: ConfigService) {
    this.logger = new Logger(EmitterService.name);
    const redisHost = this.configService.getOrThrow('REDIS_HOST');
    const redisPort = this.configService.getOrThrow('REDIS_PORT');
    const redisPassword = this.configService.getOrThrow('REDIS_PASSWORD');
    const redisClient = createClient({
      url: `redis://${redisHost}:${redisPort}`,
      password: redisPassword,
    });
    redisClient.connect().catch((err) => {
      this.logger.error('Redis connection error:', err);
    });

    redisClient.on('error', (err) => {
      this.logger.error('Redis client error:', err);
    });

    redisClient.on('connect', () => {
      this.logger.log('Redis client connected');
      this.io = new Emitter(redisClient);
    });
  }

  public async sendToBroadcast<T>(event: string, data: T): Promise<void> {
    return await this.io.emit(event, data);
  }

  public async sendToRoom<T>(
    room: string,
    event: string,
    data: T,
  ): Promise<void> {
    return await this.io.to(room).emit(event, data);
  }
}
