import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';

export type EventLogDocument = EventLog & Document;

@Schema({ timestamps: true, collection: 'event_logs' })
export class EventLog extends Document {
  @Prop({ required: true })
  txHash: string;

  @Prop({ required: true, type: [String] })
  topics: string[];

  @Prop({ required: true })
  data: string;

  @Prop({ required: true })
  blockNumber: number;

  @Prop({ required: true })
  blockTimestamp: number;

  @Prop({ required: true })
  logIndex: number;

  @Prop({ required: true })
  eventName: string;
}

export const EventLogSchema = SchemaFactory.createForClass(EventLog);

EventLogSchema.plugin(mongoosePaginate);
EventLogSchema.index({ txHash: 1, logIndex: 1 }, { unique: true, background: true });
EventLogSchema.index({ blockNumber: 1 }, { background: true });
EventLogSchema.index({ eventName: 1, blockNumber: 1 }, { background: true });
EventLogSchema.index({ blockTimestamp: 1 }, { background: true });
