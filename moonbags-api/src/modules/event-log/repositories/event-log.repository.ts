import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { EventLog, EventLogDocument } from '../schemas/event-log.schema';

@Injectable()
export class EventLogRepository extends BaseRepository<EventLog> {
  constructor(
    @InjectModel(EventLog.name) model: PaginateModel<EventLogDocument>,
  ) {
    super(model);
  }

  async findByTxHash(txHash: string) {
    return this.model.find({ txHash }).sort({ logIndex: 1 });
  }

  async findByBlockNumber(blockNumber: number) {
    return this.model.find({ blockNumber }).sort({ logIndex: 1 });
  }

  async findByEventName(eventName: string, limit?: number) {
    const query = this.model.find({ eventName }).sort({ blockNumber: -1 });
    if (limit) {
      query.limit(limit);
    }
    return query;
  }

  async findByBlockRange(fromBlock: number, toBlock: number) {
    return this.model
      .find({
        blockNumber: { $gte: fromBlock, $lte: toBlock }
      })
      .sort({ blockNumber: 1, logIndex: 1 });
  }
}
