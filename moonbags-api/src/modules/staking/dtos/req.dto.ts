import { ApiPropertyOptional } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { toChecksumAddress, validateEvmAddress } from '@shares/utils/evm-utils';

export class StakingUserQueryDto {
  @ApiPropertyOptional({ default: 1 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ default: 10 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (!value) return value;
    const trimmed = value.trim();
    if (!validateEvmAddress(trimmed)) {
      throw new Error(`Invalid EVM address: ${trimmed}`);
    }
    return toChecksumAddress(trimmed);
  })
  stakingPoolAddress?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (!value) return value;
    const trimmed = value.trim();
    if (!validateEvmAddress(trimmed)) {
      throw new Error(`Invalid EVM address: ${trimmed}`);
    }
    return toChecksumAddress(trimmed);
  })
  stakingCoinAddress?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return [];

    let addresses: string[];
    if (Array.isArray(value)) {
      addresses = value.map((item) => item.trim());
    } else {
      addresses = [value?.trim()];
    }

    // Validate and convert each address to checksum format
    return addresses.map((address) => {
      if (!address) return address;
      if (!validateEvmAddress(address)) {
        throw new Error(`Invalid EVM address: ${address}`);
      }
      return toChecksumAddress(address);
    });
  })
  userAddresses?: string[];
}
