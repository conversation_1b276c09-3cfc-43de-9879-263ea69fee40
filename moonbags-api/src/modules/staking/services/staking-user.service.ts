import { StakingUserQueryDto } from '@modules/staking/dtos/req.dto';
import { StakingUserRepository } from '@modules/staking/repositories';
import { StakingUserDocument } from '@modules/staking/schemas';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PaginateResult } from 'mongoose';
import { Decimal128 } from 'bson';
import { ethers } from 'ethers';
import { formatHypeAmount, retryAsync } from '@shares/utils/evm-utils';
import { evmProvider } from '@shares/utils/evm-provider';

const MULTIPLIER = BigInt('1000000000000000000'); // 1e18 as BigInt to match contract
const COIN_DECIMALS = 6;
const HYPE_DECIMALS = 18; // EVM standard decimals for HYPE token

// MoonbagsStake contract ABI - only the functions we need
const MOONBAGS_STAKE_ABI = [
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "stakingToken",
        "type": "address"
      },
      {
        "internalType": "address",
        "name": "staker",
        "type": "address"
      }
    ],
    "name": "getStakingAccountInfo",
    "outputs": [
      {
        "internalType": "uint256",
        "name": "balance",
        "type": "uint256"
      },
      {
        "internalType": "uint256",
        "name": "rewardIndex",
        "type": "uint256"
      },
      {
        "internalType": "uint256",
        "name": "earned",
        "type": "uint256"
      },
      {
        "internalType": "uint256",
        "name": "unstakeDeadline",
        "type": "uint256"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "stakingToken",
        "type": "address"
      }
    ],
    "name": "getStakingPoolInfo",
    "outputs": [
      {
        "internalType": "address",
        "name": "initializer",
        "type": "address"
      },
      {
        "internalType": "uint256",
        "name": "totalSupply",
        "type": "uint256"
      },
      {
        "internalType": "uint256",
        "name": "rewardIndex",
        "type": "uint256"
      },
      {
        "internalType": "uint256",
        "name": "pendingInitialRewards",
        "type": "uint256"
      },
      {
        "internalType": "uint256",
        "name": "totalRewards",
        "type": "uint256"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

@Injectable()
export class StakingUserService {
  constructor(
    private readonly stakingUserRepository: StakingUserRepository,
    private readonly configService: ConfigService,
  ) {}

  async paginateByUserAddress(
    userAddress: string,
    query: StakingUserQueryDto,
  ): Promise<PaginateResult<StakingUserDocument>> {
    const {
      page = 1,
      limit = 10,
      stakingPoolAddress,
      stakingCoinAddress,
    } = query;

    return this.stakingUserRepository.paginate(
      {
        userAddress,
        ...(stakingPoolAddress && { stakingPoolAddress }),
        ...(stakingCoinAddress && { stakingCoinAddress }),
      },
      {
        page,
        limit,
        sort: { createdAt: -1 },
      },
    );
  }

  async getPublicListStakingUserByCoinAddress(
    stakingCoinAddress: string,
    query: StakingUserQueryDto,
  ) {
    const { page = 1, limit = 10 } = query;

    return this.stakingUserRepository.paginate(
      {
        ...(stakingCoinAddress && {
          stakingCoinAddress,
        }),
        ...(query.userAddresses && {
          userAddress: { $in: query.userAddresses },
        }),
        stakedAmount: { $gt: Decimal128.fromString('0') },
      },
      {
        page,
        limit,
        sort: { stakedAmount: -1 },
      },
    );
  }

  async getStakingUserByStakingCoinAddress(
    userAddress: string,
    stakingCoinAddress: string,
  ) {
    const stakingUser = await this.stakingUserRepository.findOne({
      userAddress,
      stakingCoinAddress,
    });

    if (!stakingUser) {
      throw new NotFoundException(
        `Staking user ${userAddress} not found for coin ${stakingCoinAddress}`,
      );
    }

    return stakingUser;
  }

  async getTotalDistributedAmountForUser(stakingUser: StakingUserDocument) {
    try {
      const stakingUserAddress = stakingUser.userAddress;
      const stakingCoinAddress = stakingUser.stakingCoinAddress;

      // Get the MoonbagsStake contract address from config
      const moonbagsStakeAddress = this.configService.get<string>('app.moonbagsStakeAddress');

      if (!moonbagsStakeAddress || moonbagsStakeAddress === '******************************************') {
        console.warn('MoonbagsStake contract address not configured');
        return '0';
      }

      // Create contract instance
      const contract = new ethers.Contract(
        moonbagsStakeAddress,
        MOONBAGS_STAKE_ABI,
        evmProvider
      );

      // Get staking account info and pool info with retry logic
      const [accountInfo, poolInfo] = await retryAsync(async () => {
        const accountInfoPromise = contract.getStakingAccountInfo(stakingCoinAddress, stakingUserAddress);
        const poolInfoPromise = contract.getStakingPoolInfo(stakingCoinAddress);
        return Promise.all([accountInfoPromise, poolInfoPromise]);
      }, 3, 1000);

      // Extract values from the results
      const [balance, accountRewardIndex, earned] = accountInfo;
      const [, , poolRewardIndex] = poolInfo;

      // Calculate pending rewards manually (same logic as _calculateRewards in the contract)
      let pendingRewards = 0n;
      if (balance > 0n) {
        const rewardDiff = poolRewardIndex - accountRewardIndex;
        pendingRewards = (balance * rewardDiff) / MULTIPLIER;
      }

      // Total earned = already earned + pending rewards
      const totalEarned = earned + pendingRewards;

      // Format the result from wei to HYPE (18 decimals)
      const formattedAmount = formatHypeAmount(totalEarned);

      return formattedAmount;

    } catch (error) {
      console.error(
        `Error get total distributed amount for user ${stakingUser.userAddress}`,
        error?.message,
      );
      return '0';
    }
  }
}
