import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { TreasuryService } from './services/treasury.service';
import { NavListResponseDto } from './dtos/nav.dto';
import { ListAssetDto } from './dtos/asset.dto';
import { ConfigService } from '@nestjs/config';
@ApiTags('Treasury')
@Controller('treasury')
export class TreasuryController {
  constructor(
    private readonly treasuryService: TreasuryService,
    private readonly configService: ConfigService,
  ) {}

  @Get('')
  async getTreasuryAssets(
    @Query() input: ListAssetDto,
  ): Promise<NavListResponseDto> {
    const SHRO_TREASURY_ADDRESS = this.configService.get<string>(
      'app.shroTreasuryAddress',
    );
    return this.treasuryService.getAssets(SHRO_TREASURY_ADDRESS, input);
  }
}
