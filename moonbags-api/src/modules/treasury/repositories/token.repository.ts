import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { Token, TokenDocument } from '../schemas/token.schema';

@Injectable()
export class TokenRepository extends BaseRepository<Token> {
  constructor(@InjectModel(Token.name) model: PaginateModel<TokenDocument>) {
    super(model);
  }

  async getTokensToUpdatePrice(limit: number = 10) {
    return this.model.find({}, {}, { sort: { updatedAt: 1 }, limit });
  }

  async saveMany(docs: Token[]) {
    const bulkOps = docs.map((doc) => ({
      updateOne: {
        filter: {
          address: doc.address,
        },
        update: { $set: doc },
        upsert: true,
      },
    }));

    return await this.model.bulkWrite(bulkOps);
  }

  async updatePrice(
    tokenAddress: string,
    { price, priceUsd }: { price: number; priceUsd: number },
  ) {
    return this.model.findOneAndUpdate(
      { address: tokenAddress },
      { price, priceUsd },
      { upsert: true },
    );
  }

  async upsertToken(
    address: string,
    name: string,
    symbol: string,
    decimals: number,
    image?: string,
  ) {
    return this.model.findOneAndUpdate(
      { address },
      {
        name,
        symbol,
        decimals,
        image: image ?? null,
      },
      {
        upsert: true,
      },
    );
  }
}
