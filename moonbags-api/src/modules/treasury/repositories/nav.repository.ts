import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { Nav, NavDocument } from '../schemas/nav.schema';

@Injectable()
export class NavRepository extends BaseRepository<Nav> {
  constructor(@InjectModel(Nav.name) model: PaginateModel<NavDocument>) {
    super(model);
  }

  async saveMany(docs: Nav[]) {
    const bulkOps = docs.map((doc) => ({
      updateOne: {
        filter: {
          treasuryAddress: doc.treasuryAddress,
          tokenAddress: doc.tokenAddress,
        },
        update: { $set: doc },
        upsert: true,
      },
    }));

    return await this.model.bulkWrite(bulkOps);
  }

  async upsertAmount(
    treasuryAddress: string,
    tokenAddress: string,
    amount: number,
  ) {
    return this.model.findOneAndUpdate(
      {
        treasuryAddress,
        tokenAddress,
      },
      {
        $inc: { amount },
      },
      {
        upsert: true,
        returnDocument: 'before',
      },
    );
  }
}
