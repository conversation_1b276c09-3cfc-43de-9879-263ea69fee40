import { SharedModule } from '@modules/shared/shared.module';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TreasuryController } from './treasury.controller';
import { TreasuryService } from './services/treasury.service';
import { NavRepository } from './repositories/nav.repository';
import { TokenService } from './services/token.service';
import { TokenRepository } from './repositories/token.repository';
import { Token, TokenSchema } from './schemas/token.schema';
import { Nav, NavSchema } from './schemas/nav.schema';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeature([
      { name: Token.name, schema: TokenSchema },
      { name: Nav.name, schema: NavSchema },
    ]),
  ],
  controllers: [TreasuryController],
  providers: [TreasuryService, TokenService, NavRepository, TokenRepository],
  exports: [TreasuryService, TokenService, NavRepository, TokenRepository],
})
export class TreasuryModule {}
