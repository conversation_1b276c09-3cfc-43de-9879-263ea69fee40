import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class NavItemDto {
  @Expose()
  @ApiProperty()
  tokenAddress: string;

  @Expose()
  @ApiProperty()
  @Type(() => String)
  amount: string;

  @Expose()
  @ApiProperty()
  @Type(() => String)
  marketValue: string;

  @Expose()
  @ApiProperty()
  treasuryAddress: string;

  @Expose()
  @ApiProperty()
  tokenName: string;

  @Expose()
  @ApiProperty()
  tokenSymbol: string;

  @Expose()
  @ApiProperty()
  tokenImage: string;
}

export class NavListResponseDto {
  @Expose()
  @ApiProperty({ type: [NavItemDto] })
  @Type(() => NavItemDto)
  docs: NavItemDto[];

  @Expose()
  @ApiProperty()
  totalDocs: number;

  @Expose()
  @ApiProperty()
  limit: number;

  @Expose()
  @ApiProperty()
  totalPages: number;

  @Expose()
  @ApiProperty()
  page: number;

  @Expose()
  @ApiProperty()
  totalNav: string;

  @Expose()
  @ApiProperty()
  treasuryAddress: string;
}
