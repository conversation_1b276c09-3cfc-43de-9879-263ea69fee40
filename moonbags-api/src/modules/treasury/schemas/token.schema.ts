import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Decimal128 } from 'bson';

export type TokenDocument = Token & Document;

export type TToken = {
  address: string;
  name?: string;
  decimals?: number;
  symbol?: string;
  image?: string;
};

@Schema({ timestamps: true, collection: 'tokens', _id: false })
export class Token extends Document {
  @Prop({ required: true, unique: true })
  address: string;

  @Prop({ required: false })
  name?: string;

  @Prop({ required: false })
  symbol?: string;

  @Prop({ required: false })
  decimals?: number;

  @Prop({ required: false })
  image?: string;

  @Prop({ required: true, default: new Decimal128('0') })
  price: Decimal128;

  @Prop({ required: true, default: new Decimal128('0') })
  priceUsd: Decimal128;
}

export const TokenSchema = SchemaFactory.createForClass(Token);

TokenSchema.index({ updatedAt: 1, address: 1 });
TokenSchema.plugin(mongoosePaginate);
