import { config } from 'dotenv';
config();

import { CoinModule } from '@modules/coin/coin.module';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import { EmitterService } from '@modules/shared/emitter/emitter.service';
import { SharedModule } from '@modules/shared/shared.module';
import { Injectable, Logger, Module } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { InjectConnection } from '@nestjs/mongoose';
import { SocketEvent } from '@shares/enums/event.enum';
import { parseHypeAmount } from '@shares/utils/evm-utils';
import { toNumberBN } from '@shares/utils/number.utils';
import { Connection } from 'mongoose';
import { MongoUtils } from 'src/utils/mongo.utils';

const KING_OF_HILL_THRESHOLD = process.env.KING_OF_HILL_THRESHOLD || 2000;

@Injectable()
class KingOfTheHillUpdateProcessor {
  private readonly logger: Logger;
  constructor(
    private readonly coinRepository: CoinRepository,
    private readonly emitterService: EmitterService,
    @InjectConnection() private readonly connection: Connection,
  ) {
    this.logger = new Logger(KingOfTheHillUpdateProcessor.name);
    this.logger.log(`KING_OF_HILL_THRESHOLD: ${KING_OF_HILL_THRESHOLD}`);
  }

  async updateKingOfTheHill() {
    const kingOfTheHillThreshold = parseHypeAmount(
      KING_OF_HILL_THRESHOLD.toString(),
    ).toString();
    console.log('kingOfTheHillThreshold', kingOfTheHillThreshold);
    const coin = await this.coinRepository.aggregate([
      {
        $match: {
          realHypeReserves: { $gte: toNumberBN(kingOfTheHillThreshold) },
          prevRealHypeReserves: { $lt: toNumberBN(kingOfTheHillThreshold) },
          kingAt: { $exists: false },
          $or: [{ isKing: { $exists: false } }, { isKing: { $eq: false } }],
        },
      },
      {
        $sort: { updatedAt: -1, _id: -1 },
      },
      {
        $limit: 1,
      },
    ]);

    console.log('coin King of the Hill', coin);

    if (!coin || !coin.length) {
      this.logger.log('No new King of the Hill found.');
      return;
    }
    const newKingCoin = coin[0];
    try {
      await MongoUtils.withTransaction(this.connection, async (session) => {
        await this.coinRepository.updateMany(
          { isKing: true },
          { $set: { isKing: false } },
          { session },
        );
        await this.coinRepository.updateOne(
          { tokenAddress: newKingCoin.tokenAddress },
          { $set: { isKing: true, kingAt: new Date() } },
          { session },
        );
        await this.emitterService.sendToBroadcast(
          SocketEvent.NEW_KING_OF_THE_HILL,
          {
            data: true,
          },
        );
        this.logger.log(
          `New King of the Hill: ${newKingCoin.name} (${newKingCoin.tokenAddress})`,
        );
      });
    } catch (error) {
      this.logger.log('Update King of the Hill failed:', error);
      throw error;
    }
  }
}

@Module({
  imports: [CoinModule, SharedModule],
  providers: [KingOfTheHillUpdateProcessor],
})
class KingOfTheHillUpdateModule {}

async function bootstrap() {
  const app = await NestFactory.create(KingOfTheHillUpdateModule);
  const updateKingOfTheHillProcessor = app.get(KingOfTheHillUpdateProcessor);
  while (true) {
    await updateKingOfTheHillProcessor.updateKingOfTheHill();
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }
}
bootstrap();
