import { SharedModule } from '@modules/shared/shared.module';
import { Module, Injectable, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { BaseConsumer } from 'src/shares/base.consumer';
import { KafkaMessage } from 'kafkajs';
import { CoinModule } from '@modules/coin/coin.module';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import * as _ from 'lodash';
import { ConfigService } from '@nestjs/config';
import { KafkaTopic } from 'src/shares/enums/kafka.enum';

interface TradeMessage {
  checkpoint: number;
  timestamp: number;
  hash: string;
  tokenAddress: string;
  tokenSymbol: string;
  index: number;
  maker: string;
  tradeType: string;
  baseAmount: string;
  quoteAmount: string;
  price: string;
  priceUsd: string;
  volume: string;
  volumeUsd: string;
  virtualHypeReserves: string;
  virtualTokenReserves: string;
  realHypeReserves: string;
  realTokenReserves: string;
  fee: string;
}

@Injectable()
export class CoinConsumerTrade extends BaseConsumer {
  constructor(
    private readonly coinRepository: CoinRepository,
    configService: ConfigService,
  ) {
    super(configService);
    this.logger = new Logger(CoinConsumerTrade.name);
  }

  subscribeTopic(): string | RegExp {
    return KafkaTopic.TRADE_CREATED;
  }

  async handleBatch(messages: KafkaMessage[]) {
    // Group messages by tokenAddress
    const groupedMessages = _.groupBy(
      messages.map((message) => {
        return JSON.parse(message.value.toString()) as TradeMessage;
      }),
      'tokenAddress',
    );

    // For each tokenAddress, sort by timestamp and get latest message
    for (const [tokenAddress, trades] of Object.entries(groupedMessages)) {
      const sortedTrades = _.orderBy(trades, ['timestamp'], ['desc']);
      const latestTrade = sortedTrades[0];

      const result = await this.coinRepository.updateOne(
        { tokenAddress },
        {
          $set: {
            lastTrade: latestTrade.timestamp,
          },
        },
      );

      // Log success or failure
      this.logger.log(
        `Updated coin ${tokenAddress} with latest trade: ${latestTrade.hash} ${
          result ? 'successfully' : 'failed'
        } `,
      );
    }
  }
}

@Module({
  imports: [SharedModule, CoinModule],
  providers: [CoinConsumerTrade],
})
export class AppModule {}

const run = async () => {
  const app = await NestFactory.create(AppModule);
  const consumer = app.get(CoinConsumerTrade);
  await consumer.run();
};

run().catch((error) => {
  console.error(error.message);
  console.error(
    `Something went wrong. Process will be restart shortly... with error:${error.toString()}`,
  );
  process.exit(1);
});
