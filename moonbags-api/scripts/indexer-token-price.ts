import * as dotenv from 'dotenv';
dotenv.config();
import { NestFactory } from '@nestjs/core';
import { Injectable, Module } from '@nestjs/common';
import { Redis } from 'ioredis';
import { SharedModule } from '@modules/shared/shared.module';
import { toChecksumAddress } from '@shares/utils/evm-utils';
import { TokenRepository } from '@modules/treasury/repositories/token.repository';
import axios from 'axios';
import { sleep } from '@shares/utils/common.utils';
import { TreasuryModule } from '@modules/treasury/treasury.module';
import { NavRepository } from '@modules/treasury/repositories/nav.repository';
@Injectable()
export class IndexerPrice {
  private readonly redisClient: Redis;
  constructor(
    private readonly tokenRepository: TokenRepository,
    private readonly navRepository: NavRepository,
  ) {}

  async run() {
    while (true) {
      const tokens = await this.tokenRepository.getTokensToUpdatePrice();
      for (const token of tokens) {
        console.log(`Updating price for token ${token.address}`);
        try {
          const data = await this.getExternalPrice(
            toChecksumAddress(token.address),
          );
          await Promise.all([
            this.tokenRepository.updatePrice(token.address, {
              price: data.price,
              priceUsd: data.priceUsd,
            }),
            this.updateNavForToken(token.address, data.priceUsd),
          ]);
        } catch (error) {
          console.error(
            `Fetch external price error: ${error?.message} for token ${token.address}`,
          );
        }
      }
      console.log('Sleep 5 seconds to next tick');
      await sleep(5000);
    }
  }

  async getExternalPrice(tokenAddress: string) {
    const response = await axios.get(
      `https://api.raidenx.io/api/v1/sui/tokens/${tokenAddress}`,
    );
    return response.data;
  }

  private async updateNavForToken(tokenAddress: string, priceUsd: string) {
    return await this.navRepository.updateMany({ tokenAddress }, [
      {
        $set: {
          marketValue: {
            $toDecimal: {
              $multiply: [{ $toDecimal: '$amount' }, { $toDecimal: priceUsd }],
            },
          },
        },
      },
    ]);
  }
}

@Module({
  imports: [SharedModule, TreasuryModule],
  providers: [IndexerPrice],
  controllers: [],
})
class IndexerModule {}

async function bootstrap() {
  const app = await NestFactory.create(IndexerModule);
  await app.init();
  const indexerPrice = app.get(IndexerPrice);
  await indexerPrice.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
