/* eslint-disable @typescript-eslint/no-unused-vars */
import * as dotenv from 'dotenv';
dotenv.config();
import { NestFactory } from '@nestjs/core';
import { Injectable, Module } from '@nestjs/common';
import { SharedModule } from '@modules/shared/shared.module';
import { sleep } from '@shares/utils/common.utils';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import { Coin } from '@modules/coin/schemas/coin.schema';
import { CoinModule } from '@modules/coin/coin.module';
import { ConfigService } from '@nestjs/config';

const MAX_HOLDER_BREAKOUT = 100;
const MAX_VOLUME_24H_BREAKOUT = 50000;
const MAX_MCAP_USD_BREAKOUT = 25000;
const MAX_AGE_BREAKOUT = 2;

@Injectable()
export class CalculateCoinScore {
  constructor(
    private readonly coinRepository: CoinRepository,
    private readonly configService: ConfigService,
  ) {}

  async run() {
    while (true) {
      const newCoins = await this.coinRepository.model
        .find({
          $or: [{ scoreUpdatedAt: { $exists: false } }, { scoreUpdatedAt: 0 }],
          tokenAddress: {
            $ne: this.configService.get('app.shroTokenAddress'),
          },
        })
        .sort({ _id: 1 })
        .limit(100);
      await this.updateScoreForListCoin(newCoins);

      const coins = await this.coinRepository.model
        .find({
          $or: [
            { raidenxPairId: { $exists: true, $ne: null } },
            { raidenxCetusPairId: { $exists: true, $ne: null } },
          ],
        })
        .sort({ scoreUpdatedAt: 1, _id: 1 })
        .limit(200);

      await this.updateScoreForListCoin(coins);
      console.log('Sleep 5 seconds to next tick');
      await sleep(5000);
    }
  }

  private async updateScoreForListCoin(coins: Coin[]) {
    for (const coin of coins) {
      let score = await this.calculateScore(coin);
      if (coin.isBoostedUntil && new Date(coin.isBoostedUntil) > new Date()) {
        score = score + 30; // boost score 30 points
      }
      await this.coinRepository.updateOne(
        { _id: coin._id },
        { $set: { score, scoreUpdatedAt: Date.now() } },
      );
      await sleep(100);
    }
  }

  private async calculateScore(coin: Coin) {
    const holderPoint = await this.calculateHolderPoint(coin);
    const volume24hPoint = await this.calculateVolume24hPoint(coin);
    const mcapUsdPoint = await this.calculateMcapUsdPoint(coin);
    const agePoint = await this.calculateAgePoint(coin);
    const bondingPoint = await this.calculateBondingPoint(coin);

    const score =
      holderPoint + volume24hPoint + mcapUsdPoint + agePoint + bondingPoint;
    return score;
  }

  private async calculateBondingPoint(coin: Coin) {
    const bondingCurve = Number(coin.bondingCurve);

    if (bondingCurve >= 100 || !!coin.listedAt) {
      return 2;
    }

    const pointRanges = [
      { min: 80, max: 100, point: 10 },
      { min: 50, max: 80, point: 8 },
      { min: 30, max: 50, point: 6 },
      { min: 10, max: 30, point: 4 },
      { min: 0, max: 10, point: 2 },
    ];

    for (const range of pointRanges) {
      if (bondingCurve >= range.min && bondingCurve < range.max) {
        return range.point;
      }
    }

    return 0;
  }

  private async calculateHolderPoint(coin: Coin) {
    if (
      !!coin.listedAt &&
      Number(coin.totalHolder || 0) >= MAX_HOLDER_BREAKOUT
    ) {
      return 4;
    }
    const totalHolder = coin.totalHolder || 0;
    const pointRanges = [
      { min: MAX_HOLDER_BREAKOUT, max: Infinity, point: 5 },
      { min: 50, max: 100, point: 4 },
      { min: 20, max: 50, point: 3 },
      { min: 10, max: 20, point: 2 },
      { min: 0, max: 10, point: 1 },
    ];

    for (const range of pointRanges) {
      if (totalHolder > range.min && totalHolder <= range.max) {
        return range.point;
      }
    }

    return 0;
  }

  private async calculateVolume24hPoint(coin: Coin) {
    if (
      !!coin.listedAt &&
      Number(coin.volumeUsd24h || 0) >= MAX_VOLUME_24H_BREAKOUT
    ) {
      return 4;
    }

    const volume24h = Number(coin.volumeUsd24h || 0);
    const pointRanges = [
      { min: MAX_VOLUME_24H_BREAKOUT, max: Infinity, point: 5 },
      { min: 15000, max: MAX_VOLUME_24H_BREAKOUT, point: 4 },
      { min: 10000, max: 15000, point: 3 },
      { min: 5000, max: 10000, point: 2 },
      { min: 0, max: 5000, point: 1 },
    ];

    for (const range of pointRanges) {
      if (volume24h > range.min && volume24h <= range.max) {
        return range.point;
      }
    }

    return 0;
  }

  private async calculateMcapUsdPoint(coin: Coin) {
    if (!!coin.listedAt) {
      return 3;
    }

    const mcapUsd = Number(coin.mcapUsd || 0);
    const pointRanges = [
      { min: MAX_MCAP_USD_BREAKOUT, max: Infinity, point: 5 },
      { min: 15000, max: MAX_MCAP_USD_BREAKOUT, point: 4 },
      { min: 10000, max: 15000, point: 3 },
      { min: 5000, max: 10000, point: 2 },
      { min: 0, max: 5000, point: 1 },
    ];

    for (const range of pointRanges) {
      if (mcapUsd > range.min && mcapUsd <= range.max) {
        return range.point;
      }
    }

    return 0;
  }

  private async calculateAgePoint(coin: Coin) {
    const createdAt = coin.createdAt;
    if (!createdAt) {
      return 0;
    }
    const now = new Date();
    const diffInSeconds = Math.floor(
      (now.getTime() - createdAt.getTime()) / 1000,
    );
    const MAX_AGE_BREAKOUT_SECONDS = MAX_AGE_BREAKOUT * 24 * 60 * 60; // 2 days in seconds

    const pointRanges = [
      { min: 0, max: 0.02, point: 50 },
      { min: 0.02, max: 0.04, point: 10 },
      { min: 0.04, max: 0.2, point: 5 },
      { min: 0.2, max: 0.8, point: 3 },
      { min: 0.8, max: Infinity, point: 1 },
    ];

    const ageRatio = diffInSeconds / MAX_AGE_BREAKOUT_SECONDS;

    for (const range of pointRanges) {
      if (ageRatio >= range.min && ageRatio < range.max) {
        return range.point;
      }
    }

    return 0;
  }
}

@Module({
  imports: [SharedModule, CoinModule],
  providers: [CalculateCoinScore],
  controllers: [],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.init();
  const indexer = app.get(CalculateCoinScore);
  await indexer.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
