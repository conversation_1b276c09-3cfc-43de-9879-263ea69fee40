/* eslint-disable @typescript-eslint/no-unused-vars */
import * as dotenv from 'dotenv';
dotenv.config();
import { NestFactory } from '@nestjs/core';
import { Injectable, Module } from '@nestjs/common';
import { SharedModule } from '@modules/shared/shared.module';
import { sleep } from '@shares/utils/common.utils';
import { TreasuryModule } from '@modules/treasury/treasury.module';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import { Coin } from '@modules/coin/schemas/coin.schema';
import axios from 'axios';
import { CoinModule } from '@modules/coin/coin.module';
import moment from 'moment';

const RAIDENX_API_URL = process.env.RAIDENX_API_URL || 'https://api.raidenx.io';
const COINDETAIL_API_URL = `${RAIDENX_API_URL}/api/v1/sui/pairs/$pairId`;

@Injectable()
export class IndexerTotalHolder {
  constructor(private readonly coinRepository: CoinRepository) {}

  async run() {
    while (true) {
      const coins = await this.coinRepository.model
        .find({
          $or: [
            { raidenxPairId: { $exists: true, $ne: null } },
            { raidenxCetusPairId: { $exists: true, $ne: null } },
          ],
          tokenAddress: {
            $nin: [
              '0x490c8968eb4bde853178f9d90b2755b90b9226c499412b8cd39aaa211b1c6d32::vanis::VANIS',
            ],
          },
        })
        .sort({ raidenxUpdatedAt: 1, _id: 1 })
        .limit(1000);
      for (const coin of coins) {
        await this.processCoin(coin);
        await sleep(200);
      }

      console.log('Sleep 30 seconds to next tick');
      await sleep(30000);
    }
  }

  private async processCoin(coin: Coin) {
    try {
      const pairId = coin?.listedAt
        ? coin.raidenxCetusPairId
        : coin.raidenxPairId;

      if (!pairId) {
        console.log(`Coin ${coin.tokenAddress} has no pairId in raidenx`);
        return;
      }
      const url = COINDETAIL_API_URL.replace('$pairId', pairId);
      console.log(`Processing coin: ${coin.tokenAddress} with url: ${url}`);

      const response = await axios.get(url);
      const data = response.data;
      let volumeUsd24h = data?.stats?.volume?.['24h'] || 0;
      const updatedAt = data?.updatedAt;
      if (updatedAt) {
        // Parse the updatedAt string properly
        const parsedUpdatedAt = moment(updatedAt, 'YYYY-MM-DD H:mm:ss.SSS Z');
        const twentyFourHoursAgo = moment().subtract(24, 'hours');

        if (parsedUpdatedAt.isBefore(twentyFourHoursAgo)) {
          volumeUsd24h = 0;
        }
      }
      await this.coinRepository.updateRaidenxData(
        coin.tokenAddress,
        data.totalHolders || 0,
        data.marketCapUsd || 0,
        volumeUsd24h,
      );
    } catch (error: any) {
      console.error(
        `Error processing coin: ${coin.tokenAddress}`,
        error?.message,
      );
    }
  }
}

@Module({
  imports: [SharedModule, TreasuryModule, CoinModule],
  providers: [IndexerTotalHolder],
  controllers: [],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.init();
  const indexer = app.get(IndexerTotalHolder);
  await indexer.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
