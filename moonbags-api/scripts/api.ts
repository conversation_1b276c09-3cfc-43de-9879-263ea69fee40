import { config } from 'dotenv';
config();

import { NestFactory } from '@nestjs/core';
import { AppModule } from 'src/app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder } from '@nestjs/swagger';
import { SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
// import { AppThrottlerGuard } from 'src/shares/guards';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // const throttlerGuard = app.get(AppThrottlerGuard);
  // app.useGlobalGuards(throttlerGuard);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );
  const configService = app.get(ConfigService);
  const prefix = configService.get('app.prefix');

  app.setGlobalPrefix(prefix);
  app.enableCors();

  const appName = 'moonbags';
  const options = new DocumentBuilder()
    .addBearerAuth()
    .setTitle(appName)
    .setDescription(appName)
    .setVersion(prefix)
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(`${prefix}/docs`, app, document, {
    customSiteTitle: appName,
    swaggerOptions: {
      docExpansion: 'list',
      filter: true,
      displayRequestDuration: true,
      persistAuthorization: true,
    },
  });
  const port = configService.get('app.port');

  await app.listen(port, () => {
    console.log(`Server running on http://localhost:${port}`);
    console.log(`swagger: http://localhost:${port}/${prefix}/docs`);
  });
}
bootstrap();
