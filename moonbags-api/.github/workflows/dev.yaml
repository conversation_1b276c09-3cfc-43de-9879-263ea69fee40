name: deploy-dev
on:
  push:
    branches:
      - develop

jobs:
  dockerize:
    runs-on: sota-labs-self-hosted
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: login docker registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.HARBOR_HOST }}
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}
      - name: build-push-docker-image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ vars.HARBOR_HOST }}/moonbags/moonbags-api:${{ github.sha }}
          # cache-to: "type=s3,bucket=actions-cache,region=us-east-1,endpoint_url=${{ vars.minio_endpoint }},access_key_id=${{ secrets.minio_access_key_id }},secret_access_key=${{ secrets.minio_secret_access_key }},use_path_style=true,mode=max"
          # cache-from: "type=s3,bucket=actions-cache,region=us-east-1,endpoint_url=${{ vars.minio_endpoint }},access_key_id=${{ secrets.minio_access_key_id }},secret_access_key=${{ secrets.minio_secret_access_key }},use_path_style=true,mode=max"

  deploy_argocd:
    runs-on: sota-labs-self-hosted
    env:
      ENV: ${{ contains(fromJSON('["main", "master"]'), github.ref_name) && 'prod' || 'dev' }}
    needs:
      - dockerize
    steps:
      - name: deploy k8s by argocd
        uses: sota-labs/shared-github-actions/deploy-k8s-by-argocd@v1
        with:
          image_name: sotalab-harbor.sotatek.works/moonbags/moonbags-api
          image_tag: ${{ github.sha }}
          env: ${{ env.ENV }}
          app_name: moonbags-api
          argocd_project: moonbags
          github_org: sota-labs
          gitops_repo: moonbags-k8s
          gitops_path: applications/api/overlays/${{ env.ENV }}
          argocd_host: ${{ vars.ARGOCD_HOST }}
          argocd_token: ${{ secrets.ARGOCD_TOKEN }}
          ssh_private_key: ${{ secrets.SSH_PRIVATE_TOKEN }}